<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- 页面标题 -->
    <div style="margin-bottom: 16px">
      <h3 style="margin: 0; color: #1d2129">
        Configuration inner VLAN list, QinQ IPv6 environment will send route
        announcement
      </h3>
    </div>

    <!-- 新建按钮 -->
    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-button type="primary" @click="showAddModal">
          <template #icon>
            <icon-plus />
          </template>
          Add
        </a-button>
      </a-col>
    </a-row>

    <!-- IPv6地址配置表格 -->
    <a-table
      row-key="index"
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
    >
      <template #ipv6_address="{ record }">
        <span>{{ record.ip || '-' }}</span>
      </template>

      <template #cvlan_head="{ record }">
        <span>{{ record.head || '-' }}</span>
      </template>

      <template #cvlan_tail="{ record }">
        <span>{{ record.tail || '-' }}</span>
      </template>

      <template #operation="{ record }">
        <a-space>
          <a-button type="primary" size="small" @click="editRecord(record)">
            <template #icon>
              <icon-edit />
            </template>
            Edit
          </a-button>
          <a-button
            type="primary"
            status="danger"
            size="small"
            @click="deleteRecord(record)"
          >
            <template #icon>
              <icon-delete />
            </template>
            Delete
          </a-button>
        </a-space>
      </template>
    </a-table>

    <!-- 分页信息 -->
    <div style="margin-top: 16px; color: #86909c; font-size: 14px">
      Display the {{ tableData.length }}-{{ tableData.length }} records, a total
      of {{ tableData.length }} records part
    </div>
  </a-card>

  <!-- 新建IPv6 VLAN模态框 -->
  <a-modal
    v-model:visible="isAddModalVisible"
    title="Add IPv6 VLAN"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleAddConfirm"
    @cancel="handleAddCancel"
    :width="600"
  >
    <a-form :model="addFormData" :rules="addRules" ref="addFormRef">
      <a-form-item label="IPv6 Address" field="ip">
        <a-input
          v-model="addFormData.ip"
          placeholder="请输入IPv6地址"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="CVLAN Head" field="head">
        <a-input
          v-model="addFormData.head"
          placeholder="请输入CVLAN Head"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="CVLAN Tail" field="tail">
        <a-input
          v-model="addFormData.tail"
          placeholder="请输入CVLAN Tail"
          style="width: 300px"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 编辑IPv6 VLAN模态框 -->
  <a-modal
    v-model:visible="isEditModalVisible"
    title="Edit IPv6 VLAN"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleEditConfirm"
    @cancel="handleEditCancel"
    :width="600"
  >
    <a-form :model="editFormData" :rules="editRules" ref="editFormRef">
      <a-form-item label="IPv6 Address" field="ip">
        <a-input
          v-model="editFormData.ip"
          placeholder="请输入IPv6地址"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="CVLAN Head" field="head">
        <a-input
          v-model="editFormData.head"
          placeholder="请输入CVLAN Head"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="CVLAN Tail" field="tail">
        <a-input
          v-model="editFormData.tail"
          placeholder="请输入CVLAN Tail"
          style="width: 300px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface IPv6VlanItem {
    index: number;
    ip: string;
    head: string;
    tail: string;
    isEditing?: boolean;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns = [
        {
          title: 'IPv6 Address',
          dataIndex: 'ip',
          slotName: 'ipv6_address',
          align: 'center' as const,
          width: 300,
        },
        {
          title: 'CVLAN Head',
          dataIndex: 'head',
          slotName: 'cvlan_head',
          align: 'center' as const,
          width: 150,
        },
        {
          title: 'CVLAN Tail',
          dataIndex: 'tail',
          slotName: 'cvlan_tail',
          align: 'center' as const,
          width: 150,
        },
        {
          title: 'Operate',
          dataIndex: 'operation',
          slotName: 'operation',
          align: 'center' as const,
          width: 200,
        },
      ];

      const tableData = ref<IPv6VlanItem[]>([]);
      const isLoading = ref(false);
      const isAddModalVisible = ref(false);
      const isEditModalVisible = ref(false);
      const currentEditRecord = ref<IPv6VlanItem | null>(null);

      // 新建表单数据
      const addFormData = reactive({
        ip: '',
        head: '',
        tail: '',
      });

      // 编辑表单数据
      const editFormData = reactive({
        ip: '',
        head: '',
        tail: '',
      });

      // 新建表单规则
      const addRules = {
        ip: [{ required: true, message: 'IPv6地址不能为空' }],
        head: [{ required: true, message: 'CVLAN Head不能为空' }],
        tail: [{ required: true, message: 'CVLAN Tail不能为空' }],
      };

      // 编辑表单规则
      const editRules = {
        ip: [{ required: true, message: 'IPv6地址不能为空' }],
        head: [{ required: true, message: 'CVLAN Head不能为空' }],
        tail: [{ required: true, message: 'CVLAN Tail不能为空' }],
      };

      const addFormRef = ref();
      const editFormRef = ref();

      // 获取IPv6 VLAN数据
      const fetchData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_ipv6_setting.lua',
            new URLSearchParams({ act: 'qinq_cvlan' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              tableData.value = response.data.data.map(
                (item: any, index: number) => ({
                  index: index + 1,
                  ip: item.ip || '',
                  head: item.head || '',
                  tail: item.tail || '',
                  isEditing: false,
                })
              );
            } else {
              tableData.value = [];
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        } finally {
          isLoading.value = false;
        }
      };

      // 显示新建模态框
      const showAddModal = () => {
        addFormData.ip = '';
        addFormData.head = '';
        addFormData.tail = '';
        isAddModalVisible.value = true;
      };

      // 处理新建确认
      const handleAddConfirm = async (done: any) => {
        try {
          const errors = await addFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          const response = await axios.post(
            '/lua/set_cfg_ipv6_settings.lua',
            new URLSearchParams({
              act: 'qinq_cvlan',
              act_type: 'add',
              ip: addFormData.ip,
              head: addFormData.head,
              tail: addFormData.tail,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success('添加成功');
            fetchData(); // 重新获取数据
            done(true);
          } else {
            Message.error({
              content: response.data.err || '添加失败',
              duration: 5000,
            });
            done(false);
          }
        } catch (error) {
          console.error('添加失败:', error);
          Message.error('添加请求失败');
          done(false);
        }
      };

      // 处理新建取消
      const handleAddCancel = () => {
        isAddModalVisible.value = false;
      };

      // 编辑记录
      const editRecord = (record: IPv6VlanItem) => {
        currentEditRecord.value = record;
        editFormData.ip = record.ip;
        editFormData.head = record.head;
        editFormData.tail = record.tail;
        isEditModalVisible.value = true;
      };

      // 处理编辑确认
      const handleEditConfirm = async (done: any) => {
        try {
          const response = await axios.post(
            'lua/set_cfg_ipv6_settings.lua',
            new URLSearchParams({
              act: 'qinq_cvlan',
              act_type: 'edit',
              ip: editFormData.ip,
              head: editFormData.head,
              tail: editFormData.tail,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success('编辑成功');
            fetchData(); // 重新获取数据
            done(true);
          } else {
            Message.error({
              content: response.data.err || '编辑失败',
              duration: 5000,
            });
            done(false);
          }
        } catch (error) {
          console.error('编辑失败:', error);
          Message.error('编辑请求失败');
          done(false);
        }
      };

      // 处理编辑取消
      const handleEditCancel = () => {
        isEditModalVisible.value = false;
        currentEditRecord.value = null;
      };

      // 删除记录
      const deleteRecord = async (record: IPv6VlanItem) => {
        try {
          const response = await axios.post(
            'lua/set_cfg_ipv6_settings.lua',
            new URLSearchParams({
              act: 'qinq_cvlan',
              act_type: 'delete',
              ip: record.ip,
              head: record.head,
              tail: record.tail,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success('删除成功');
            fetchData(); // 重新获取数据
          } else {
            Message.error({
              content: response.data.err || '删除失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('删除失败:', error);
          Message.error('删除请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        tableData,
        isLoading,
        isAddModalVisible,
        isEditModalVisible,
        addFormData,
        editFormData,
        addRules,
        editRules,
        addFormRef,
        editFormRef,
        showAddModal,
        handleAddConfirm,
        handleAddCancel,
        editRecord,
        handleEditConfirm,
        handleEditCancel,
        deleteRecord,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
