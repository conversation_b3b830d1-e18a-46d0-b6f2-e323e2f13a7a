<template>
  <div class="container">
    <Breadcrumb
      :items="['系统控制', '操作控制']"
    />
    <a-card title="">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-row :gutter="20" :style="{ marginBottom: '20px' }">
          <a-col :span="8">
            <a-card
              title="会话批量下线"
              :bordered="false"
              :style="{ width: '100%' }"
            >
              <br />
              <div class="session">
                <div>
                  <span>请导入文件：</span>
                  <a-tooltip
                    content="注意：文件命名不能带有（）()等特殊字符"
                    position="tr"
                    background-color="#3491FA"
                  >
                    <a-input
                      class="input-field"
                      readonly
                      :style="{ width: '100%' }"
                      :model-value="fileName"
                    />
                  </a-tooltip>
                </div>
                <br />               
                <a-upload
                  :action="'/lua/batch_disc_ses.lua'"
                  class="upload"
                  :auto-upload="false"
                  :limit="1"
                  :show-file-list="false"
                  :file-list="uploadFileList"
                  ref="fileRef"
                  @change="onFileChange"
                  @before-upload="beforeUpload"
                  @success="onUploadSuccess"
                  @error="onUploadError"
                >
                  <template #upload-button>
                    <a-space>
                      <a-button :disabled="!hasPermission('offlineImport')">选择文件</a-button>
                      <a-button type="primary" @click="submitUpload" :disabled="!(uploadFileList?.length)">
                        <template #icon>
                          <icon-upload />
                        </template>
                        上传
                      </a-button>
                    </a-space>
                  </template>
                </a-upload>
              
                <div class="form-item">
                  <a-button type="primary" @click="exampleFileOutput" :disabled="!hasPermission('offlineSampleDownload')">
                    <template #icon>
                      <icon-download />
                    </template>
                    样例下载
                  </a-button> 
                  <a-button type="primary" @click="resultFileOutput" :disabled="!hasPermission('offlineResultDownload')">
                    <template #icon>
                      <icon-download />
                    </template>
                    结果下载
                  </a-button>  
                </div>
                
                <div class="form-item">
                  <textarea 
                    rows="6" 
                    cols="50"
                  >
文件内容格式如：
1.1.1.1
2.2.2.2
0001
0002
                  </textarea>
                </div>
              </div>
            </a-card>
          </a-col>
          
          <a-col :span="8">
            <a-card
              title="回收地址"
              :bordered="false"
              :style="{ width: '100%', height: '50%' }"
            >
              
              <template #extra> </template>
              <div class="session">
                <a-button
                  id="deleteAll"
                  :disabled="!hasPermission('cleanDhcpAll')"
                  status="success"
                   @click="cleanAll()"
                >
                  回收全部
                </a-button>
                
                <a-table
                  :columns="columnsDhcp"
                  :data="dhcpdata"
                  style="margin-top: 2px"
                  :pagination="false"
                >
                  <template #ip="{ record }">
                    <span>{{ record.start }}</span>
                  </template>

                  <template #mask="{ record }">
                    <span>{{ record.end }}</span>
                  </template>

                  <template #opoperation="{ record }">
                    <a-button
                      id="userWhitelistDelete"
                      :disabled="!hasPermission('userWhitelistDelete')"
                      status="success"
                      @click="cleanOne(record.start, record.end)"
                    >
                      <template #default>回收</template>
                    </a-button>
                  </template>
                </a-table>
              </div>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card
              title="端口重启"
              :bordered="false"
              :style="{ width: '100%', height: '50%' }"
            >
              <a-table
                  :columns="columns"
                  :data="portData"
                  column-resizable
                  :bordered="{ cell: true }"
                  style="margin-top: 2px"
                  :pagination="false"
                >
                  <template #port="{ rowIndex }">
                    <span>{{ portData[rowIndex].port }}</span>
                  </template>
                  
                  <template #sw="{ rowIndex }">
                    <a-button
                        class="nav-btn"
                        type="outline"
                        :shape="'circle'"
                        @click="handleClick(rowIndex)"
                      >
                      <template #icon>
                        <IconPoweroff />
                      </template>
                    </a-button>
                    <a-modal
                      v-model:visible="visibleVM"
                      :on-before-ok="handleBeforeOk"
                      unmountOnClose
                      @cancel="handleCancel"
                    >
                      <template #title> 重启端口</template>
                      <div style="color: red">* 确认重启端口{{ currentPort }}吗？ </div>
                    </a-modal>
                  </template>
              </a-table>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import usePermission from '@/hooks/permission';
  import axios from 'axios';

  interface ApiResponse {
    code: number;
    result?: string;
    err?: string;
  }

  const { hasPermission } = usePermission();

  export default defineComponent({
    setup() {
      const fileName = ref('');
      const uploadFileList = ref<any[]>([]);
      const fileRef = ref();
      
      const submitUpload = (e) => {
        if (!hasPermission('offlineImport')) {
          Message.error({
            content: '您没有权限',
            duration: 5000,
          });
          return false;
        } 
        e.stopPropagation();
        fileRef.value.submit();
      };

      const beforeUpload = async (file: File) => {
        if (!hasPermission('offlineImport')) {
          Message.error({
            content: '您没有权限',
            duration: 5000,
          });
          return false;
        }
        return true;
      };

      const onFileChange = (fileList) => {
        uploadFileList.value = [],
        fileName.value = '';
        
        if (fileList && fileList.length > 0 && fileList[0].status === "init") {
          const file = fileList[0].file;
          fileName.value = file.name;
          uploadFileList.value = fileList;
        }
      };

      const onUploadSuccess = (response: any) => {
        if (response.data.code === 200) {
          Message.success(response.data.success);
          uploadFileList.value = [];
          fileName.value = '';
        } else {
          Message.error(response.data.err);
        }
      };
      
      const onUploadError = () => {
        uploadFileList.value = [];
        fileName.value = '';
        
        Message.error('上传失败，请稍后重试');
      };
      
      const exampleFileOutput = async () => {
        if (!hasPermission('offlineSampleDownload')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const response = await axios.post(
            '/lua/download.lua',
            new URLSearchParams({ act: 'down_batch_offline_example' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              responseType: 'blob', // 指定响应类型为blob
            }
          );
          if (response.data.code === 0) {
            Message.error(response.data.err || '下载失败'); 
          }else{
            let filename = 'batch_offline_example1.txt'; // 默认文件名
            const disposition = response.headers['content-disposition'];
            // console.log(disposition);
            if (disposition) {
              // 处理标准格式：attachment; filename="file.cfg"
              const matches = disposition.match(/filename="?([^"]+)"?/i);
              if (matches && matches[1]) {
                filename = matches[1];
              }
              // 处理UTF-8编码格式：filename*=UTF-8''%E6%96%87%E4%BB%B6.txt
              else {
                const utf8Matches = disposition.match(/filename\*=UTF-8''([^;]+)/i);
                if (utf8Matches && utf8Matches[1]) {
                  filename = decodeURIComponent(utf8Matches[1]);
                }
              }
            }

            // 创建下载链接
            const blob = new Blob([response.data]);
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            // 清理URL对象
            URL.revokeObjectURL(url);          
          }
        } catch (error) {
          Message.error('下载文件失败，请稍后重试');
        }
      };

      const resultFileOutput = async () => {
        if (!hasPermission('offlineResultDownload')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const response = await axios.post(
            '/lua/download.lua',
            new URLSearchParams({ act: 'down_batch_offline_result' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              responseType: 'blob', // 指定响应类型为blob
            }
          );
          if (response.data.code === 0) {
            Message.error(response.data.err || '下载失败'); 
          }else{
            let filename = 'batch_offline_result.txt'; // 默认文件名
            const disposition = response.headers['content-disposition'];
            // console.log(disposition);
            if (disposition) {
              // 处理标准格式：attachment; filename="file.cfg"
              const matches = disposition.match(/filename="?([^"]+)"?/i);
              if (matches && matches[1]) {
                filename = matches[1];
              }
              // 处理UTF-8编码格式：filename*=UTF-8''%E6%96%87%E4%BB%B6.txt
              else {
                const utf8Matches = disposition.match(/filename\*=UTF-8''([^;]+)/i);
                if (utf8Matches && utf8Matches[1]) {
                  filename = decodeURIComponent(utf8Matches[1]);
                }
              }
            }

            // 创建下载链接
            const blob = new Blob([response.data]);
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            // 清理URL对象
            URL.revokeObjectURL(url);          
          }
        } catch (error) {
          Message.error('下载文件失败，请稍后重试');
        }
      };

      const dhcpdata = ref<any[]>([]);
      const columnsDhcp = [
      {
        title: '地址池起始地址',
        dataIndex: 'start',        
        slotName: 'start',
      },
      {
        title: '地址池结尾地址',
        dataIndex: 'end',
        slotName: 'end',
      },
      {
        title: '操作',
        dataIndex: 'opoperation',
        slotName: 'opoperation',
      },
    ];

    const fetchDhcpData = async () => {
        try {
          const response = await axios.post(
            '/lua/system_info.lua',
            new URLSearchParams({ act: 'dhcp_pool_info' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );
          console.log(response)
          if (response.data.code === 200) {
           data.value =
              response.data.data.data.map((item: any) => ({                
                start: item[1],
                end : item[2],
              })) || [];
            dhcpdata.value = [...data.value];
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Fetch error:', error);
          Message.error('获取数据失败');
        }
    };  

    const cleanAll = async () => {
        try {
          if (!hasPermission('cleanDhcpAll')) {
            Message.error('您没有权限');
            return;
          }

          const response = await axios.post(
            '/lua/system_control.lua',
            new URLSearchParams({
              act: 'clean_dhcp_pool',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchDhcpData();
            Message.success(response.data.success);
          } else {
            Message.error(response.data.err || '操作失败');
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('操作失败');
        }
    };
    
    const cleanOne = async (start: string, end: string ) => {
      try {
        if (!hasPermission('userWhitelistDelete')) {
          Message.error('您没有权限');
          return;
        }          

        const response = await axios.post(
          '/lua/system_control.lua',
          new URLSearchParams({
            act: 'clean_dhcp_pool',
            act_type: 'one',
            head: start,
            tail: end,
          }),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        );

        if (response.data.code === 200) {
          await fetchDhcpData();
          Message.success(response.data.success);
        } else {
          Message.error(response.data.err || '操作失败');
        }
      } catch (error) {
        Message.error('操作失败');
      }
    };

      const columns = [
        {
          title: '端口',
          dataIndex: 'port',
          slotName: 'port',
        },
        {
          title: '是否重启',
          dataIndex: 'sw',
          slotName: 'sw',
        },
      ] as {
        title: string;
        dataIndex: string;        
        slotName: string,
      }[];
      const data = ref([
        {
          port: '',
          sw: 'false',
        },
      ]);

      const portData = ref([]);

      const fetchPorts = async () => {
        try {
          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({ act: 'get_vm_port' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          console.log(response);
          
          if (response.data.code === 200) {
            data.value =
              response.data.data.port.map((item: any) => ({                
                port: item,
                sw : 'false',
              })) || [];
              
            // console.log(data);
            portData.value = [...data.value];
            // console.log(portData)
          } else {
            Message.error(response.data.result || '获取端口失败');
          }
        } catch (error) {
          Message.error('获取端口错误');
        }
      };
    
      const visibleVM = ref(false);
      const currentPort = ref(null);
      const currentRowIndex = ref(null);
      const handleClick = (rowIndex) => {
        currentRowIndex.value = rowIndex;
        currentPort.value = portData.value[rowIndex].port;
        visibleVM.value = true;
      };
      const handleCancel = () => {
        visibleVM.value = false;
      };
      const handleBeforeOk = async () => {
        try {
          let act = '';
          let successMsg = '';
          act = 'restart_port';
          // console.log(currentPort.value);
                   
          if (act) {
            const response = await axios.post(
              '/lua/system_control.lua',
              new URLSearchParams({
                act: act,
                port: currentPort.value,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            );

            if (response.data.code === 200) {
              Message.success({
                content: response.data.success || '重启成功',
              });
            } else {
              Message.error(response.data.err || '操作失败');
            }
          }

          return true; // 允许对话框关闭
        } catch (error) {
          console.error('操作错误:', error);
          Message.error('请求发送失败');
          // return false; // 阻止对话框关闭
        }
      };

      onMounted(async () => {
        fetchPorts();
        fetchDhcpData();
      });
      

      return {
        exampleFileOutput,
        resultFileOutput,
        fileName,
        uploadFileList,
        onFileChange,        
        beforeUpload,
        onUploadSuccess,        
        onUploadError,
        hasPermission,
        submitUpload,
        fileRef,
        columnsDhcp,
        dhcpdata,
        cleanAll,
        cleanOne,        
        columns,
        portData,
        visibleVM,
        handleClick,
        handleCancel,
        handleBeforeOk,
        currentPort
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .hint {
    flex: 1; /* 占据剩余空间 */
    text-align: left;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }
  
  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px; /* 设置子元素之间的间距 */
    // margin-left: 25%;
    margin-top: 5%;
  }

  /* 为上传按钮添加居中样式 */
  .upload {
    display: block;
    margin: 0 auto;
  }

  /* 为按钮添加居中样式 */
  .button {
    text-align: center;
  }
  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .arco-input-wrapper {
    margin: 10px 0 !important;
  }
</style>