<!-- 状态信息组件 -->
<template>
  <div class="container">
    <div style="width: 100%; background-color: var(--color-neutral-2)">
      <Breadcrumb :items="['menu.systemstate', 'menu.status-information']" />
    </div>
    <div class="table">
      <div
        v-for="(tableData, index) in tableDataList"
        :key="index"
        :class="'table' + (index + 1)"
      >
        <a-table :columns="columns" :data="tableData" :pagination="false" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import axios from 'axios';

  interface StatusInfo {
    describe: string;
    data: string;
  }

  const columns = [
    { title: '描述', dataIndex: 'describe' },
    { title: '数据', dataIndex: 'data' },
  ];

  const tableDataList = ref<StatusInfo[][]>([]);

  const categoryMap = {
    'ICMP NAT': [0, 5],
    'UDP NAT': [5, 11],
    'TCP NAT': [11, 17],
    'Other NAT': [17, 19],
    '出入口信息': [19, 26],
    '会话信息': [26, 30],
    'NAT会话信息': [30, 34],
    '系统信息': [34, 40],
    '转发信息': [40, 44],
    '溢出信息': [44, 50],
  };

  onMounted(async () => {
    try {
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({ act: 'sys_info' }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data.code === 200) {
        const { data } = response.data.data;
        tableDataList.value = Object.values(categoryMap).map(([start, end]) =>
          data.slice(start, end)
        );
      } else {
        console.error(
          'Error fetching data:',
          response.data ? response.data.msg : 'No data received'
        );
      }
    } catch (error) {
      console.error('Error fetching status information:', error);
    }
  });
</script>

<style scoped>
  .container {
    border: 1px solid var(--color-neutral-3);
    background-color: var(--color-neutral-2);
    widows: 100%;
    padding: 20px;
    display: flex; /* 使用 Flex 布局 */
    flex-wrap: wrap; /* 允许元素换行 */
    gap: 8px; /* 表格之间的间距 */
    justify-content: space-between; /* 内容从左到右排列 */
    width: 100%; /* 父容器宽度为 100% */
  }

  /* 表格容器样式 */
  .table {
    display: flex;
    flex-wrap: wrap; /* 允许表格换行 */
    gap: 8px; /* 每个表格之间的间距 */
    background-color: var(--color-bg-2);
    border: 1px solid var(--color-neutral-3);
    padding: 5px;
  }

  /* 单个表格样式 */
  .table1,
  .table2,
  .table3,
  .table4,
  .table5,
  .table6,
  .table7,
  .table8,
  .table9,
  .table10 {
    flex: 1 1 calc(33.33% - 20px); /* 每个表格占 1/3 宽度，并减去间距 */
    background-color: rgba(255, 255, 255, 0.6); /* 背景色带透明度 */
    border-radius: 12px; /* 圆角 */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 阴影效果 */
    padding: 20px;
    overflow: auto; /* 自动处理内容超出问题 */
    table-layout: auto;
    background-color: #fff;
    /* 添加磨玻璃效果 */
    backdrop-filter: blur(10px); /* 模糊效果 */
    -webkit-backdrop-filter: blur(10px); /* Safari兼容性 */
    border: 1px solid rgba(255, 255, 255, 0.3); /* 白色半透明边框 */
  }

</style>
