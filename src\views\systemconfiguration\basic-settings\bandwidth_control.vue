<template>

  <a-card class="general-card">

    <a-divider style="margin-top: 0" />

    <a-row :gutter="20" :style="{ marginBottom: '2px' }">
      <a-col :span="7">
        <a-card title="会话初始建立速率 " :bordered="false" :style="{ width: '100%' }">
          <a-form-item field="init_uprate" class="uniform-form-item">
            <template #label>
              <span class="form-label">上行速率</span>
            </template>
            <a-tooltip content="单位：kbits/s，如果不填则缺省为0" position="tl" background-color="#3491FA">
              <a-input-number v-model="formData.init_uprate" placeholder="" />
            </a-tooltip>
          </a-form-item>

          <a-form-item field="init_downrate" class="uniform-form-item">
            <template #label>
              <span class="form-label">下行速率</span>
            </template>
            <a-tooltip content="单位：kbits/s，如果不填则缺省为0" position="tl" background-color="#3491FA">
              <a-input-number v-model="formData.init_downrate" placeholder="" />
            </a-tooltip>
          </a-form-item>
        </a-card>
      </a-col>

      <a-col :span="9">
        <a-card title="会话认证成功缺省速率（后台认证系统如果没有设置速率） " :bordered="false" :style="{ width: '100%', height: '50%' }">
          <a-form-item field="up_uprate" class="uniform-form-item">
            <template #label>
              <span class="form-label">上行速率</span>
            </template>
            <a-tooltip content="单位：kbits/s，如果不填则缺省为0" position="tl" background-color="#3491FA">
              <a-input-number v-model="formData.up_uprate" placeholder="" />
            </a-tooltip>
          </a-form-item>

          <a-form-item field="up_downrate" class="uniform-form-item">
            <template #label>
              <span class="form-label">下行速率</span>
            </template>
            <a-tooltip content="单位：kbits/s，如果不填则缺省为0" position="tl" background-color="#3491FA">
              <a-input-number v-model="formData.up_downrate" placeholder="" />
            </a-tooltip>
          </a-form-item>
        </a-card>
      </a-col>
      <a-col :span="7">
        <a-card title="内网访问速率" :bordered="false" :style="{ width: '100%', height: '50%' }">
          <a-form-item field="intra_uprate" class="uniform-form-item">
            <template #label>
              <span class="form-label">上行速率</span>
            </template>
            <a-tooltip content="单位：kbits/s，如果不填则缺省为0" position="tl" background-color="#3491FA">
              <a-input-number v-model="formData.intra_uprate" placeholder="" />
            </a-tooltip>
          </a-form-item>

          <a-form-item field="intra_downrate" class="uniform-form-item">
            <template #label>
              <span class="form-label">下行速率</span>
            </template>
            <a-tooltip content="单位：kbits/s，如果不填则缺省为0" position="tl" background-color="#3491FA">
              <a-input-number v-model="formData.intra_downrate" placeholder="" />
            </a-tooltip>
          </a-form-item>
        </a-card>
      </a-col>
    </a-row>

    <a-button :disabled="!hasPermission('bandwidthControlSubmit')" type="primary" style="margin-top: 2%"
      @click="saveAction">
      <template #icon>
        <icon-check />
      </template>
      <template #default>提交</template>
    </a-button>
  </a-card>

</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed,
    watch
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  export default defineComponent({
    props: {
      active: Boolean
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      const formData = reactive({
        init_uprate: 0,
        init_downrate: 0,
        up_uprate: 0,
        up_downrate: 0,
        intra_uprate: 0,
        intra_downrate: 0,
      });
      
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_basic_setting.lua',
            new URLSearchParams({ act: 'bandwidth_contrl' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );
          
          if (response.data.code === 200) {
            formData.init_uprate = Number(response.data.data.init_uprate) || 0;  
            formData.init_downrate = Number(response.data.data.init_downrate) || 0;
            formData.up_uprate = Number(response.data.data.up_uprate) || 0;
            formData.up_downrate = Number(response.data.data.up_downrate) || 0;
            formData.intra_uprate = Number(response.data.data.intra_uprate) || 0;
            formData.intra_downrate = Number(response.data.data.intra_downrate) || 0;
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        try {
          if (!hasPermission('bandwidthControlSubmit')) {
            Message.error('您没有权限');
            return;
          }        
          
          const response = await axios.post(
            '/lua/set_cfg_basic_setting.lua',
            new URLSearchParams({
              act: 'bandwidth_contrl',
              init_uprate: String(formData.init_uprate),
              up_uprate: String(formData.up_uprate),
              up_downrate: String(formData.up_downrate),
              init_downrate: String(formData.init_downrate),
              intra_uprate: String(formData.intra_uprate),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };
      
      watch(() => props.active, (newVal) => {
        if (newVal) {
          fetchData();
        }
      });
      
      onMounted(() => {
        if (props.active) {
          fetchData();
        }        
      });

      return {
        formData,
        saveAction,
        hasPermission,
      };
    },
  });
</script>

<style scoped>

  .general-card {
    width: 100%;
  }
  .form-label {
    display: inline-block;
    width: 90px; /* 调整这个值 */
    text-align: right;
    margin-right: 8px;
  }
</style>