<!-- 会话查询组件 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.session-list']" />
    <a-card title="">
      <div class="session">
        <span>用户类型：</span>
        <a-select :style="{width:'200px'}" v-model="formData.session_name">
          <a-option value="auth">认证用户</a-option>
          <a-option value="pass">直通用户</a-option>
          <a-option value="oelist">pppoe会话</a-option>
        </a-select>
        &nbsp;
        <span>活动状态：</span>
        <a-select :style="{width:'200px'}" v-model="formData.activeState">
          <a-option value="active">活跃</a-option>
          <a-option value="no_active">不活跃</a-option>
        </a-select>
        <a-button type="primary" style="margin-left: 10px" @click="fetchData">
          <template #icon>
            <icon-search />
          </template>
          <template #default>查询</template>
        </a-button>
        
        <div class="session2">
          <br />
          <a-table
            :columns="columns"
            :data="paginatedData"
            column-resizable
            :pagination="false"
            :bordered="{ cell: true }"
          />
          <br />
          <br />
        </div>
      </div>
      
      <a-pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="tableData.length"
        show-total
        show-size-changer
        show-jumper
        show-page-size
        style="margin-top: 20px"
      />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, computed } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import usePermission from '@/hooks/permission';

  const { hasPermission } = usePermission();
  const formData = reactive({
    session_name: '',
    activeState: '',
  });
  
  interface TableColumn {
    title: string;
    dataIndex: string;
  }

  interface TableItem {
    [key: string]: any;
  }

  const tableData = ref<TableItem[]>([]);
  const columns = ref<TableColumn[]>([]);
  
  const currentPage = ref(1);
  const pageSize = ref(10);
  const paginatedData = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    return tableData.value.slice(start, start + pageSize.value);
  });
  
  // 清空会话数据方法
  const clearSessionData = () => {
    tableData.value = [];
  };

  const fetchData = async () => {
    if (!formData.session_name) {
      Message.warning('请选择用户类型');
      return;
    }
    if (!formData.activeState) {
      Message.warning('请选择活动状态');
      return;
    }
    try {
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({
          act: 'session_list',
          session_name: formData.session_name,
          activeState: formData.activeState
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      
      console.log(response)
      if (response.data.code === 200) {
        // 1. 处理列数据
        if (Array.isArray(response.data.data?.name)) {
          columns.value = response.data.data.name.map((title: string, index: number) => ({
            title: title || `Column ${index}`,
            dataIndex: index.toString(),
          }));
        }

        // 2. 处理行数据
        if (Array.isArray(response.data.data?.data)) {
          tableData.value = response.data.data.data.map((row: any[], rowIndex: number) => {
            const rowData: TableItem = { key: rowIndex.toString() };
            
            console.log(row)
            if (typeof row === 'object' && row !== null) {
                console.log("----------");
                Object.entries(row).forEach(([key, value]) => {
                    console.log(key, value);
                    rowData[key] = value ?? '-';
                });
            }
            
            return rowData;
          });
        }

        console.log('Processed Columns:', columns.value);
        console.log('Processed Table Data:', tableData.value);
      } else {
        // 清空会话数据
        clearSessionData();
        Message.error(response.data.err || '查询失败');
      }
    } catch (error) {
      console.error('获取数据时出错:', error);
      clearSessionData();
      Message.error('查询会话请求失败');
    }
  };

</script>

<style scoped>
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .session,
  .session2 {
    text-align: left;
    background-color: #fff;
    padding: 5px;
    white-space: nowrap;
  }

  .session {
    margin-bottom: 20px;
  }
  span {
    font-size: 20px;
  }
</style>
