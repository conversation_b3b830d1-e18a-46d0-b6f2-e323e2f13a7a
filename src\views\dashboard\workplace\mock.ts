// import Mock from 'mockjs';
// import qs from 'query-string';
// import dayjs from 'dayjs';
// import { GetParams } from '@/types/global';
// import setupMock, { successResponseWrap } from '@/utils/setup-mock';

// const sessionList = [
//   { describe: 'IP 会话数目', data: '0' },
//   { describe: 'NAT 会话数目', data: '0' },
//   { describe: 'NAT-FRAG 会话数目', data: '0' },
// ];
// const NATList = [
//   { describe: 'CPU 空载运行', data: '10529742' },
//   { describe: 'NAT 会话每秒创建数目', data: '0' },
//   { describe: 'NAT 会话每秒释放数目', data: '0' },
//   { describe: 'NAT-FRAG 会话每秒创建数目', data: '0' },
//   { describe: 'NAT-FRAG 会话每秒释放数目', data: '0' },
// ];

// const TCPList = [
//   { describe: 'TCP NAT输出创建失败数目', data: '0' },
//   { describe: 'TCP NAT输入查找失败数目', data: '0' },
//   { describe: 'TCP NAT-FRAG创建失败数目', data: '0|0' },
//   { describe: 'TCP NAT-FRAG输入查找失败数目', data: '0' },
//   { describe: 'TCP NAT-FRAG输出查找失败数目', data: '0' },
//   { describe: 'TCP RDR NAT创建失败数目', data: '0' },
// ];

// const UDPList = [
//   { describe: 'UDP NAT输出创建失败数目', data: '0' },
//   { describe: 'UDP NAT输入查找失败数目', data: '0' },
//   { describe: 'UDP NAT-FRAG创建失败数目', data: '0|0' },
//   { describe: 'UDP NAT-FRAG输入查找失败数目', data: '0' },
//   { describe: 'UDP NAT-FRAG输出查找失败数目', data: '0' },
//   { describe: 'UDP RDR NAT创建失败数目', data: '0' },
// ];
// const ICMPList = [
//   { describe: 'ICMP NAT输出创建失败数目', data: '0' },
//   { describe: 'ICMP NAT输入查找失败数目', data: '0' },
//   { describe: 'ICMP NAT-FRAG创建失败数目', data: '0|0' },
//   { describe: 'ICMP NAT-FRAG输入查找失败数目', data: '0' },
//   { describe: 'ICMP NAT-FRAG输出查找失败数目', data: '0' },
// ];
// const EXITList = [
//   { describe: '入口接收速率(Kbits/s)', data: '0' },
//   { describe: '入口转送速率(Kbits/s)', data: '0' },
//   { describe: '出口接收速率(Kbits/s)', data: '0' },
//   { describe: '出口转送速率(Kbits/s)', data: '0' },
//   { describe: '入口自身MAC地址', data: 'B0:51:8E:09:95:9E' },
//   { describe: '入口网关MAC地址', data: '00:00:00:00:00:00' },
//   { describe: '入口网关MAC过期时间(秒)', data: '0' },
// ];
// const OTHERList = [
//   { describe: 'Other NAT输入查找失败数目', data: '0' },
//   { describe: 'Other NAT输出创建失败数目', data: '0' },
// ];
// const PUTList = [
//   { describe: 'Input 转发数据包数目', data: '0' },
//   { describe: 'Output 转发数据包数目', data: '0' },
//   { describe: 'Input 丢弃不支持ICMP数目', data: '0' },
//   { describe: 'Output 丢弃不支持ICMP数目', data: '0' },
// ];

// const systemList = [
//   {
//     key: 1,
//     clickNumber: '346.3w+',
//     describe: '软件版本',
//     increases: 35,
//     data: '3.1 Rel Jun 3 2024',
//   },
//   {
//     key: 2,
//     clickNumber: '324.2w+',
//     describe: '版本授权时间',
//     increases: 22,
//     data: '20250101',
//   },
//   {
//     key: 3,
//     clickNumber: '318.9w+',
//     describe: '系统时间',
//     increases: 9,
//     data: '20241203 17:39:24',
//   },
//   {
//     key: 4,
//     clickNumber: '257.9w+',
//     describe: '授权并发用户',
//     increases: 17,
//     data: '5000',
//   },
//   {
//     key: 5,
//     clickNumber: '124.2w+',
//     describe: '运行时间',
//     increases: 37,
//     data: '0 Day 23:48:00',
//   },
//   {
//     key: 6,
//     clickNumber: '124.2w+',
//     describe: '发送ARP 请求数',
//     increases: 37,
//     data: '10',
//   },
//   {
//     key: 7,
//     clickNumber: '124.2w+',
//     describe: '发送ARP 回应数',
//     increases: 37,
//     data: '0',
//   },
// ];

// const imageList = [
//   {
//     key: 1,
//     clickNumber: '15.3w+',
//     increases: 15,
//   },
//   {
//     key: 2,
//     clickNumber: '12.2w+',
//     increases: 26,
//   },
//   {
//     key: 3,
//     clickNumber: '18.9w+',
//     increases: 9,
//   },
//   {
//     key: 4,
//     clickNumber: '7.9w+',
//     increases: 0,
//   },
//   {
//     key: 5,
//     clickNumber: '5.2w+',
//     describe: '派出所副所长威胁市民？警方调…',
//     increases: 4,
//   },
// ];
// const videoList = [
//   {
//     key: 1,
//     clickNumber: '367.6w+',
//     describe: '这是今日10点的南京',
//     increases: 5,
//   },
//   {
//     key: 2,
//     clickNumber: '352.2w+',
//     describe: '立陶宛不断挑衅致经济受损民众…',
//     increases: 17,
//   },
//   {
//     key: 3,
//     clickNumber: '348.9w+',
//     describe: '韩国艺人刘在石确诊新冠',
//     increases: 30,
//   },
//   {
//     key: 4,
//     clickNumber: '346.3w+',
//     describe: '关于北京冬奥会，文在寅表态',
//     increases: 12,
//   },
//   {
//     key: 5,
//     clickNumber: '271.2w+',
//     describe: '95后现役军人荣立一等功',
//     increases: 2,
//   },
// ];

// setupMock({
//   setup() {
//     Mock.mock(new RegExp('/api/content-data'), () => {
//       const presetData = [58, 81, 53, 90, 64, 88, 49, 79];
//       const getLineData = () => {
//         const count = 8;
//         return new Array(count).fill(0).map((el, idx) => ({
//           x: dayjs()
//             .day(idx - 2)
//             .format('YYYY-MM-DD'),
//           y: presetData[idx],
//         }));
//       };
//       return successResponseWrap([...getLineData()]);
//     });
//     Mock.mock(new RegExp('/api/popular/list'), (params: GetParams) => {
//       const { type = 'text' } = qs.parseUrl(params.url).query;
//       if (type === 'session') {
//         return successResponseWrap([...sessionList]);
//       }
//       if (type === 'video') {
//         return successResponseWrap([...imageList]);
//       }
//       if (type === 'system') {
//         return successResponseWrap([...systemList]);
//       }
//       if (type === 'NAT') {
//         return successResponseWrap([...NATList]);
//       }
//       if (type === 'EXIT') {
//         return successResponseWrap([...EXITList]);
//       }
//       if (type === 'TCP') {
//         return successResponseWrap([...TCPList]);
//       }
//       if (type === 'UDP') {
//         return successResponseWrap([...UDPList]);
//       }
//       if (type === 'ICMP') {
//         return successResponseWrap([...ICMPList]);
//       }
//       if (type === 'OTHER') {
//         return successResponseWrap([...OTHERList]);
//       }
//       if (type === 'PUT') {
//         return successResponseWrap([...PUTList]);
//       }

//       return successResponseWrap([...sessionList]);
//     });
//   },
// });
