<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- DHCP Option 配置表格 -->
    <a-table
      row-key="index"
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
    >
      <template #option_type="{ record }">
        <a-select
          v-model="record.option_type"
          :style="{ width: '120px' }"
          placeholder="请选择"
          :disabled="record.isSubmitted"
        >
          <a-option value="string">字符串</a-option>
          <a-option value="hex">十六进制</a-option>
          <a-option value="ip">IP地址</a-option>
          <a-option value="number">数字</a-option>
        </a-select>
      </template>

      <template #option_id="{ record }">
        <a-input
          v-model="record.id"
          placeholder="请输入Option ID"
          :disabled="record.isSubmitted"
          :style="{ width: '150px' }"
        />
      </template>

      <template #option_value="{ record }">
        <a-input
          v-model="record.value"
          placeholder="请输入Option值"
          :disabled="record.isSubmitted"
          :style="{ width: '200px' }"
        />
      </template>

      <template #operation="{ record }">
        <a-button
          v-if="!record.isSubmitted"
          type="primary"
          size="small"
          @click="submitOption(record)"
        >
          <template #icon>
            <icon-check />
          </template>
          提交
        </a-button>

        <a-button
          v-else
          type="primary"
          status="danger"
          size="small"
          @click="deleteOption(record)"
        >
          <template #icon>
            <icon-delete />
          </template>
          删除
        </a-button>
      </template>
    </a-table>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface DhcpOptionItem {
    index: number;
    id: string;
    option_type: string;
    value: string;
    isSubmitted: boolean;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: 'option类型',
          dataIndex: 'option_type',
          slotName: 'option_type',
          align: 'center',
          width: 150,
        },
        {
          title: 'option ID',
          dataIndex: 'id',
          slotName: 'option_id',
          align: 'center',
          width: 180,
        },
        {
          title: 'option值',
          dataIndex: 'value',
          slotName: 'option_value',
          align: 'center',
          width: 250,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          slotName: 'operation',
          align: 'center',
          width: 120,
        },
      ];

      const isLoading = ref(false);

      // 初始化8行固定数据
      const tableData = ref<DhcpOptionItem[]>([]);

      // 已提交的数据列表
      const submittedData = ref<DhcpOptionItem[]>([]);

      // 初始化表格数据
      const initTableData = () => {
        tableData.value = Array.from({ length: 8 }, (_, index) => ({
          index: index + 1,
          id: '',
          option_type: '',
          value: '',
          isSubmitted: false,
        }));
      };

      // 获取数据
      const fetchData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'option' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 重新初始化表格数据
            initTableData();

            if (response.data.data && Array.isArray(response.data.data)) {
              // 将已提交的数据标记为已提交状态
              submittedData.value = response.data.data;

              response.data.data.forEach((item: any, index: number) => {
                if (index < 8) {
                  tableData.value[index] = {
                    index: index + 1,
                    id: item.id || '',
                    option_type: item.option_type || '',
                    value: item.value || '',
                    isSubmitted: true,
                  };
                }
              });
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        } finally {
          isLoading.value = false;
        }
      };

      // 提交Option
      const submitOption = async (record: DhcpOptionItem) => {
        // 检查是否已达到最大提交数量（7行）
        const submittedCount = submittedData.value.length;
        if (submittedCount >= 7) {
          Message.error('over dhcp option num');
          return;
        }

        // 检查必填字段
        if (!record.option_type || !record.id || !record.value) {
          Message.error('请填写完整的Option信息');
          return;
        }

        try {
          const response = await axios.post(
            'lua/set_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'dhcp_option',
              act_type: 'add',
              id: record.id,
              option_type: record.option_type,
              value: record.value,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            record.isSubmitted = true;
            submittedData.value.push({ ...record });
            Message.success('提交成功');
          } else {
            Message.error({
              content: response.data.err || '提交失败',
              duration: 5000,
            });
          }

          record.isSubmitted = true;
          submittedData.value.push({ ...record });
          Message.success('提交成功（模拟）');
        } catch (error) {
          console.error('提交失败:', error);
          Message.error('提交请求失败');
        }
      };

      // 删除Option
      const deleteOption = async (record: DhcpOptionItem) => {
        try {
          // TODO: 等待后端接口完成后，取消注释以下代码
          const response = await axios.post(
            'lua/set_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'dhcp_option',
              act_type: 'del',
              id: record.id,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 重置该行数据
            record.id = '';
            record.option_type = '';
            record.value = '';
            record.isSubmitted = false;

            // 从已提交列表中移除
            const index = submittedData.value.findIndex(
              (item) =>
                item.id === record.id && item.option_type === record.option_type
            );
            if (index > -1) {
              submittedData.value.splice(index, 1);
            }

            Message.success('删除成功');
          } else {
            Message.error({
              content: response.data.err || '删除失败',
              duration: 5000,
            });
          }

          // 暂时的模拟删除逻辑
          const originalId = record.id;
          const originalType = record.option_type;

          // 重置该行数据
          record.id = '';
          record.option_type = '';
          record.value = '';
          record.isSubmitted = false;

          // 从已提交列表中移除
          const index = submittedData.value.findIndex(
            (item) =>
              item.id === originalId && item.option_type === originalType
          );
          if (index > -1) {
            submittedData.value.splice(index, 1);
          }

          Message.success('删除成功（模拟）');
        } catch (error) {
          console.error('删除失败:', error);
          Message.error('删除请求失败');
        }
      };

      // 初始化数据
      onMounted(() => {
        initTableData();
        if (props.active) {
          fetchData();
        }
      });

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      return {
        columns,
        tableData,
        isLoading,
        submitOption,
        deleteOption,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  :deep(.arco-table-th) {
    text-align: center;
    background-color: #f7f8fa;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }

  :deep(.arco-table-td .arco-select) {
    width: 100%;
  }

  :deep(.arco-table-td .arco-input) {
    text-align: center;
  }

  :deep(.arco-btn-status-danger) {
    background-color: #f53f3f;
    border-color: #f53f3f;
  }

  :deep(.arco-btn-status-danger:hover) {
    background-color: #e03e3e;
    border-color: #e03e3e;
  }
</style>
