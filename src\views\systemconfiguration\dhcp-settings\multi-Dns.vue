<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- 配置提示 -->
    <a-alert
      type="info"
      style="margin-bottom: 16px"
      message="Configuration 0.0.0.0 to delete the configuration"
      show-icon
    />

    <!-- 多DNS配置表格 -->
    <a-table
      row-key="index"
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
    >
      <template #start_address="{ record }">
        <a-input
          v-model="record.ip"
          placeholder="***********"
          :disabled="record.isSubmitted"
          :style="{ width: '150px' }"
        />
      </template>

      <template #end_address="{ record }">
        <a-input
          v-model="record.mask"
          placeholder="************"
          :disabled="record.isSubmitted"
          :style="{ width: '150px' }"
        />
      </template>

      <template #dns3="{ record }">
        <a-input
          v-model="record.dns3"
          placeholder=""
          :disabled="record.isSubmitted"
          :style="{ width: '150px' }"
        />
      </template>

      <template #dns4="{ record }">
        <a-input
          v-model="record.dns4"
          placeholder=""
          :disabled="record.isSubmitted"
          :style="{ width: '150px' }"
        />
      </template>

      <template #operation="{ record }">
        <a-button
          v-if="!record.isSubmitted"
          type="primary"
          size="small"
          @click="submitDnsConfig(record)"
        >
          <template #icon>
            <icon-check />
          </template>
          Submit
        </a-button>

        <a-button
          v-else
          type="primary"
          status="danger"
          size="small"
          @click="deleteDnsConfig(record)"
        >
          <template #icon>
            <icon-delete />
          </template>
          删除
        </a-button>
      </template>
    </a-table>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, watch } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface DnsConfigItem {
    index: number;
    ip: string;
    mask: string;
    dns3: string;
    dns4: string;
    desc: string;
    isSubmitted: boolean;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: 'Start Address',
          dataIndex: 'ip',
          slotName: 'start_address',
          align: 'center',
          width: 180,
        },
        {
          title: 'End Address',
          dataIndex: 'mask',
          slotName: 'end_address',
          align: 'center',
          width: 180,
        },
        {
          title: 'DNS3',
          dataIndex: 'dns3',
          slotName: 'dns3',
          align: 'center',
          width: 180,
        },
        {
          title: 'DNS4',
          dataIndex: 'dns4',
          slotName: 'dns4',
          align: 'center',
          width: 180,
        },
        {
          title: 'Operate',
          dataIndex: 'operation',
          slotName: 'operation',
          align: 'center',
          width: 120,
        },
      ];

      const isLoading = ref(false);

      // DNS配置数据
      const tableData = ref<DnsConfigItem[]>([]);

      // 已提交的数据列表
      const submittedData = ref<DnsConfigItem[]>([]);

      // 初始化表格数据
      const initTableData = () => {
        tableData.value = Array.from({ length: 3 }, (_, index) => ({
          index: index + 1,
          ip: '',
          mask: '',
          dns3: '',
          dns4: '',
          desc: '',
          isSubmitted: false,
        }));
      };

      // 获取DNS配置数据
      const fetchData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'multi_dns' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 重新初始化表格数据
            initTableData();

            if (response.data.data && Array.isArray(response.data.data)) {
              // 将已提交的数据标记为已提交状态
              submittedData.value = response.data.data;

              response.data.data.forEach((item: any, index: number) => {
                if (index < 3) {
                  tableData.value[index] = {
                    index: index + 1,
                    ip: item.ip || '',
                    mask: item.mask || '',
                    dns3: item.dns3 || '',
                    dns4: item.dns4 || '',
                    desc: item.desc || '',
                    isSubmitted: true,
                  };
                }
              });
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        } finally {
          isLoading.value = false;
        }
      };

      // 提交DNS配置
      const submitDnsConfig = async (record: DnsConfigItem) => {
        // 检查是否已达到最大提交数量（3行）
        const submittedCount = submittedData.value.length;
        if (submittedCount >= 3) {
          Message.error('最多只能配置3个DNS');
          return;
        }

        // 检查必填字段
        if (!record.ip || !record.mask) {
          Message.error('请填写开始地址和结束地址');
          return;
        }

        try {
          const response = await axios.post(
            'lua/set_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'multi_dns',
              act_type: 'add',
              ip: record.ip,
              mask: record.mask,
              dns3: record.dns3 || '',
              dns4: record.dns4 || '',
              desc: record.desc || '',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            record.isSubmitted = true;
            submittedData.value.push({ ...record });
            Message.success('提交成功');
          } else {
            Message.error({
              content: response.data.err || '提交失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('提交失败:', error);
          Message.error('提交请求失败');
        }
      };

      // 删除DNS配置
      const deleteDnsConfig = async (record: DnsConfigItem) => {
        try {
          const response = await axios.post(
            'lua/set_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'multi_dns',
              act_type: 'del',
              ip: record.ip,
              mask: record.mask,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 重置该行数据
            record.ip = '';
            record.mask = '';
            record.dns3 = '';
            record.dns4 = '';
            record.desc = '';
            record.isSubmitted = false;

            // 从已提交列表中移除
            const index = submittedData.value.findIndex(
              (item) => item.ip === record.ip && item.mask === record.mask
            );
            if (index > -1) {
              submittedData.value.splice(index, 1);
            }

            Message.success('删除成功');
          } else {
            Message.error({
              content: response.data.err || '删除失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('删除失败:', error);
          Message.error('删除请求失败');
        }
      };

      // 初始化数据
      onMounted(() => {
        initTableData();
        if (props.active) {
          fetchData();
        }
      });

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      return {
        columns,
        tableData,
        isLoading,
        submitDnsConfig,
        deleteDnsConfig,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  :deep(.arco-table-th) {
    text-align: center;
    background-color: #f7f8fa;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }

  :deep(.arco-table-td .arco-select) {
    width: 100%;
  }

  :deep(.arco-table-td .arco-input) {
    text-align: center;
  }

  :deep(.arco-btn-status-danger) {
    background-color: #f53f3f;
    border-color: #f53f3f;
  }

  :deep(.arco-btn-status-danger:hover) {
    background-color: #e03e3e;
    border-color: #e03e3e;
  }
</style>
