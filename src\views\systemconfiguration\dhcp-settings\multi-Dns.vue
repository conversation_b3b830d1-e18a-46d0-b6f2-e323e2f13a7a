<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- 配置提示 -->
    <a-alert type="warning" style="margin-bottom: 16px" show-icon>
      配置0.0.0.0 删除配置
    </a-alert>

    <!-- 多DNS配置表格 -->
    <a-table
      row-key="index"
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
    >
      <template #start_address="{ record }">
        <span>{{ record.ip || '-' }}</span>
      </template>

      <template #end_address="{ record }">
        <span>{{ record.mask || '-' }}</span>
      </template>

      <template #dns3="{ record }">
        <a-input
          v-model="record.dns3"
          placeholder=""
          :disabled="record.isSubmitted"
          :style="{ width: '150px' }"
        />
      </template>

      <template #dns4="{ record }">
        <a-input
          v-model="record.dns4"
          placeholder=""
          :disabled="record.isSubmitted"
          :style="{ width: '150px' }"
        />
      </template>

      <template #operation="{ record }">
        <a-button type="primary" size="small" @click="submitDnsConfig(record)">
          <template #icon>
            <icon-check />
          </template>
          提交
        </a-button>
      </template>
    </a-table>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, watch } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface DnsConfigItem {
    index: number;
    ip: string;
    mask: string;
    dns3: string;
    dns4: string;
    desc: string;
    isSubmitted: boolean;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: 'Start Address',
          dataIndex: 'ip',
          slotName: 'start_address',
          align: 'center',
          width: 180,
        },
        {
          title: 'End Address',
          dataIndex: 'mask',
          slotName: 'end_address',
          align: 'center',
          width: 180,
        },
        {
          title: 'DNS3',
          dataIndex: 'dns3',
          slotName: 'dns3',
          align: 'center',
          width: 180,
        },
        {
          title: 'DNS4',
          dataIndex: 'dns4',
          slotName: 'dns4',
          align: 'center',
          width: 180,
        },
        {
          title: 'Operate',
          dataIndex: 'operation',
          slotName: 'operation',
          align: 'center',
          width: 120,
        },
      ];

      const isLoading = ref(false);

      // DNS配置数据
      const tableData = ref<DnsConfigItem[]>([]);

      // 已提交的数据列表
      const submittedData = ref<DnsConfigItem[]>([]);

      // 初始化表格数据（根据接口数据动态生成）
      const initTableData = (data: any[] = []) => {
        if (data.length === 0) {
          // 如果没有数据，显示空状态
          tableData.value = [];
        } else {
          // 根据接口数据生成表格行
          tableData.value = data.map((item: any, index: number) => ({
            index: index + 1,
            ip: item.ip || '',
            mask: item.mask || '',
            dns3: item.dns3 || '',
            dns4: item.dns4 || '',
            desc: item.desc || '',
            isSubmitted: false,
          }));
        }
      };

      // 获取DNS配置数据
      const fetchData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'multi_dns' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              // 根据接口数据初始化表格
              initTableData(response.data.data);
              submittedData.value = response.data.data;
            } else {
              // 没有数据时显示空表格
              initTableData([]);
              submittedData.value = [];
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        } finally {
          isLoading.value = false;
        }
      };

      // 提交DNS配置
      const submitDnsConfig = async (record: DnsConfigItem) => {
        try {
          const response = await axios.post(
            'lua/set_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'multi_dns',
              act_type: 'set',
              ip: record.ip,
              mask: record.mask,
              dns3: record.dns3 || '',
              dns4: record.dns4 || '',
              desc: record.desc || '',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success('提交成功');
            // 重新获取数据以更新显示
            fetchData();
          } else {
            Message.error({
              content: response.data.err || '提交失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('提交失败:', error);
          Message.error('提交请求失败');
        }
      };

      // 初始化数据
      onMounted(() => {
        if (props.active) {
          fetchData();
        } else {
          initTableData([]);
        }
      });

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      return {
        columns,
        tableData,
        isLoading,
        submitDnsConfig,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  :deep(.arco-table-th) {
    text-align: center;
    background-color: #f7f8fa;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }

  :deep(.arco-table-td .arco-select) {
    width: 100%;
  }

  :deep(.arco-table-td .arco-input) {
    text-align: center;
  }

  :deep(.arco-btn-status-danger) {
    background-color: #f53f3f;
    border-color: #f53f3f;
  }

  :deep(.arco-btn-status-danger:hover) {
    background-color: #e03e3e;
    border-color: #e03e3e;
  }
</style>
