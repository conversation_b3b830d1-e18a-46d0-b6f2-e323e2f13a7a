<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.port']" />
    <a-card title="">
      <a-table
        :columns="columns"
        :data="paginatedData"
        :bordered="true"
        :pagination="false"
      />

      <a-pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="tableData.length"
        show-total
        show-size-changer
        show-jumper
        show-page-size
        style="margin-top: 20px"
      />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import axios from 'axios';
  import { values } from 'lodash';

  interface TableColumn {
    title: string;
    dataIndex: string;
  }

  interface TableItem {
    [key: string]: any;
  }

  const tableData = ref<TableItem[]>([]);
  const columns = ref<TableColumn[]>([]);

  const currentPage = ref(1);
  const pageSize = ref(10);
  const paginatedData = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    return tableData.value.slice(start, start + pageSize.value);
  });

  onMounted(async () => {
    try {
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({ act: 'dhcp_pool_info' }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      // console.log(response);
      if (response.data?.code === 200) {
        // 1. 处理列数据
        if (Array.isArray(response.data.data?.name)) {
          columns.value = response.data.data.name.map(
            (title: string, index: number) => ({
              title: title || `Column ${index}`,
              dataIndex: index.toString(),
            })
          );
        }

        // 2. 处理行数据
        if (Array.isArray(response.data.data?.data)) {
          tableData.value = response.data.data.data.map(
            (row: any[], rowIndex: number) => {
              const rowData: TableItem = { key: rowIndex.toString() };

              if (Array.isArray(row)) {
                row.forEach((value, colIndex) => {
                  rowData[colIndex.toString()] = value ?? '-';
                });
              }

              return rowData;
            }
          );
        }

        // console.log('Processed Columns:', columns.value);
        // console.log('Processed Table Data:', tableData.value);
      } else {
        console.error('API Error:', response.data?.err || 'No data returned');
      }
    } catch (error) {
      console.error('API error:', error);
    }
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .a-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
</style>
