#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大麦网快速抢票脚本 - 极速版
专注于抢票速度，去除复杂功能
"""

import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

class QuickGrabber:
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def setup_driver(self):
        """快速设置浏览器"""
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-images')
        options.add_argument('--disable-javascript')  # 禁用JS加速
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = webdriver.Chrome(options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.wait = WebDriverWait(self.driver, 3)
        
    def quick_login(self, username, password):
        """快速登录"""
        logging.info("快速登录中...")
        self.driver.get("https://passport.damai.cn/login")
        
        # 密码登录
        try:
            pwd_tab = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '密码登录')]")))
            pwd_tab.click()
        except:
            pass
            
        # 输入凭据
        user_input = self.wait.until(EC.presence_of_element_located((By.ID, "fm-login-id")))
        user_input.send_keys(username)
        
        pwd_input = self.driver.find_element(By.ID, "fm-login-password")
        pwd_input.send_keys(password)
        
        # 登录
        login_btn = self.driver.find_element(By.XPATH, "//button[contains(text(), '登录')]")
        login_btn.click()
        
        time.sleep(3)
        return "damai.cn" in self.driver.current_url
        
    def grab_ticket(self, url, price="", num=1):
        """极速抢票"""
        logging.info("开始抢票...")
        
        for attempt in range(200):  # 最多尝试200次
            try:
                # 刷新页面
                self.driver.get(url)
                time.sleep(0.5)
                
                # 选择价位
                if price:
                    try:
                        price_elem = self.driver.find_element(By.XPATH, f"//div[contains(text(), '{price}')]")
                        self.driver.execute_script("arguments[0].click();", price_elem)
                    except:
                        pass
                
                # 增加票数
                for _ in range(num - 1):
                    try:
                        plus_btn = self.driver.find_element(By.XPATH, "//span[contains(@class, 'plus')]")
                        plus_btn.click()
                        time.sleep(0.1)
                    except:
                        break
                
                # 立即购买
                buy_selectors = [
                    "//button[contains(text(), '立即购买')]",
                    "//button[contains(text(), '立即预订')]",
                    "//a[contains(text(), '立即购买')]"
                ]
                
                for selector in buy_selectors:
                    try:
                        buy_btn = self.driver.find_element(By.XPATH, selector)
                        if buy_btn.is_enabled():
                            self.driver.execute_script("arguments[0].click();", buy_btn)
                            time.sleep(1)
                            break
                    except:
                        continue
                
                # 选择观演人（第一个）
                try:
                    viewer = self.driver.find_element(By.XPATH, "//input[@type='checkbox'][1]")
                    if not viewer.is_selected():
                        self.driver.execute_script("arguments[0].click();", viewer)
                        time.sleep(0.2)
                except:
                    pass
                
                # 提交订单
                submit_selectors = [
                    "//button[contains(text(), '确认订单')]",
                    "//button[contains(text(), '提交订单')]"
                ]
                
                for selector in submit_selectors:
                    try:
                        submit_btn = self.driver.find_element(By.XPATH, selector)
                        if submit_btn.is_enabled():
                            self.driver.execute_script("arguments[0].click();", submit_btn)
                            time.sleep(1)
                            break
                    except:
                        continue
                
                # 检查结果
                if "trade.damai.cn" in self.driver.current_url:
                    logging.info(f"🎉 抢票成功！第 {attempt + 1} 次尝试")
                    return True
                
                # 检查售罄
                if any(word in self.driver.page_source for word in ["售罄", "暂无票", "已售完"]):
                    logging.warning("票已售罄")
                    return False
                
                logging.info(f"第 {attempt + 1} 次尝试失败，继续...")
                time.sleep(random.uniform(0.3, 0.8))
                
            except Exception as e:
                logging.error(f"第 {attempt + 1} 次尝试异常: {e}")
                continue
        
        return False
    
    def run(self, config):
        """运行抢票"""
        try:
            self.setup_driver()
            
            # 登录
            if not self.quick_login(config['username'], config['password']):
                logging.error("登录失败")
                return False
            
            # 等待开抢时间
            if config.get('start_time'):
                target_time = datetime.strptime(config['start_time'], "%Y-%m-%d %H:%M:%S")
                wait_seconds = (target_time - datetime.now()).total_seconds()
                if wait_seconds > 0:
                    logging.info(f"等待开抢，剩余 {wait_seconds:.1f} 秒")
                    time.sleep(max(0, wait_seconds - 0.5))  # 提前0.5秒
            
            # 开始抢票
            success = self.grab_ticket(
                config['target_url'],
                config.get('target_price', ''),
                config.get('ticket_num', 1)
            )
            
            if success:
                logging.info("🎉 抢票成功！请尽快支付！")
                input("按回车键关闭浏览器...")
            else:
                logging.info("抢票失败")
            
            return success
            
        except Exception as e:
            logging.error(f"抢票过程异常: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

def main():
    """主函数"""
    print("🚀 大麦网极速抢票脚本")
    print("=" * 30)
    
    # 快速配置
    config = {
        'username': input("请输入大麦网用户名: ").strip(),
        'password': input("请输入密码: ").strip(),
        'target_url': input("请输入演出链接: ").strip(),
        'target_price': input("请输入目标价位(可选): ").strip(),
        'ticket_num': int(input("请输入票数(默认1): ").strip() or "1"),
        'start_time': input("请输入开抢时间(格式: 2024-01-15 10:00:00, 可选): ").strip()
    }
    
    if not all([config['username'], config['password'], config['target_url']]):
        print("❌ 必填信息不完整")
        return
    
    print("\n开始抢票...")
    grabber = QuickGrabber()
    grabber.run(config)

if __name__ == "__main__":
    main()
