<template>
  <div class="container">
    <Breadcrumb
      :items="['menu.Virtual_Machine_Settings', 'menu.basic_settings']"
    />

    <a-card title="网络端口">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-row :gutter="20" :style="{ marginBottom: '20px' }">
          <a-col :span="8">
            <a-card
              title="网络端口"
              :bordered="false"
              :style="{ width: '100%', height: '500px' }"
            >
              <template #extra> </template>
              <div class="session">
                <a-alert type="info" banner closable>
                  <template #icon> </template>
                  <template #title>
                    <span style="font-size: 15px"
                      >注意：配置后需要重新启动虚机才能生效</span
                    >
                  </template>
                  <template #extra> </template>
                </a-alert>
                <a-table
                  :columns="columns"
                  :data="data"
                  column-resizable
                  :bordered="{ cell: true }"
                  style="margin-top: 20px"
                  :pagination="false"
                >
                  <template #choosecard="{ record }">
                    <a-checkbox
                      v-model="record.checked"
                      @change="handleCheckboxChange(record)"
                    ></a-checkbox>
                  </template>
                </a-table>
                <div class="button">
                  <a-button
                    id="networkPortSubmit"
                    :disabled="!hasPermission('networkPortSubmit')"
                    type="primary"
                    @click="handleNetworkPortsSubmit"
                  >
                    <template #icon>
                      <icon-check />
                    </template>
                    <template #default>设置</template>
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card
              title="CPU资源"
              :bordered="false"
              :style="{ width: '100%', height: '370px' }"
            >
              <template #extra> </template>
              <div class="session">
                <a-alert type="info" banner closable>
                  <template #icon> </template>
                  <template #title>
                    <span style="font-size: 15px"
                      >注意：配置后需要重新启动虚机才能生效</span
                    >
                  </template>
                  <template #extra> </template>
                </a-alert>
                <br />
                <div class="form-item">
                  <span>{{ cpuFormData.socket }}</span>
                  <a-tooltip
                    content="注意：请输入一个数值（16进制），例如：0x0f"
                    position="tr"
                    background-color="#3491FA"
                  >
                  </a-tooltip>
                </div>
                <div class="form-item">
                  <span>{{ cpuFormData.core }}</span>
                </div>
                <div class="form-item">
                  <span>选择CPU：</span>
                  <a-tooltip
                    content="注意：请输入一个数值（16进制），例如：0x0f"
                    position="tr"
                    background-color="#3491FA"
                  >
                    <a-input v-model="cpuFormData.cpu" class="input-field"
                  /></a-tooltip>
                </div>
                <div class="button">
                  <a-button
                    id="cpuResourcesSubmit"
                    :disabled="!hasPermission('cpuResourcesSubmit')"
                    type="primary"
                    @click="handleCpuResourceSubmit"
                  >
                    <template #icon>
                      <icon-check />
                    </template>
                    <template #default>设置</template>
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card
              title="内存设置"
              :bordered="false"
              :style="{ width: '100%', height: '370px' }"
            >
              <template #extra> </template>
              <a-alert type="info" banner closable>
                <template #icon> </template>
                <template #title>
                  <span style="font-size: 15px"
                    >注意：配置后需要重新启动虚机才能生效</span
                  >
                </template>
                <template #extra> </template>
              </a-alert>
              <br />
              <div class="session">
                <div class="form-item">
                  <span>内存通道数：</span>
                  <a-tooltip
                    content="请输入1-10的值"
                    position="tr"
                    background-color="#3491FA"
                  >
                    <a-input
                      v-model="memoryFormData.channel"
                      class="input-field"
                    />
                  </a-tooltip>

                  <span class="hint"></span>
                </div>
                <div class="form-item">
                  <span>内存列数：</span>
                  <a-tooltip
                    content="请输入1-10的值"
                    position="tr"
                    background-color="#3491FA"
                  >
                    <a-input
                      v-model="memoryFormData.column"
                      class="input-field"
                    />
                  </a-tooltip>
                </div>
                <div class="button">
                  <a-button
                    id="memoryChannelsSubmit"
                    :disabled="!hasPermission('memoryChannelsSubmit')"
                    type="primary"
                    @click="handleMemorySubmit"
                  >
                    <template #icon>
                      <icon-check />
                    </template>
                    <template #default>设置</template>
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import {
    handleNotification4,
    handleNotification2,
  } from '../../../utils/info';

  interface OptionValue {
    [key: string]: string[];
  }

  interface DataItem {
    key: string;
    NetworkcardID?: string;
    choosecard?: string;
    checked?: boolean;
  }

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const options: OptionValue = {
        0: [],
        1: [],
        2: [],
        3: [],
      };

      const options2: OptionValue = {
        1: [],
        2: [],
        3: [],
        4: [],
      };

      // 定义columns数组中元素的类型
      const columns = [
        {
          title: '网卡ID',
          dataIndex: 'NetworkcardID',
          slotName: 'NetworkcardID',
        },
        {
          title: '选择网卡进入虚机',
          dataIndex: 'choosecard',
          slotName: 'choosecard',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];

      // 使用DataItem类型定义data数组中元素的类型
      const data = reactive<DataItem[]>([]);

      // 所有可用的物理端口
      const allPorts = ref<string[]>([]);

      // 已配置的端口
      const configuredPorts = ref<string[]>([]);

      // 选中的端口ID
      const selectedPorts = ref<string[]>([]);

      // CPU资源表单数据
      const cpuFormData = reactive({
        socket: '',
        cpu: '',
        core: '',
      });

      // 内存设置表单数据
      const memoryFormData = reactive({
        channel: '',
        column: '',
      });

      // 处理复选框变更
      const handleCheckboxChange = (record: DataItem) => {
        if (record.checked && record.NetworkcardID) {
          if (!selectedPorts.value.includes(record.NetworkcardID)) {
            selectedPorts.value.push(record.NetworkcardID);
          }
        } else if (record.NetworkcardID) {
          const index = selectedPorts.value.indexOf(record.NetworkcardID);
          if (index !== -1) {
            selectedPorts.value.splice(index, 1);
          }
        }
        console.log('选中的端口:', selectedPorts.value);
      };
      // 获取已配置的端口
      const fetchConfiguredPorts = async () => {
        try {
          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({ act: 'get_cfg_port' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 保存已配置的端口
            if (Array.isArray(response.data.data)) {
              configuredPorts.value = [...response.data.data];
              selectedPorts.value = [...response.data.data];

              // 清空现有数据
              data.length = 0;

              // 填充表格数据，显示所有物理端口
              if (allPorts.value.length > 0) {
                allPorts.value.forEach((portId: string, index: number) => {
                  // 检查端口是否在已配置列表中
                  const isConfigured = configuredPorts.value.includes(portId);

                  data.push({
                    key: String(index + 1),
                    NetworkcardID: portId,
                    checked: isConfigured, // 只有已配置的端口才选中
                  });
                });
              } else {
                console.warn('没有可用的物理端口');
              }
            } else {
              console.warn('没有已配置的端口或数据格式不正确');

              // 如果没有已配置的端口，仍然显示所有物理端口，但都不选中
              if (allPorts.value.length > 0) {
                allPorts.value.forEach((portId: string, index: number) => {
                  data.push({
                    key: String(index + 1),
                    NetworkcardID: portId,
                    checked: false,
                  });
                });
              }
            }
          } else {
            console.error('获取已配置端口失败：', response.data.result);
            Message.error(response.data.result || '获取配置端口失败');

            // 如果获取失败，仍然显示所有物理端口，但都不选中
            if (allPorts.value.length > 0) {
              allPorts.value.forEach((portId: string, index: number) => {
                data.push({
                  key: String(index + 1),
                  NetworkcardID: portId,
                  checked: false,
                });
              });
            }
          }
        } catch (error) {
          console.error('获取已配置端口错误:', error);
          Message.error('获取配置端口错误');

          // 如果出错，仍然显示所有物理端口，但都不选中
          if (allPorts.value.length > 0) {
            allPorts.value.forEach((portId: string, index: number) => {
              data.push({
                key: String(index + 1),
                NetworkcardID: portId,
                checked: false,
              });
            });
          }
        }
      };
      // 获取所有物理端口
      const fetchAllPorts = async () => {
        try {
          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({ act: 'get_all_port' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 保存所有物理端口
            if (Array.isArray(response.data.data)) {
              allPorts.value = [...response.data.data];

              // 获取已配置的端口
              fetchConfiguredPorts();
            } else {
              console.error('获取所有物理端口：返回数据格式不正确');
              Message.error('获取物理端口失败：数据格式错误');
            }
          } else {
            console.error('获取所有物理端口失败：', response.data.result);
            Message.error(response.data.result || '获取物理端口失败');
          }
        } catch (error) {
          console.error('获取所有物理端口错误:', error);
          Message.error('获取物理端口错误');
        }
      };

      // 获取CPU配置
      const fetchCpuConfig = async () => {
        try {
          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({ act: 'get_cpu' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            cpuFormData.cpu = response.data.data.cpu || '';
            cpuFormData.socket = response.data.data.socket || '';
            cpuFormData.core = response.data.data.core || '';
          } else {
            console.log('没有CPU配置或获取失败');
          }
        } catch (error) {
          console.error('获取CPU配置错误:', error);
        }
      };

      // 获取内存通道配置
      const fetchMemoryChannelConfig = async () => {
        try {
          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({ act: 'get_channel' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            memoryFormData.channel = response.data.data.chan || '';
          }
        } catch (error) {
          console.error('获取内存通道配置错误:', error);
        }
      };

      // 获取内存行列配置
      const fetchMemoryRankConfig = async () => {
        try {
          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({ act: 'get_rank' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            memoryFormData.column = response.data.data.rank || '';
          }
        } catch (error) {
          console.error('获取内存行列配置错误:', error);
        }
      };

      // 设置网络端口
      const setNetworkPorts = async () => {
        try {
          if (!hasPermission('networkPortSubmit')) {
            Message.error('您没有权限');
            return;
          }
          if (selectedPorts.value.length === 0) {
            Message.warning('请选择至少一个网卡');
            return;
          }

          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({
              act: 'set_port',
              id: selectedPorts.value.join(','),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '网络端口设置成功');
          } else {
            Message.error(response.data.result || '网络端口设置失败');
          }
        } catch (error) {
          console.error('设置网络端口错误:', error);
          Message.error('设置网络端口错误');
        }
      };

      // 设置CPU资源
      const setCpuResource = async () => {
        try {
          if (!hasPermission('cpuResourcesSubmit')) {
            Message.error('您没有权限');
            return;
          }
          if (!cpuFormData.cpu) {
            Message.warning('请输入CPU资源值');
            return;
          }

          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({
              act: 'set_cpu',
              cpu: cpuFormData.cpu,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || 'CPU资源设置成功');
          } else {
            Message.error(response.data.result || 'CPU资源设置失败');
          }
        } catch (error) {
          console.error('设置CPU资源错误:', error);
          Message.error('设置CPU资源错误');
        }
      };

      // 设置内存通道
      const setMemoryChannel = async () => {
        try {
          if (!hasPermission('memoryChannelsSubmit')) {
            Message.error('您没有权限');
            return;
          }
          if (!memoryFormData.channel) {
            Message.warning('请输入内存通道数');
            return;
          }

          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({
              act: 'set_channel',
              channel: memoryFormData.channel,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '内存通道设置成功');
          } else {
            Message.error(response.data.result || '内存通道设置失败');
          }
        } catch (error) {
          console.error('设置内存通道错误:', error);
          Message.error('设置内存通道错误');
        }
      };

      // 设置内存列数
      const setMemoryRank = async () => {
        try {
          if (!hasPermission('memoryRowsSubmit')) {
            Message.error('您没有权限');
            return;
          }
          if (!memoryFormData.column) {
            Message.warning('请输入内存列数');
            return;
          }

          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({
              act: 'set_rank',
              column: memoryFormData.column,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '内存列数设置成功');
          } else {
            Message.error(response.data.result || '内存列数设置失败');
          }
        } catch (error) {
          console.error('设置内存列数错误:', error);
          Message.error('设置内存列数错误');
        }
      };

      // 处理网络端口按钮点击
      const handleNetworkPortsSubmit = () => {
        if (!hasPermission('networkPortSubmit')) {
          Message.error('您没有权限');
          return;
        }
        setNetworkPorts();
      };

      // 处理CPU资源按钮点击
      const handleCpuResourceSubmit = () => {
        if (!hasPermission('cpuResourcesSubmit')) {
          Message.error('您没有权限');
          return;
        }
        setCpuResource();
      };

      // 处理内存设置按钮点击
      const handleMemorySubmit = () => {
        if (!hasPermission('memoryChannelsSubmit')) {
          Message.error('您没有权限');
          return;
        }
        setMemoryChannel();
        setMemoryRank();
      };

      onMounted(() => {
        fetchAllPorts(); // 先获取所有物理端口，然后会自动获取已配置端口
        fetchCpuConfig();
        fetchMemoryChannelConfig();
        fetchMemoryRankConfig();
      });

      return {
        options,
        options2,
        columns,
        data,
        cpuFormData,
        memoryFormData,
        handleCheckboxChange,
        handleNetworkPortsSubmit,
        handleCpuResourceSubmit,
        handleMemorySubmit,
        handleNotification2,
        handleNotification4,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .hint {
    flex: 1; /* 占据剩余空间 */
    text-align: left;
    color: gray;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }
  .button {
    margin-top: 20px;
    text-align: center;
  }

  .arco-input-wrapper {
    margin: 10px 0 !important;
  }
  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
</style>
