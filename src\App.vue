<template>
  <a-config-provider :locale="locale">
    <router-view />
    <global-setting />
  </a-config-provider>
</template>

<script lang="ts" setup>
  import { computed, onMounted, onUnmounted, ref } from 'vue';
  import enUS from '@arco-design/web-vue/es/locale/lang/en-us';
  import zhCN from '@arco-design/web-vue/es/locale/lang/zh-cn';
  import * as GlobalSettingModule from '@/components/global-setting/index.vue';
  import useLocale from '@/hooks/locale';
  import { loadAndSetAppTitle } from '@/utils/title';
  import { useUserStore, useAppStore } from '@/store';
  import { Message } from '@arco-design/web-vue';

  const GlobalSetting = GlobalSettingModule;

  const userStore = useUserStore();
  const appStore = useAppStore();
  const { currentLocale } = useLocale();
  const lastPermissionCheck = ref<number>(0);
  const checkInterval = ref<number | null>(null);

  const locale = computed(() => {
    switch (currentLocale.value) {
      case 'zh-CN':
        return zhCN;
      case 'en-US':
        return enUS;
      default:
        return enUS;
    }
  });

  // 检查并更新权限和菜单
  const checkAndUpdatePermissions = async () => {
    try {
      // 获取上次权限更新时间
      const lastUpdated = localStorage.getItem('permission_last_updated');

      // 如果没有上次更新记录，记录当前时间并返回
      if (!lastUpdated) {
        lastPermissionCheck.value = Date.now();
        localStorage.setItem(
          'permission_last_updated',
          lastPermissionCheck.value.toString()
        );
        return;
      }

      // 如果上次检查后有新的更新，则刷新权限和菜单
      if (
        Number(lastUpdated) > lastPermissionCheck.value ||
        localStorage.getItem('permissions_changed') === 'true'
      ) {
        console.log('检测到权限变更，正在更新菜单...');

        // 强制刷新用户权限
        await userStore.fetchUserPermissions(true);

        // 强制刷新菜单
        if (appStore.menuFromServer) {
          await appStore.fetchServerMenuConfig(true);
        }

        // 更新最后检查时间
        lastPermissionCheck.value = Date.now();

        // 清除变更标志
        localStorage.removeItem('permissions_changed');

        console.log('菜单和权限已更新');
      }
    } catch (error) {
      console.error('检查权限更新失败:', error);
    }
  };

  // 启动定期检查
  const startPermissionCheckInterval = () => {
    // 每30秒检查一次权限变更
    checkInterval.value = window.setInterval(checkAndUpdatePermissions, 30000);

    // 初始检查
    checkAndUpdatePermissions();
  };
  // 处理窗口可见性变化
  const handleVisibilityChange = () => {
    // 当用户返回页面时，立即检查权限变更
    if (document.visibilityState === 'visible') {
      checkAndUpdatePermissions();
    }
  };

  const updatePermissions = async () => {
    try {
      // 强制刷新用户权限
      await userStore.fetchUserPermissions(true);

      // 强制刷新菜单
      if (appStore.menuFromServer) {
        await appStore.fetchServerMenuConfig(true);
      }
    } catch (error) {
      console.error('检查权限更新失败:', error);
    }
  };
  // 应用挂载时
  onMounted(() => {
    // 加载标题
    loadAndSetAppTitle();

    const id = userStore.userInfo?.accountId || '';
    if (id !== '') {
      updatePermissions();
    }

    // 启动权限检查
    // startPermissionCheckInterval();

    // 监听窗口可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange);
  });

  // 组件卸载时清理
  onUnmounted(() => {
    // 清除定时器
    if (checkInterval.value !== null) {
      clearInterval(checkInterval.value);
    }

    // 移除事件监听
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  });
</script>
