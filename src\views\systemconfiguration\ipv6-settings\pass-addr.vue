<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-space>
          <!-- 新建按钮 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            添加外层vlan
          </a-button>
        </a-space>
      </a-col>
      <!-- 右侧按钮 -->
      <a-col
        :span="12"
        style="display: flex; align-items: center; justify-content: end"
      >
        <!-- 刷新按钮 -->
        <a-tooltip content="刷新">
          <div class="action-icon" @click="handleRefresh">
            <icon-refresh size="18" />
          </div>
        </a-tooltip>
      </a-col>
    </a-row>

    <!-- 列表 -->
    <a-table
      :columns="columns"
      :data="data"
      style="margin-top: 20px"
      :pagination="false"
    >
      <template #operation="{ index }">
        <a-button status="danger" @click="deleteRow(index)">
          <template #icon>
            <icon-delete />
          </template>
          <template #default>删除</template>
        </a-button>
      </template>
    </a-table>
  </a-card>

  <a-modal
    v-model:visible="isModalVisible"
    title="添加外层vlan"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
    :width="700"
  >
    <a-form :model="formData" :rules="rules" ref="formRef">
      <a-form-item label="外层起始vlan" field="start_vlan">
        <a-input v-model="formData.start_vlan" style="width: 200px" />
      </a-form-item>
      <a-form-item label="外层结束vlan" field="end_vlan">
        <a-input v-model="formData.end_vlan" style="width: 200px" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidVlanId } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns = [
        {
          title: 'Start Address',
          dataIndex: 'start_vlan',
        },
        {
          title: 'End Address',
          dataIndex: 'end_vlan',
        },
        {
          title: 'End Description',
          dataIndex: 'end_vlan',
        },
        {
          title: '操作',
          dataIndex: 'operation',
          slotName: 'operation',
        },
      ];

      const data = ref<{ start_vlan: string; end_vlan: string }[]>([]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);

      // 表单数据
      const formData = reactive({
        start_vlan: '',
        end_vlan: '',
      });

      // 表单规则
      const rules = {
        start_vlan: [
          { required: true, message: '外层起始vlan不能为空' },
          {
            validator: (value: number, callback: (error?: string) => void) => {
              if (!isValidVlanId(value)) {
                callback('vlan的范围：0~4095');
              } else {
                callback();
              }
            },
          },
        ],
        end_vlan: [
          { required: true, message: '外层结束vlan不能为空' },
          {
            validator: (value: number, callback: (error?: string) => void) => {
              if (!isValidVlanId(value)) {
                callback('vlan的范围：0~4095');
              } else {
                callback();
              }
            },
          },
        ],
      };

      const formRef = ref();

      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_spec_bw_ctrl.lua',
            new URLSearchParams({ act: 'chk_pvlan' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 如果后端返回了时间段数据，可以在这里设置
            if (response.data.data && Array.isArray(response.data.data)) {
              data.value = response.data.data;
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 刷新数据
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 显示模态框
      const showModal = () => {
        formData.start_vlan = '';
        formData.end_vlan = '';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleBeforeOk = (done) => {
        formRef.value.validate().then((errors) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 添加到列表
          data.value.push({
            start_vlan: formData.start_vlan,
            end_vlan: formData.end_vlan,
          });

          Message.success('添加成功');
          done(true); // 允许模态框关闭
        });
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      // 删除行
      const deleteRow = (index: number) => {
        data.value.splice(index, 1);
        Message.success('删除成功');
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        handleRefresh,
        isRefreshing,
        formData,
        rules,
        formRef,
        isModalVisible,
        showModal,
        handleBeforeOk,
        handleCancel,
        deleteRow,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
