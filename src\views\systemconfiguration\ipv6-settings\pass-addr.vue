<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- 页面标题 -->
    <div style="margin-bottom: 16px">
      <h3 style="margin: 0; color: #1d2129">
        {{ $t('ipv6_pass_addr_title') }}
      </h3>
    </div>

    <!-- 新建按钮 -->
    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-button type="primary" @click="showAddModal">
          <template #icon>
            <icon-plus />
          </template>
          {{ $t('add') }}
        </a-button>
      </a-col>
    </a-row>

    <!-- IPv6地址配置表格 -->
    <a-table
      row-key="index"
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
    >
      <template #start_address="{ record }">
        <span>{{ record.start_addr || '-' }}</span>
      </template>

      <template #end_address="{ record }">
        <span>{{ record.end_addr || '-' }}</span>
      </template>

      <template #description="{ record }">
        <span>{{ record.desc || '-' }}</span>
      </template>

      <template #operation="{ record }">
        <a-space>
          <a-button type="primary" size="small" @click="editRecord(record)">
            <template #icon>
              <icon-edit />
            </template>
            {{ $t('edit') }}
          </a-button>
          <a-button
            type="primary"
            status="danger"
            size="small"
            @click="deleteRecord(record)"
          >
            <template #icon>
              <icon-delete />
            </template>
            {{ $t('delete') }}
          </a-button>
        </a-space>
      </template>
    </a-table>

    <!-- 分页信息 -->
    <div style="margin-top: 16px; color: #86909c; font-size: 14px">
      {{
        $t('display_records', {
          start: tableData.length,
          end: tableData.length,
          total: tableData.length,
        })
      }}
    </div>
  </a-card>

  <!-- 新建IPv6地址模态框 -->
  <a-modal
    v-model:visible="isAddModalVisible"
    :title="$t('add_ipv6_address')"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleAddConfirm"
    @cancel="handleAddCancel"
    :width="600"
  >
    <a-form :model="addFormData" :rules="addRules" ref="addFormRef">
      <a-form-item :label="$t('start_address')" field="start_addr">
        <a-input
          v-model="addFormData.start_addr"
          :placeholder="$t('please_enter_start_address')"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item :label="$t('end_address')" field="end_addr">
        <a-input
          v-model="addFormData.end_addr"
          :placeholder="$t('please_enter_end_address')"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item :label="$t('description')" field="desc">
        <a-input
          v-model="addFormData.desc"
          :placeholder="$t('please_enter_description')"
          style="width: 300px"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 编辑描述模态框 -->
  <a-modal
    v-model:visible="isEditModalVisible"
    :title="$t('edit_description')"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleEditConfirm"
    @cancel="handleEditCancel"
    :width="800"
  >
    <a-form :model="editFormData" :rules="editRules" ref="editFormRef">
      <a-form-item :label="$t('start_address')" field="start_addr">
        <a-input
          v-model="editFormData.start_addr"
          disabled
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item :label="$t('end_address')" field="end_addr">
        <a-input
          v-model="editFormData.end_addr"
          disabled
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item :label="$t('description')" field="desc">
        <a-input
          v-model="editFormData.desc"
          :placeholder="$t('please_enter_description')"
          style="width: 300px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface IPv6AddressItem {
    index: number;
    start_addr: string;
    end_addr: string;
    desc: string;
    isEditing?: boolean;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();
      const { t } = useI18n();

      // 表格列定义
      const columns = [
        {
          title: t('start_address'),
          dataIndex: 'start_addr',
          slotName: 'start_address',
          align: 'center',
          width: 250,
        },
        {
          title: t('end_address'),
          dataIndex: 'end_addr',
          slotName: 'end_address',
          align: 'center',
          width: 250,
        },
        {
          title: t('description'),
          dataIndex: 'desc',
          slotName: 'description',
          align: 'center',
          width: 200,
        },
        {
          title: t('operate'),
          dataIndex: 'operation',
          slotName: 'operation',
          align: 'center',
          width: 200,
        },
      ];

      const tableData = ref<IPv6AddressItem[]>([]);
      const isLoading = ref(false);
      const isAddModalVisible = ref(false);
      const isEditModalVisible = ref(false);
      const currentEditRecord = ref<IPv6AddressItem | null>(null);

      // 新建表单数据
      const addFormData = reactive({
        start_addr: '',
        end_addr: '',
        desc: '',
      });

      // 编辑表单数据
      const editFormData = reactive({
        start_addr: '',
        end_addr: '',
        desc: '',
      });

      // 新建表单规则
      const addRules = {
        start_addr: [{ required: true, message: t('start_address_required') }],
        end_addr: [{ required: true, message: t('end_address_required') }],
      };

      // 编辑表单规则
      const editRules = {
        desc: [{ required: false, message: t('description_optional') }],
      };

      const addFormRef = ref();
      const editFormRef = ref();

      // 获取IPv6地址数据
      const fetchData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_ipv6_setting.lua',
            new URLSearchParams({ act: 'pass_addr' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              tableData.value = response.data.data.map(
                (item: any, index: number) => ({
                  index: index + 1,
                  start_addr: item.start_addr || '',
                  end_addr: item.end_addr || '',
                  desc: item.desc || '',
                  isEditing: false,
                })
              );
            } else {
              tableData.value = [];
            }
          } else {
            Message.error({
              content: response.data.err || t('get_data_failed'),
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error(t('get_data_failed'));
        } finally {
          isLoading.value = false;
        }
      };

      // 显示新建模态框
      const showAddModal = () => {
        addFormData.start_addr = '';
        addFormData.end_addr = '';
        addFormData.desc = '';
        isAddModalVisible.value = true;
      };

      // 处理新建确认
      const handleAddConfirm = async (done: any) => {
        try {
          const errors = await addFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          const response = await axios.post(
            'lua/set_cfg_ipv6_settings.lua',
            new URLSearchParams({
              act: 'pass_addr',
              act_type: 'add',
              start_addr: addFormData.start_addr,
              end_addr: addFormData.end_addr,
              desc: addFormData.desc,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(t('add_success'));
            fetchData(); // 重新获取数据
            done(true);
          } else {
            Message.error({
              content: response.data.err || t('add_failed'),
              duration: 5000,
            });
            done(false);
          }
        } catch (error) {
          console.error('添加失败:', error);
          Message.error(t('add_request_failed'));
          done(false);
        }
      };

      // 处理新建取消
      const handleAddCancel = () => {
        isAddModalVisible.value = false;
      };

      // 编辑记录
      const editRecord = (record: IPv6AddressItem) => {
        currentEditRecord.value = record;
        editFormData.start_addr = record.start_addr;
        editFormData.end_addr = record.end_addr;
        editFormData.desc = record.desc;
        isEditModalVisible.value = true;
      };

      // 处理编辑确认
      const handleEditConfirm = async (done: any) => {
        try {
          const response = await axios.post(
            'lua/set_cfg_ipv6_settings.lua',
            new URLSearchParams({
              act: 'pass_addr',
              act_type: 'edit',
              start_addr: editFormData.start_addr,
              end_addr: editFormData.end_addr,
              desc: editFormData.desc,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(t('edit_success'));
            fetchData(); // 重新获取数据
            done(true);
          } else {
            Message.error({
              content: response.data.err || t('edit_failed'),
              duration: 5000,
            });
            done(false);
          }
        } catch (error) {
          console.error('编辑失败:', error);
          Message.error(t('edit_request_failed'));
          done(false);
        }
      };

      // 处理编辑取消
      const handleEditCancel = () => {
        isEditModalVisible.value = false;
        currentEditRecord.value = null;
      };

      // 删除记录
      const deleteRecord = async (record: IPv6AddressItem) => {
        try {
          const response = await axios.post(
            'lua/set_cfg_ipv6_settings.lua',
            new URLSearchParams({
              act: 'pass_addr',
              act_type: 'delete',
              start_addr: record.start_addr,
              end_addr: record.end_addr,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(t('delete_success'));
            fetchData(); // 重新获取数据
          } else {
            Message.error({
              content: response.data.err || t('delete_failed'),
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('删除失败:', error);
          Message.error(t('delete_request_failed'));
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        tableData,
        isLoading,
        isAddModalVisible,
        isEditModalVisible,
        addFormData,
        editFormData,
        addRules,
        editRules,
        addFormRef,
        editFormRef,
        showAddModal,
        handleAddConfirm,
        handleAddCancel,
        editRecord,
        handleEditConfirm,
        handleEditCancel,
        deleteRecord,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
