import { defineStore } from 'pinia';
import { Notification } from '@arco-design/web-vue';
import type { NotificationReturn } from '@arco-design/web-vue/es/notification/interface';
import type { RouteRecordNormalized } from 'vue-router';
import defaultSettings from '@/config/settings.json';
// import { getMenuList } from '@/api/user';
import { AppState } from './types';

const useAppStore = defineStore('app', {
  state: (): AppState => {
    const savedMenu = localStorage.getItem('appServerMenu');
    const initialState = { ...defaultSettings };

    if (savedMenu) {
      initialState.serverMenu = JSON.parse(savedMenu);
    }

    return initialState;
  },

  getters: {
    appCurrentSetting(state: AppState): AppState {
      return { ...state };
    },
    appDevice(state: AppState) {
      return state.device;
    },
    appAsyncMenus(state: AppState): RouteRecordNormalized[] {
      return state.serverMenu as unknown as RouteRecordNormalized[];
    },
  },

  actions: {
    // Update app settings
    updateSettings(partial: Partial<AppState>) {
      // @ts-ignore-next-line
      this.$patch(partial);
    },

    // Change theme color
    toggleTheme(dark: boolean) {
      if (dark) {
        this.theme = 'dark';
        document.body.setAttribute('arco-theme', 'dark');
      } else {
        this.theme = 'light';
        document.body.removeAttribute('arco-theme');
      }
    },
    toggleDevice(device: string) {
      this.device = device;
    },
    toggleMenu(value: boolean) {
      this.hideMenu = value;
    },
    async fetchServerMenuConfig(forceRefresh = false) {
      let notifyInstance: NotificationReturn | null = null;
      try {
        if (!forceRefresh && this.serverMenu && this.serverMenu.length > 0) {
          return;
        }

        // notifyInstance = Notification.info({
        //   id: 'menuNotice',
        //   content: '加载菜单配置...',
        //   closable: true,
        // });

        // 从localStorage获取菜单数据
        const savedMenuData = localStorage.getItem('appServerMenu');
        if (savedMenuData) {
          try {
            const menuData = JSON.parse(savedMenuData);
            this.serverMenu = menuData;

            // notifyInstance = Notification.success({
            //   id: 'menuNotice',
            //   content: '菜单加载成功',
            //   closable: true,
            // });
          } catch (e) {
            console.error('解析菜单数据失败:', e);
          }
        }

        // notifyInstance = Notification.warning({
        //   id: 'menuNotice',
        //   content: '未找到菜单数据',
        //   closable: true,
        // });
      } catch (error) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        notifyInstance = Notification.error({
          id: 'menuNotice',
          content: '加载菜单失败',
          closable: true,
        });
      }
    },
    clearServerMenu() {
      this.serverMenu = [];
      localStorage.removeItem('appServerMenu');
    },
  },
});

export default useAppStore;
