<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- DHCP Relay 端口配置表格 -->
    <a-table
      row-key="portLabel"
      :columns="columns"
      :data="data"
      style="margin-top: 20px"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
    >
      <template #port0="{ record }">
        <a-select v-model="record.port0" :style="{ width: '80px' }">
          <a-option value="enable">{{ $t('yes') }}</a-option>
          <a-option value="disable">{{ $t('no') }}</a-option>
        </a-select>
      </template>
      <template #port1="{ record }">
        <a-select v-model="record.port1" :style="{ width: '80px' }">
          <a-option value="enable">{{ $t('yes') }}</a-option>
          <a-option value="disable">{{ $t('no') }}</a-option>
        </a-select>
      </template>
      <template #port2="{ record }">
        <a-select v-model="record.port2" :style="{ width: '80px' }">
          <a-option value="enable">{{ $t('yes') }}</a-option>
          <a-option value="disable">{{ $t('no') }}</a-option>
        </a-select>
      </template>
      <template #port3="{ record }">
        <a-select v-model="record.port3" :style="{ width: '80px' }">
          <a-option value="enable">{{ $t('yes') }}</a-option>
          <a-option value="disable">{{ $t('no') }}</a-option>
        </a-select>
      </template>
      <template #port4="{ record }">
        <a-select v-model="record.port4" :style="{ width: '80px' }">
          <a-option value="enable">{{ $t('yes') }}</a-option>
          <a-option value="disable">{{ $t('no') }}</a-option>
        </a-select>
      </template>
    </a-table>

    <!-- DHCP Relay 服务器地址配置 -->
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 4 }"
      :wrapper-col-props="{ span: 18 }"
      style="margin-top: 30px"
    >
      <a-form-item
        :rules="rules.dhcp_relay_srv_addr"
        field="dhcp_relay_srv_addr"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">{{ $t('dhcp_relay_server_address') }}</span>
        </template>
        <a-input
          v-model="formData.dhcp_relay_srv_addr"
          :style="{ width: '300px' }"
          :placeholder="$t('please_enter_dhcp_relay_server_address')"
        />
      </a-form-item>

      <a-form-item
        :rules="rules.dhcp_relay_local_addr"
        field="dhcp_relay_local_addr"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">{{ $t('dhcp_relay_local_address') }}</span>
        </template>
        <a-input
          v-model="formData.dhcp_relay_local_addr"
          :style="{ width: '300px' }"
          :placeholder="$t('please_enter_dhcp_relay_local_address')"
        />
      </a-form-item>

      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          style="margin-top: 2%"
          @click="saveAction"
          :loading="isSubmitting"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>{{ $t('submit') }}</template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, ref, reactive, onMounted, watch } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';

  interface DhcpPortData {
    portLabel: string;
    port0: string;
    port1: string;
    port2: string;
    port3: string;
    port4: string;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();
      const { t } = useI18n();
      const formRef = ref(null);

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: t('port_number'),
          dataIndex: 'portLabel',
          width: 120,
          align: 'center',
        },
        {
          title: '0',
          dataIndex: 'port0',
          slotName: 'port0',
          width: 100,
          align: 'center',
        },
        {
          title: '1',
          dataIndex: 'port1',
          slotName: 'port1',
          width: 100,
          align: 'center',
        },
        {
          title: '2',
          dataIndex: 'port2',
          slotName: 'port2',
          width: 100,
          align: 'center',
        },
        {
          title: '3',
          dataIndex: 'port3',
          slotName: 'port3',
          width: 100,
          align: 'center',
        },
        {
          title: '4',
          dataIndex: 'port4',
          slotName: 'port4',
          width: 100,
          align: 'center',
        },
      ];

      // 表单数据
      const formData = reactive({
        dhcp_relay_srv_addr: '',
        dhcp_relay_local_addr: '',
      });

      // 表单验证规则
      const rules = {
        dhcp_relay_srv_addr: [
          { required: true, message: t('dhcp_relay_server_address_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('ip_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        dhcp_relay_local_addr: [
          { required: true, message: t('dhcp_relay_local_address_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('ip_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
      };

      const data = ref<DhcpPortData[]>([
        {
          portLabel: t('accept_user_dhcp_relay_request'),
          port0: 'disable',
          port1: 'disable',
          port2: 'disable',
          port3: 'disable',
          port4: 'disable',
        },
      ]);

      const isLoading = ref(false);
      const isSubmitting = ref(false);

      // 获取DHCP Relay配置
      const fetchData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'dhcp_relay' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200 && response.data.data) {
            const responseData = response.data.data;

            // 重置所有端口为禁用
            const row = data.value[0];
            row.port0 = 'disable';
            row.port1 = 'disable';
            row.port2 = 'disable';
            row.port3 = 'disable';
            row.port4 = 'disable';

            // 根据接口数据设置端口状态
            if (responseData.switch && Array.isArray(responseData.switch)) {
              responseData.switch.forEach((item: any) => {
                if (item.port && item.dhcp === 'enable') {
                  const portKey = `port${item.port}` as keyof DhcpPortData;
                  if (portKey in row) {
                    (row as any)[portKey] = 'enable';
                  }
                }
              });
            }

            // 设置服务器地址
            formData.dhcp_relay_srv_addr =
              responseData.dhcp_relay_srv_addr || '';
            formData.dhcp_relay_local_addr =
              responseData.dhcp_relay_local_addr || '';

            console.log('获取到的DHCP Relay配置数据:', responseData);
          } else {
            Message.error({
              content: response.data.err || t('get_dhcp_relay_config_failed'),
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取DHCP Relay配置失败:', error);
          Message.error(t('get_dhcp_relay_config_failed'));
        } finally {
          isLoading.value = false;
        }
      };

      // 保存DHCP Relay配置
      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error(t('no_permission'));
          return;
        }

        // 表单验证
        try {
          const errors = await formRef.value.validate();
          if (errors) {
            Message.error(t('form_validation_failed'));
            return;
          }
        } catch (validationError) {
          Message.error(t('form_validation_error'));
          console.error('Validation error:', validationError);
          return;
        }

        isSubmitting.value = true;
        try {
          const currentData = data.value[0];

          // 构建端口开关数据
          const switchData = [];
          [0, 1, 2, 3, 4].forEach((i) => {
            const portKey = `port${i}` as keyof DhcpPortData;
            if (currentData[portKey] === 'enable') {
              switchData.push({
                port: i.toString(),
                dhcp: 'enable',
              });
            }
          });

          // 创建提交数据
          const submitData = new URLSearchParams();
          submitData.append('act', 'dhcp_relay');
          submitData.append(
            'dhcp_relay_srv_addr',
            formData.dhcp_relay_srv_addr
          );
          submitData.append(
            'dhcp_relay_local_addr',
            formData.dhcp_relay_local_addr
          );
          submitData.append('switch', JSON.stringify(switchData));

          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            submitData,
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(t('dhcp_relay_config_save_success'));
            fetchData(); // 刷新数据以确保状态正确
          } else {
            Message.error({
              content: response.data.err || t('configuration_save_failed'),
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('保存DHCP Relay配置失败:', error);
          Message.error(t('save_request_failed'));
        } finally {
          isSubmitting.value = false;
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        formData,
        rules,
        formRef,
        isLoading,
        isSubmitting,
        saveAction,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }

  :deep(.arco-table-th) {
    text-align: center;
    background-color: #f7f8fa;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }

  .form-label {
    font-weight: 500;
  }
</style>
