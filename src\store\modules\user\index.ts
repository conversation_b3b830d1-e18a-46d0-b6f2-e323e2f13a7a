import { defineStore } from 'pinia';
import { login as userLogin, LoginData, getMenuByUserId } from '@/api/user';
import { setToken, clearToken } from '@/utils/auth';
import { removeRouteListener } from '@/utils/route-listener';
import axios from 'axios';
import { UserState } from './types';
import useAppStore from '../app';

const useUserStore = defineStore('user', {
  state: (): UserState => {
    // 从localStorage中恢复状态
    const savedState = localStorage.getItem('userState');
    const defaultState = {
      name: undefined,
      avatar: undefined,
      job: undefined,
      organization: undefined,
      location: undefined,
      email: undefined,
      introduction: undefined,
      personalWebsite: undefined,
      jobName: undefined,
      organizationName: undefined,
      locationName: undefined,
      phone: undefined,
      registrationDate: undefined,
      accountId: undefined,
      certification: undefined,
      role: '',
      permissions: [], // 初始化为空数组而不是undefined
    };
    return savedState ? JSON.parse(savedState) : defaultState;
  },

  getters: {
    userInfo(state: UserState): UserState {
      return { ...state };
    },
  },

  actions: {
    switchRoles() {
      return new Promise((resolve) => {
        this.role = this.role === 'user' ? 'admin' : 'user';
        resolve(this.role);
      });
    },
    // Set user's information
    setInfo(partial: Partial<UserState>) {
      this.$patch(partial);
      // 保存状态到localStorage
      localStorage.setItem('userState', JSON.stringify(this.$state));
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
      // 清除localStorage中的状态
      localStorage.removeItem('userState');
    },

    // Login
    async login(loginForm: LoginData) {
      try {
        const res = await userLogin(loginForm);
        if (res && res.code === 200) {
          // 登录成功，设置token
          setToken(res.result);

          localStorage.setItem('userId', loginForm.userid);

          // 设置用户基本信息
          this.setInfo({
            name: `${loginForm.userid}`,
            accountId: loginForm.userid,
          });

          console.log('登录成功，用户名:', loginForm.userid);
          // 成功时返回数据，不抛出错误
          return res;
        }

        // 登录失败，抛出错误
        clearToken();
        throw new Error(res?.result || '登录失败');
      } catch (err) {
        clearToken();
        throw err;
      }
    },

    // Logout方法
    async logout() {
      try {
        // 调用登出回调函数
        this.logoutCallBack();
        return true;
      } catch (err) {
        return false;
      }
    },

    logoutCallBack() {
      const appStore = useAppStore();
      this.resetInfo();
      this.setPermissions([]); // 明确清空权限信息
      clearToken();
      removeRouteListener();
      appStore.clearServerMenu();

      // 清除本地存储中的所有用户相关数据
      localStorage.removeItem('userState');
      localStorage.removeItem('permissions');
      localStorage.removeItem('userRole');
      localStorage.removeItem('userId');
      localStorage.removeItem('login-config'); // 清除登录配置
      localStorage.removeItem('recentLinks'); // 清除最近访问记录
    },
    // 设置用户权限
    setPermissions(permissions: string[]) {
      this.permissions = permissions;

      // 额外保存权限到localStorage，便于调试
      localStorage.setItem('permissions', JSON.stringify(permissions));

      // 保存状态到localStorage
      localStorage.setItem('userState', JSON.stringify(this.$state));
    },

    // 检查用户是否有特定权限
    hasPermission(permissionId: string) {
      return this.permissions.includes(permissionId);
    },

    // 在登录成功后获取用户权限
    async fetchUserPermissions(forceRefresh = false) {
      try {
        const appStore = useAppStore();

        // 如果已有权限且不强制刷新，则直接返回
        // if (!forceRefresh && this.permissions && this.permissions.length > 0) {
        //   return;
        // }
        // 从localStorage获取用户ID
        const userId = localStorage.getItem('userId');

        // 如果存在用户ID，通过用户ID获取菜单
        if (userId) {
          try {
            // 确保accountId和userId保持一致
            this.setInfo({
              accountId: userId,
              name: `用户${userId}`,
            });

            // 从本地存储获取缓存的菜单数据
            const cachedMenuData = localStorage.getItem('userMenuData');
            const cacheTimestamp = localStorage.getItem('userMenuTimestamp');
            const now = Date.now();
            const cacheAge = cacheTimestamp
              ? now - parseInt(cacheTimestamp, 10)
              : Infinity;

            const response = await getMenuByUserId(userId);

            if (response && response.code === 200) {
              // 提取所有菜单项ID作为权限
              const getAllMenuIds = (menuItems: any[]): string[] => {
                let ids: string[] = [];
                if (!menuItems || !Array.isArray(menuItems)) return ids;

                menuItems.forEach((item) => {
                  // 从resource属性获取权限ID
                  if (item.resource) {
                    ids.push(item.resource);
                  }
                  // 兼容处理，如果有id属性也获取
                  if (item.id) {
                    ids.push(item.id);
                  }
                  // 递归获取子菜单的权限
                  if (item.submenu) {
                    ids = ids.concat(getAllMenuIds(item.submenu));
                  }
                  if (item.children) {
                    ids = ids.concat(getAllMenuIds(item.children));
                  }
                  if (item.action) {
                    ids = ids.concat(getAllMenuIds(item.action));
                  }
                });
                return ids;
              };

              // 设置菜单数据到appStore
              if (response.data && response.data.menu) {
                const menuData = response.data.menu;

                // 缓存菜单数据和时间戳
                localStorage.setItem('userMenuData', JSON.stringify(menuData));
                localStorage.setItem('userMenuTimestamp', now.toString());

                appStore.serverMenu = menuData;
                localStorage.setItem('appServerMenu', JSON.stringify(menuData));

                // 提取权限并设置
                const allPermissions = menuData ? getAllMenuIds(menuData) : [];

                // 如果没有从菜单中获取到权限，先设置默认权限，方便调试
                if (allPermissions.length === 0) {
                  this.setPermissions(['workplace', 'dashboard']);
                } else {
                  this.setPermissions(allPermissions);
                }

                // 清除旧菜单并刷新菜单配置
                await appStore.fetchServerMenuConfig(true);
              } else {
                this.setPermissions(['workplace', 'dashboard']);
              }
            } else {
              this.setPermissions(['workplace', 'dashboard']);
            }

            // 只有在强制刷新或缓存超过30分钟时才重新获取数据
            // if (forceRefresh || !cachedMenuData || cacheAge > 30 * 60 * 1000) {
            //   const response = await getMenuByUserId(userId);

            //   if (response && response.code === 200) {
            //     // 提取所有菜单项ID作为权限
            //     const getAllMenuIds = (menuItems: any[]): string[] => {
            //       let ids: string[] = [];
            //       if (!menuItems || !Array.isArray(menuItems)) return ids;

            //       menuItems.forEach((item) => {
            //         // 从resource属性获取权限ID
            //         if (item.resource) {
            //           ids.push(item.resource);
            //         }
            //         // 兼容处理，如果有id属性也获取
            //         if (item.id) {
            //           ids.push(item.id);
            //         }
            //         // 递归获取子菜单的权限
            //         if (item.submenu) {
            //           ids = ids.concat(getAllMenuIds(item.submenu));
            //         }
            //         if (item.children) {
            //           ids = ids.concat(getAllMenuIds(item.children));
            //         }
            //         if (item.action) {
            //           ids = ids.concat(getAllMenuIds(item.action));
            //         }
            //       });
            //       return ids;
            //     };

            //     // 设置菜单数据到appStore
            //     if (response.data && response.data.menu) {
            //       const menuData = response.data.menu;

            //       // 缓存菜单数据和时间戳
            //       localStorage.setItem(
            //         'userMenuData',
            //         JSON.stringify(menuData)
            //       );
            //       localStorage.setItem('userMenuTimestamp', now.toString());

            //       appStore.serverMenu = menuData;
            //       localStorage.setItem(
            //         'appServerMenu',
            //         JSON.stringify(menuData)
            //       );

            //       // 提取权限并设置
            //       const allPermissions = menuData
            //         ? getAllMenuIds(menuData)
            //         : [];

            //       // 如果没有从菜单中获取到权限，先设置默认权限，方便调试
            //       if (allPermissions.length === 0) {
            //         this.setPermissions(['workplace', 'dashboard']);
            //       } else {
            //         this.setPermissions(allPermissions);
            //       }

            //       // 清除旧菜单并刷新菜单配置
            //       await appStore.fetchServerMenuConfig(true);
            //     } else {
            //       this.setPermissions(['workplace', 'dashboard']);
            //     }
            //   } else {
            //     this.setPermissions(['workplace', 'dashboard']);
            //   }
            // } else if (cachedMenuData) {
            //   // 使用缓存的菜单数据
            //   const menuData = JSON.parse(cachedMenuData);

            //   // 设置菜单数据到appStore
            //   appStore.serverMenu = menuData;

            //   // 提取所有菜单项ID作为权限
            //   const getAllMenuIds = (menuItems: any[]): string[] => {
            //     let ids: string[] = [];
            //     if (!menuItems || !Array.isArray(menuItems)) return ids;

            //     menuItems.forEach((item) => {
            //       if (item.resource) ids.push(item.resource);
            //       if (item.id) ids.push(item.id);
            //       if (item.submenu)
            //         ids = ids.concat(getAllMenuIds(item.submenu));
            //       if (item.children)
            //         ids = ids.concat(getAllMenuIds(item.children));
            //       if (item.action) ids = ids.concat(getAllMenuIds(item.action));
            //     });
            //     return ids;
            //   };

            //   // 提取权限并设置
            //   const allPermissions = menuData ? getAllMenuIds(menuData) : [];
            //   if (allPermissions.length > 0) {
            //     this.setPermissions(allPermissions);
            //   } else {
            //     this.setPermissions(['workplace', 'dashboard']);
            //   }
            // }

            return;
          } catch (error) {
            console.error('获取菜单数据失败:', error);
            this.setPermissions(['workplace', 'dashboard']);
            return;
          }
        }

        // 如果是test账号，从API获取临时管理组的权限
        const token = localStorage.getItem('token');
        if (token === 'test-token') {
          const response = await axios.post(
            '/lua/permission.lua',
            new URLSearchParams({
              act: 'get_group',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (
            response.data &&
            response.data.group &&
            response.data.group.length > 0
          ) {
            // 筛选出临时管理组（gid=2）的菜单权限
            const tempGroup = response.data.group.find(
              (group) => group.gid === 2
            );
            if (tempGroup && tempGroup.menu && Array.isArray(tempGroup.menu)) {
              const permissions = tempGroup.menu.map((item) => item.resource);
              this.setPermissions(permissions);

              // 清除旧菜单并刷新菜单配置
              appStore.clearServerMenu();
              await appStore.fetchServerMenuConfig(forceRefresh);
              return;
            }
          }
          this.setPermissions([]);
          return;
        }

        // 如果是超级管理员，设置相应权限
        if (this.role && this.role.includes('超级管理员')) {
          const response = await axios.post(
            '/lua/permission.lua',
            new URLSearchParams({
              act: 'get_menu',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data) {
            // 递归获取所有菜单项的ID
            const getAllMenuIds = (menuItems: any[]): string[] => {
              let ids: string[] = [];
              menuItems.forEach((item) => {
                // 从resource属性获取权限ID
                if (item.resource) {
                  ids.push(item.resource);
                }
                // 兼容处理，如果有id属性也获取
                if (item.id) {
                  ids.push(item.id);
                }
                // 递归获取子菜单的权限
                if (item.submenu) {
                  ids = ids.concat(getAllMenuIds(item.submenu));
                }
                if (item.children) {
                  ids = ids.concat(getAllMenuIds(item.children));
                }
                if (item.action) {
                  ids = ids.concat(getAllMenuIds(item.action));
                }
              });
              return ids;
            };

            const allPermissions = getAllMenuIds(response.data.menu);
            this.setPermissions(allPermissions);

            // 清除旧菜单并刷新菜单配置
            appStore.clearServerMenu();
            await appStore.fetchServerMenuConfig(forceRefresh);
          }
        } else {
          console.log('--------------');
          // 非admin用户获取指定权限
          const response = await axios.post(
            '/lua/permission.lua',
            new URLSearchParams({
              act: 'get_group',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (
            response.data &&
            response.data.group &&
            response.data.group.length > 0
          ) {
            // 从权限组中提取所有resource
            const permissions = response.data.group.reduce(
              (acc: string[], group: any) => {
                if (group.menu && Array.isArray(group.menu)) {
                  const resources = group.menu.map(
                    (item: any) => item.resource
                  );
                  return [...acc, ...resources];
                }
                return acc;
              },
              []
            );

            // 设置用户权限
            this.setPermissions(permissions);

            // 清除旧菜单并刷新菜单配置
            appStore.clearServerMenu();
            await appStore.fetchServerMenuConfig(forceRefresh);
          } else {
            this.setPermissions([]);
          }
        }
      } catch (error) {
        console.error('获取用户权限失败:', error);
        throw error;
      }
    },

    // 获取用户信息
    async info() {
      // 获取用户ID
      const userId = localStorage.getItem('userId');
      if (!userId) {
        throw new Error('未找到用户ID');
      }

      // 设置基本用户信息
      this.setInfo({
        name: `用户${userId}`,
        accountId: userId,
      });

      // 获取用户菜单权限
      // await this.fetchUserPermissions(true);

      return this.userInfo;
    },
  },
});

export default useUserStore;
