#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大麦网高级抢票脚本 - 增强版
支持多线程、智能重试、实时监控等高级功能
"""

import time
import json
import random
import requests
import threading
from concurrent.futures import ThreadPoolExecutor
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from datetime import datetime, timedelta
import logging
import os
import sys
from dataclasses import dataclass
from typing import List, Dict, Optional
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('damai_advanced.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

@dataclass
class TicketConfig:
    """票务配置数据类"""
    username: str
    password: str
    target_url: str
    target_price: str
    ticket_num: int
    viewers: List[str]
    start_time: str
    max_attempts: int
    headless: bool
    thread_count: int = 1
    retry_interval: float = 0.5
    email_notify: bool = False
    email_config: Dict = None

class AdvancedDamaiGrabber:
    """高级大麦抢票器"""
    
    def __init__(self, config: TicketConfig):
        self.config = config
        self.success_count = 0
        self.total_attempts = 0
        self.start_timestamp = None
        self.lock = threading.Lock()
        
    def create_driver(self) -> webdriver.Chrome:
        """创建浏览器驱动实例"""
        chrome_options = Options()
        
        # 性能优化
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')
        
        # 反检测
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        
        # 随机User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        chrome_options.add_argument(f'--user-agent={random.choice(user_agents)}')
        
        if self.config.headless:
            chrome_options.add_argument('--headless')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver

    def wait_for_start_time(self):
        """等待开抢时间"""
        if not self.config.start_time:
            return
            
        target_time = datetime.strptime(self.config.start_time, "%Y-%m-%d %H:%M:%S")
        current_time = datetime.now()
        
        if current_time < target_time:
            wait_seconds = (target_time - current_time).total_seconds()
            logging.info(f"等待开抢时间，剩余: {wait_seconds:.1f} 秒")
            
            # 精确等待
            if wait_seconds > 60:
                time.sleep(wait_seconds - 60)
                wait_seconds = 60
            
            if wait_seconds > 10:
                time.sleep(wait_seconds - 10)
                wait_seconds = 10
            
            # 最后10秒精确等待
            while wait_seconds > 0:
                time.sleep(0.1)
                wait_seconds -= 0.1
                if wait_seconds <= 0:
                    break

    def smart_login(self, driver: webdriver.Chrome) -> bool:
        """智能登录"""
        try:
            logging.info(f"[{threading.current_thread().name}] 开始登录...")
            driver.get("https://passport.damai.cn/login")
            
            wait = WebDriverWait(driver, 10)
            
            # 选择密码登录
            try:
                password_tab = wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '密码登录')]"))
                )
                password_tab.click()
                time.sleep(1)
            except:
                pass
            
            # 输入凭据
            username_input = wait.until(EC.presence_of_element_located((By.ID, "fm-login-id")))
            username_input.clear()
            username_input.send_keys(self.config.username)
            
            password_input = driver.find_element(By.ID, "fm-login-password")
            password_input.clear()
            password_input.send_keys(self.config.password)
            
            # 点击登录
            login_btn = driver.find_element(By.XPATH, "//button[contains(@class, 'fm-button') and contains(text(), '登录')]")
            login_btn.click()
            
            time.sleep(3)
            
            # 处理验证码
            try:
                slider = driver.find_element(By.CLASS_NAME, "nc_iconfont")
                if slider:
                    logging.warning(f"[{threading.current_thread().name}] 需要滑块验证，请手动完成")
                    # 这里可以集成自动滑块识别
                    time.sleep(10)  # 给用户时间手动完成
            except:
                pass
            
            # 验证登录状态
            try:
                wait.until(EC.url_contains("damai.cn"))
                logging.info(f"[{threading.current_thread().name}] 登录成功")
                return True
            except:
                logging.error(f"[{threading.current_thread().name}] 登录失败")
                return False
                
        except Exception as e:
            logging.error(f"[{threading.current_thread().name}] 登录异常: {e}")
            return False

    def fast_grab_ticket(self, driver: webdriver.Chrome) -> bool:
        """快速抢票核心逻辑"""
        try:
            wait = WebDriverWait(driver, 5)
            
            # 跳转到目标页面
            driver.get(self.config.target_url)
            time.sleep(1)
            
            # 快速选择票档
            if self.config.target_price:
                try:
                    price_element = driver.find_element(By.XPATH, f"//div[contains(text(), '{self.config.target_price}')]")
                    price_element.click()
                    time.sleep(0.2)
                except:
                    pass
            
            # 选择票数
            for i in range(self.config.ticket_num):
                try:
                    plus_btn = driver.find_element(By.XPATH, "//span[contains(@class, 'plus')]")
                    plus_btn.click()
                    time.sleep(0.1)
                except:
                    break
            
            # 立即购买
            buy_selectors = [
                "//button[contains(text(), '立即购买')]",
                "//button[contains(text(), '立即预订')]",
                "//a[contains(text(), '立即购买')]",
                "//div[contains(@class, 'buy-btn')]//button"
            ]
            
            for selector in buy_selectors:
                try:
                    buy_btn = driver.find_element(By.XPATH, selector)
                    if buy_btn.is_enabled():
                        driver.execute_script("arguments[0].click();", buy_btn)
                        time.sleep(1)
                        break
                except:
                    continue
            
            # 快速选择观演人
            if self.config.viewers:
                for viewer in self.config.viewers:
                    try:
                        viewer_checkbox = driver.find_element(
                            By.XPATH, f"//label[contains(text(), '{viewer}')]//input"
                        )
                        if not viewer_checkbox.is_selected():
                            driver.execute_script("arguments[0].click();", viewer_checkbox)
                            time.sleep(0.1)
                    except:
                        continue
            else:
                # 选择第一个观演人
                try:
                    first_viewer = driver.find_element(By.XPATH, "//input[@type='checkbox'][1]")
                    if not first_viewer.is_selected():
                        driver.execute_script("arguments[0].click();", first_viewer)
                except:
                    pass
            
            # 提交订单
            submit_selectors = [
                "//button[contains(text(), '确认订单')]",
                "//button[contains(text(), '提交订单')]",
                "//button[contains(text(), '确认购买')]"
            ]
            
            for selector in submit_selectors:
                try:
                    submit_btn = driver.find_element(By.XPATH, selector)
                    if submit_btn.is_enabled():
                        driver.execute_script("arguments[0].click();", submit_btn)
                        time.sleep(2)
                        break
                except:
                    continue
            
            # 检查结果
            current_url = driver.current_url
            page_source = driver.page_source
            
            if "trade.damai.cn" in current_url or "支付" in driver.title:
                with self.lock:
                    self.success_count += 1
                logging.info(f"🎉 [{threading.current_thread().name}] 抢票成功！")
                self.send_notification("抢票成功", f"线程 {threading.current_thread().name} 抢票成功，请尽快支付！")
                return True
            
            if any(keyword in page_source for keyword in ["售罄", "暂无票", "已售完"]):
                logging.warning(f"[{threading.current_thread().name}] 票已售罄")
                return False
            
            return False
            
        except Exception as e:
            logging.error(f"[{threading.current_thread().name}] 抢票异常: {e}")
            return False

    def send_notification(self, subject: str, message: str):
        """发送邮件通知"""
        if not self.config.email_notify or not self.config.email_config:
            return
        
        try:
            msg = MimeMultipart()
            msg['From'] = self.config.email_config['from']
            msg['To'] = self.config.email_config['to']
            msg['Subject'] = subject
            
            msg.attach(MimeText(message, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(self.config.email_config['smtp_server'], self.config.email_config['smtp_port'])
            server.starttls()
            server.login(self.config.email_config['username'], self.config.email_config['password'])
            server.send_message(msg)
            server.quit()
            
            logging.info("邮件通知发送成功")
        except Exception as e:
            logging.error(f"邮件通知发送失败: {e}")

    def worker_thread(self, thread_id: int):
        """工作线程"""
        driver = None
        try:
            driver = self.create_driver()
            
            # 登录
            if not self.smart_login(driver):
                return False
            
            # 等待开抢时间
            self.wait_for_start_time()
            
            # 开始抢票
            for attempt in range(self.config.max_attempts):
                with self.lock:
                    self.total_attempts += 1
                
                logging.info(f"[线程{thread_id}] 第 {attempt + 1} 次尝试")
                
                if self.fast_grab_ticket(driver):
                    return True
                
                # 随机间隔
                time.sleep(random.uniform(0.3, self.config.retry_interval))
                
                # 如果其他线程已成功，停止当前线程
                if self.success_count > 0:
                    logging.info(f"[线程{thread_id}] 其他线程已成功，停止尝试")
                    break
            
            return False
            
        except Exception as e:
            logging.error(f"[线程{thread_id}] 工作线程异常: {e}")
            return False
        finally:
            if driver:
                driver.quit()

    def run_multi_thread(self):
        """多线程抢票"""
        self.start_timestamp = time.time()
        
        logging.info(f"启动 {self.config.thread_count} 个抢票线程")
        
        with ThreadPoolExecutor(max_workers=self.config.thread_count) as executor:
            futures = []
            for i in range(self.config.thread_count):
                future = executor.submit(self.worker_thread, i + 1)
                futures.append(future)
            
            # 等待所有线程完成
            results = [future.result() for future in futures]
        
        # 统计结果
        end_time = time.time()
        duration = end_time - self.start_timestamp
        
        logging.info(f"抢票结束，耗时: {duration:.2f}秒")
        logging.info(f"总尝试次数: {self.total_attempts}")
        logging.info(f"成功次数: {self.success_count}")
        
        if self.success_count > 0:
            logging.info("🎉 抢票成功！")
            return True
        else:
            logging.info("😞 抢票失败")
            return False

def load_advanced_config() -> TicketConfig:
    """加载高级配置"""
    try:
        with open('config_advanced.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        return TicketConfig(**data)
    except FileNotFoundError:
        # 创建默认配置文件
        default_config = {
            "username": "your_username",
            "password": "your_password", 
            "target_url": "https://detail.damai.cn/item.htm?id=xxxxxx",
            "target_price": "380",
            "ticket_num": 1,
            "viewers": [],
            "start_time": "2024-01-15 10:00:00",
            "max_attempts": 100,
            "headless": False,
            "thread_count": 3,
            "retry_interval": 0.5,
            "email_notify": False,
            "email_config": {
                "smtp_server": "smtp.qq.com",
                "smtp_port": 587,
                "username": "<EMAIL>",
                "password": "your_email_password",
                "from": "<EMAIL>",
                "to": "<EMAIL>"
            }
        }
        
        with open('config_advanced.json', 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        logging.info("已创建默认配置文件 config_advanced.json，请修改后重新运行")
        sys.exit(1)

def main():
    """主函数"""
    print("🎫 大麦网高级抢票脚本 v2.0")
    print("=" * 50)
    
    config = load_advanced_config()
    grabber = AdvancedDamaiGrabber(config)
    
    success = grabber.run_multi_thread()
    
    if success:
        print("🎉 抢票成功！请尽快完成支付！")
    else:
        print("😞 抢票失败，请重试或检查配置")

if __name__ == "__main__":
    main()
