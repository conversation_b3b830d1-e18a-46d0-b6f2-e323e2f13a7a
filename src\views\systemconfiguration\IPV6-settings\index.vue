<!-- 5G网络接入 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.rate-settings']" />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane
        key="1"
        title="PassAddr"
        v-if="hasPermission('bindMultiplePort')"
      >
        <PassAddr :active="activeTabKey === '1'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane
        key="2"
        title="Free address"
        v-if="hasPermission('bindMultiplePort')"
      >
        <FreeAddr :active="activeTabKey === '2'" />
      </a-tab-pane>
      <!-- 标签页3 -->
      <a-tab-pane
        key="3"
        title="IPV6 Protocol Stack"
        v-if="hasPermission('bindMultiplePort')"
      >
        <IPV6Protocol :active="activeTabKey === '3'" />
      </a-tab-pane>
      <!-- 标签页4 -->
      <a-tab-pane
        key="4"
        title="dhcp-relay"
        v-if="hasPermission('bindMultiplePort')"
      >
        <dhcprelay :active="activeTabKey === '4'" />
      </a-tab-pane>
      <!-- 标签页5 -->
      <a-tab-pane
        key="5"
        title="固定分配地址"
        v-if="hasPermission('bindMultiplePort')"
      >
        <fixedaddress :active="activeTabKey === '5'" />
      </a-tab-pane>
      <!-- 标签页6 -->
      <a-tab-pane
        key="6"
        title="全局option"
        v-if="hasPermission('bindMultiplePort')"
      >
        <Globaloptions :active="activeTabKey === '6'" />
      </a-tab-pane>
      <!-- 标签页7 -->
      <a-tab-pane
        key="7"
        title="地址池option"
        v-if="hasPermission('bindMultiplePort')"
      >
        <Addrpool :active="activeTabKey === '7'" />
      </a-tab-pane>
      <!-- 标签页8 -->
      <a-tab-pane
        key="8"
        title="IP-地址池"
        v-if="hasPermission('bindMultiplePort')"
      >
        <multidns :active="activeTabKey === '8'" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import PassAddr from './pass-addr.vue';
  import FreeAddr from './free-address.vue';

  export default defineComponent({
    components: {
      PassAddr,
      FreeAddr,
    },
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const activeTabKey = ref('1');

      const handleTabChange = (key: string) => {
        activeTabKey.value = key;
      };

      return {
        activeTabKey,
        handleTabChange,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
</style>
