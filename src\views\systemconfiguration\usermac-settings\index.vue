<!-- IPV6设置 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.rate-settings']" />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane
        key="1"
        title="Whitelist"
        v-if="hasPermission('bindMultiplePort')"
      >
        <whitelist :active="activeTabKey === '1'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane
        key="2"
        title="Blacklist"
        v-if="hasPermission('bindMultiplePort')"
      >
        <blacklist :active="activeTabKey === '2'" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import whitelist from './white-list.vue';
  import blacklist from './black-list.vue';

  export default defineComponent({
    components: {
      whitelist,
      blacklist,
    },
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const activeTabKey = ref('1');

      const handleTabChange = (key: string) => {
        activeTabKey.value = key;
      };

      return {
        activeTabKey,
        handleTabChange,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
</style>
