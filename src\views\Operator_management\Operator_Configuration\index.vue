<!-- 操作员配置组件 -->
<template>
  <div v-if="isComponentVisible" class="container">
    <div v-if="isRefreshing" class="overlay">
      <div class="loader"></div>
    </div>
    <Breadcrumb
      :items="['menu.Operator management', 'menu.Operator Configuration']"
    />
    <a-card class="general-card" :title="$t('menu.Operator Configuration')">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="{ searchQuery }"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="number" :label="$t('登录账号')">
                  <a-input
                    v-model="searchQuery"
                    :placeholder="$t('searchTable.form.number.placeholder')"
                  />
                  <a-button
                    type="primary"
                    @click="filterData"
                    style="margin-left: 10px"
                  >
                    <template #icon>
                      <icon-search />
                    </template>
                    {{ $t('searchTable.form.search') }}
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-col :flex="'86px'" style="text-align: right"> </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <!-- 新建按钮 -->
            <a-button
              id="operatorsAdd"
              :disabled="!hasPermission('operatorsAdd')"
              type="primary"
              @click="showModal"
            >
              <template #icon>
                <icon-plus />
              </template>
              {{ $t('searchTable.operation.create') }}
            </a-button>
            <a-upload action="/">
              <template #upload-button>
                <!-- <a-button>
                  {{ $t('searchTable.operation.import') }}
                </a-button> -->
              </template>
            </a-upload>
          </a-space>
        </a-col>
        <!-- 右侧按钮 -->
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip :content="$t('searchTable.actions.refresh')">
            <div class="action-icon" @click="handleRefresh">
              <icon-refresh size="18" />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>
      <!-- 列表 -->
      <a-table
        :columns="columns4"
        :data="paginatedData"
        style="margin-top: 20px"
        :pagination="false"
      >
        <template #userid="{ record }">
          <span>{{ record.userid }}</span>
        </template>

        <template #username="{ record }">
          <span>{{ record.username }}</span>
        </template>

        <template #role="{ record }">
          <span>{{ record.role }}</span>
        </template>

        <template #effect="{ record }">
          <span>{{ record.effect }}</span>
        </template>

        <template #effectdate="{ record }">
          <span>{{ record.effectdate }}</span>
        </template>

        <template #option="{ record }">
          <a-space>
            <a-button
              id="operatorsedit"
              type="primary"
              :disabled="
                (record.userid === 'admin' &&
                  userStore.userInfo?.accountId != 'admin') ||
                !hasPermission('operatorsedit')
              "
              style="margin-right: 8px"
              @click="showEditModal(record)"
            >
              <template #icon>
                <icon-edit />
              </template>
              <template #default>编辑</template>
            </a-button>
            <a-button
              id="operatorsDelete"
              :disabled="
                record.userid === 'admin' ||
                !hasPermission('operatorsDelete')
              "
              type="primary"
              status="danger"
              @click="showDeleteConfirm(record)"
            >
              <template #icon>
                <icon-delete />
              </template>
              <template #default>删除</template>
            </a-button>
          </a-space>
        </template>
      </a-table>
      
      <a-pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="filteredData.length"
        show-total
        show-size-changer
        show-jumper
        show-page-size
        style="margin-top: 20px"
      />
      
    </a-card>

    <!-- 新建管理员模态框 -->
    <a-modal
      v-model:visible="isModalVisible"
      :title="modalMode === 'add' ? '新建管理员' : '编辑管理员'"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form :model="formData" :rules="formRules" ref="formRef">
        <a-form-item label="登录账号" field="userid">
          <a-input v-model="formData.userid" :disabled="modalMode === 'edit'" />
        </a-form-item>
        <a-form-item label="登录名" field="username">
          <a-input v-model="formData.username" />
        </a-form-item>
        <template v-if="modalMode === 'add'">
          <a-form-item label="密码" field="password">
            <a-input v-model="formData.password" type="password" />
          </a-form-item>
        </template>
        <template v-else>
          <a-form-item label="新密码" field="new_password">
            <a-input
              v-model="formData.new_password"
              type="password"
              placeholder="不修改密码请留空"
            />
          </a-form-item>
          <a-form-item label="确认新密码" field="confirm_password">
            <a-input
              v-model="formData.confirm_password"
              type="password"
              placeholder="不修改密码请留空"
            />
          </a-form-item>
        </template>
        <a-form-item label="权限组" field="role">
          <a-select v-model="formData.role" :options="roleOptions" />
        </a-form-item>
        <a-form-item label="是否生效" field="effect">
          <a-select v-model="formData.effect" :options="effectOptions" />
        </a-form-item>
        <a-form-item label="失效时间" field="effectdate">
          <a-date-picker v-model="formData.effectdate" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 删除确认弹窗 -->
    <a-modal
      v-model:visible="isDeleteConfirmVisible"
      title="确认删除"
      @ok="confirmDelete"
      @cancel="cancelDelete"
    >
      <p>确定要删除管理员 "{{ deleteRecord.username }}" 吗？此操作不可撤销。</p>
    </a-modal>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed
  } from 'vue';

  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import axios from 'axios';
  import {
    handleNotification1,
    handleNotification2,
  } from '../../../utils/info';

  interface OperatorData {
    userid: string;
    username: string;
    password?: string;
    role: string;
    effect: string;
    effectdate: string;
  }

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const columns4 = [
        {
          title: '登录账号',
          dataIndex: 'userid',
          slotName: 'userid',
        },
        {
          title: '用户名',
          dataIndex: 'username',
          slotName: 'username',
        },
        {
          title: '权限组',
          dataIndex: 'role',
          slotName: 'role',
        },
        {
          title: '是否生效',
          dataIndex: 'effect',
          slotName: 'effect',
        },
        {
          title: '失效时间',
          dataIndex: 'effectdate',
          slotName: 'effectdate',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];

      const data = ref<OperatorData[]>([]);
      const filteredData = ref<OperatorData[]>([]);
      const roleOptions = ref<{ label: string; value: string }[]>([]);

      // 获取权限组数据
      const fetchRoleOptions = async () => {
        try {
          const response = await axios.post(
            '/lua/permission.lua',
            new URLSearchParams({
              act: 'get_group',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            const groupData = response.data.data?.group || [];
            roleOptions.value = groupData.map((group: any) => ({
              label: group.name,
              value: group.gid.toString(),
            }));
          } else {
            Message.error(response.data?.result || '获取权限组失败');
          }
        } catch (error) {
          console.error('获取权限组失败:', error);
          Message.error('获取权限组失败');
        }
      };

      const effectOptions = [
        { label: '生效', value: '1' },
        { label: '不生效', value: '0' },
      ];

      const isModalVisible = ref(false);
      const modalMode = ref<'add' | 'edit'>('add');
      const formData = reactive({
        userid: '',
        username: '',
        password: '',
        new_password: '',
        confirm_password: '',
        role: '',
        effect: '1',
        effectdate: '',
        old_userid: '', // 用于编辑时保存原账号
      });

      // 删除确认相关变量
      const isDeleteConfirmVisible = ref(false);
      const deleteRecord = ref<OperatorData>({} as OperatorData);

      const formRules = {
        userid: [{ required: true, message: '登录账号不能为空' }],
        username: [{ required: true, message: '登录名不能为空' }],
        password: [
          {
            required: true,
            message: '密码不能为空',
            validator: (value: string, cb: any) => {
              if (modalMode.value === 'add' && !value) {
                return cb('密码不能为空');
              }
              return cb();
            },
          },
        ],
        new_password: [
          {
            validator: (value: string, cb: any) => {
              if (value && value !== formData.confirm_password) {
                return cb('两次输入的密码不一致');
              }
              return cb();
            },
          },
        ],
        confirm_password: [
          {
            validator: (value: string, cb: any) => {
              if (value && value !== formData.new_password) {
                return cb('两次输入的密码不一致');
              }
              return cb();
            },
          },
        ],
        role: [{ required: true, message: '权限组不能为空' }],
        effect: [{ required: true, message: '是否生效不能为空' }],
        effectdate: [{ required: true, message: '失效时间不能为空' }],
      };

      const formRef = ref();
      const isComponentVisible = ref(true);
      const isRefreshing = ref(false);
      const searchQuery = ref('');

      const currentPage = ref(1);
      const pageSize = ref(10);

      const paginatedData = computed(() => {
        const start = (currentPage.value - 1) * pageSize.value;
        return filteredData.value.slice(start, start + pageSize.value);
      });

      // 获取所有管理员数据
      const fetchData = async () => {
        try {
          isRefreshing.value = true;
          const response = await axios.post(
            '/lua/operator.lua',
            new URLSearchParams({
              act: 'get',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 确保已加载权限组选项
            if (roleOptions.value.length === 0) {
              await fetchRoleOptions();
            }

            // 转换数据中的role值到对应的标签名称
            const processedData = (response.data.data || []).map(
              (item: OperatorData) => {
                // 查找匹配的角色选项
                const roleOption = roleOptions.value.find(
                  (option) => option.value === item.role
                );
                if (roleOption) {
                  return {
                    ...item,
                    role: roleOption.label,
                  };
                }
                return item;
              }
            );

            data.value = processedData;
            filteredData.value = [...data.value];
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Fetch error:', error);
          Message.error('获取数据失败');
        } finally {
          isRefreshing.value = false;
        }
      };

      // 删除管理员
      const deleteRow = async (record: OperatorData) => {
        try {
          if (!hasPermission('operatorsDelete')) {
            Message.error('您没有权限');
            return;
          }
          const response = await axios.post(
            '/lua/operator.lua',
            new URLSearchParams({
              act: 'settings',
              set_type: 'del',
              userid: record.userid,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData(); // 重新获取数据
            Message.success('删除成功');
          } else {
            Message.error(response.data.result || '删除失败');
          }
        } catch (error) {
          console.error('Delete error:', error);
          Message.error('删除失败');
        }
      };

      // 显示删除确认弹窗
      const showDeleteConfirm = (record: OperatorData) => {
        if (!hasPermission('operatorsDelete')) {
          Message.error('您没有权限');
          return;
        }
        deleteRecord.value = record;
        isDeleteConfirmVisible.value = true;
      };

      // 确认删除
      const confirmDelete = () => {
        deleteRow(deleteRecord.value);
        isDeleteConfirmVisible.value = false;
      };

      // 取消删除
      const cancelDelete = () => {
        isDeleteConfirmVisible.value = false;
      };

      const showModal = () => {
        if (!hasPermission('operatorsAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 重置表单数据
        modalMode.value = 'add';
        formData.userid = '';
        formData.username = '';
        formData.password = '';
        formData.new_password = '';
        formData.confirm_password = '';
        formData.role = '';
        formData.effect = '1';
        formData.effectdate = '';
        formData.old_userid = '';
        isModalVisible.value = true;
      };

      // 显示编辑模态框
      const showEditModal = (record: OperatorData) => {
        if (!hasPermission('operatorsedit')) {
          Message.error('您没有权限');
          return;
        }
        modalMode.value = 'edit';
        formData.old_userid = record.userid;
        formData.userid = record.userid;
        formData.username = record.username;
        formData.password = '';
        formData.new_password = '';
        formData.confirm_password = '';

        // 查找匹配的权限组选项
        const roleOption = roleOptions.value.find(
          (option) => option.label === record.role
        );
        formData.role = roleOption ? roleOption.value : record.role; // 如果找到匹配项则使用gid值，否则保持原值

        formData.effect = record.effect;
        formData.effectdate = record.effectdate;
        isModalVisible.value = true;
      };

      // 处理表单提交（新增或编辑）
      const handleOk = async () => {
        try {
          await formRef.value.validate();

          const params: Record<string, string> = {
            act: 'settings',
            set_type: modalMode.value === 'add' ? 'add' : 'edit',
            userid: formData.userid,
            username: formData.username,
            role: formData.role,
            effect: formData.effect,
            effectdate: formData.effectdate,
          };

          if (modalMode.value === 'add') {
            params.password = formData.password;
          } else {
            params.old_userid = formData.old_userid;

            // 只有当新密码不为空时才添加密码相关参数
            if (formData.new_password) {
              params.new_password = formData.new_password;
              params.confirm_password = formData.confirm_password;
            }
          }

          const response = await axios.post(
            '/lua/operator.lua',
            new URLSearchParams(params),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData(); // 重新获取数据
            Message.success(
              modalMode.value === 'add' ? '添加成功' : '编辑成功'
            );
            isModalVisible.value = false;
          } else {
            Message.error(
              response.data.result ||
                (modalMode.value === 'add' ? '添加失败' : '编辑失败')
            );
          }
        } catch (error) {
          console.error(
            modalMode.value === 'add' ? 'Add error:' : 'Edit error:',
            error
          );
          Message.error(
            modalMode.value === 'add'
              ? '添加失败，请检查表单'
              : '编辑失败，请检查表单'
          );
        }
      };

      const handleCancel = () => {
        isModalVisible.value = false;
      };

      const handleRefresh = () => {
        fetchData().then(() => {
          Message.success('刷新成功');
        });
      };

      const filterData = () => {
        if (searchQuery.value.trim() === '') {
          filteredData.value = [...data.value];
          Message.error('请填写登录账号');
          return;
        }

        filteredData.value = data.value.filter((item) =>
          item.userid.includes(searchQuery.value)
        );

        if (filteredData.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      onMounted(() => {
        fetchData();
        fetchRoleOptions(); // 添加获取权限组的调用
      });

      return {
        columns4,
        data,
        filteredData,
        roleOptions,
        effectOptions,
        handleNotification1,
        handleNotification2,
        deleteRow,
        isModalVisible,
        modalMode,
        formData,
        formRules,
        formRef,
        showModal,
        showEditModal,
        handleOk,
        handleCancel,
        isComponentVisible,
        handleRefresh,
        isRefreshing,
        searchQuery,
        filterData,
        // 删除确认相关
        isDeleteConfirmVisible,
        deleteRecord,
        showDeleteConfirm,
        confirmDelete,
        cancelDelete,
        hasPermission,
        userStore,        
        paginatedData,
        currentPage,
        pageSize,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
  }
</style>
