<template>
  <div class="container">
    <Breadcrumb
      :items="['系统控制', '网络诊断']"
    />
    <a-card title="">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-row :gutter="20" :style="{ marginBottom: '20px' }">
          <a-col :span="8">
            <a-card
              title="show-arp命令"
              :bordered="false"
              :style="{ width: '100%' }"
            >
              <div class="session">
                <span>IP地址：</span>
                  
                <a-input
                  class="input-field"                  
                  :style="{ width: '100%' }"
                  v-model="formData.arp_ip"
                  :status="arpipError ? 'error' : undefined"
                />
                <div v-if="arpipError" class="error-message">{{ arpipError }}</div>
                <a-button type="primary" @click="showArp" :disabled="!hasPermission('arpClick')">
                  提交
                </a-button>           
              </div>               
              <a-descriptions style="margin-top: 20px" :data="arpData" title="结果" :column="1" bordered/>
              
            </a-card>
          </a-col>
          
          <a-col :span="8">
            <a-card
              title="ping命令"
              :bordered="false"
              :style="{ width: '100%', height: '50%' }"
            >
              
              <div class="session">
                <span>IP地址：</span>
                  
                <a-input
                  class="input-field"
                  :style="{ width: '100%' }"
                  v-model="formData.ping_ip"
                  :status="pingipError ? 'error' : undefined"
                />
                <div v-if="pingipError" class="error-message">{{ pingipError }}</div>
                <a-button type="primary" @click="ping" :disabled="!hasPermission('pingClick')">
                  提交
                </a-button>           
              </div>
              <a-descriptions style="margin-top: 20px" :data="pingData" title="结果" :column="1" bordered/>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card
              title="ping6命令"
              :bordered="false"
              :style="{ width: '100%', height: '50%' }"
            >
              <div class="session">
                <span>IPv6地址：</span>
                  
                <a-input
                  class="input-field"
                  :style="{ width: '100%' }"
                  v-model="formData.ping_ipv6"
                  :status="pingip6Error ? 'error' : undefined"
                />
                <div v-if="pingip6Error" class="error-message">{{ pingip6Error }}</div>
                <a-button type="primary" @click="ping6" :disabled="!hasPermission('ping6Click')">
                  提交
                </a-button>           
              </div>
              <a-descriptions style="margin-top: 20px" :data="ping6Data" title="结果" :column="1" bordered/>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, reactive } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import usePermission from '@/hooks/permission';
  import axios from 'axios';

  import { isValidIPv4,isValidIPv6 } from "@/utils/validate";

  const { hasPermission } = usePermission();

  export default defineComponent({
    setup() {
      
      const arpipError = ref('');
      const pingipError = ref('');
      const pingip6Error = ref('');
      const formData = reactive({
        arp_ip: '',
        ping_ip: '',
        ping_ipv6: '',
      });
            
      const arpData = ref<any[]>([]); 
      const pingData = ref<any[]>([]); 
      const ping6Data = ref<any[]>([]); 
      
      const showArp = async () => {
        if (!formData.arp_ip) {
          arpipError.value = 'IP地址不能为空';
          return false;
        } else if (!isValidIPv4(formData.arp_ip)) {
          arpipError.value = 'IP地址格式不正确，应为：***********';
          return false;
        } else {
          arpipError.value = '';
        }
        
        try {
          const response = await axios.post(
            '/lua/system_control.lua',
            new URLSearchParams({ 
              act: 'network_diagnosis',
              act_type: 'arp',
              ip: formData.arp_ip
            }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );
          // console.log(response);
          if (response.data.code === 200) {
            let data = [];
            
            data.push({'label': '状态', 'value': response.data.data.active});
            data.push({'label': 'MAC地址', 'value': response.data.data.mac});   
            data.push({'label': '过期时间(秒)', 'value': response.data.data.expire});
            data.push({'label': 'ipv6地址状态', 'value': response.data.data.sesActive});
            data.push({'label': '用户ipv6地址', 'value': response.data.data.ipv6_addr});
                
            arpData.value = data;
          } else {
            Message.error({
              content: response.data.err,
            });
          }
        } catch (error) {
          console.error('Fetch error:', error);
          Message.error('获取数据失败');
        }
      }; 
      
      const ping = async () => {
        if (!formData.ping_ip) {
          pingipError.value = 'IP地址不能为空';
          return false;
        } else if (!isValidIPv4(formData.ping_ip)) {
          pingipError.value = 'IP地址格式不正确，应为：***********';
          return false;
        } else {
          pingipError.value = '';
        }
        
        try {
          const response = await axios.post(
            '/lua/system_control.lua',
            new URLSearchParams({ 
              act: 'network_diagnosis',
              act_type: 'ping',
              ip: formData.ping_ip
            }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );
          // console.log(response);
          if (response.data.code === 200) {
            let data = [];
            
            data.push({'label': '接受回应时间(毫秒)', 'value': response.data.data.ms});
                
            pingData.value = data;
          } else {
            Message.error({
              content: response.data.err,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };  
            
      const ping6 = async () => {
        if (!formData.ping_ipv6) {
          pingip6Error.value = 'IPv6地址不能为空';
          return false;
        } else if (!isValidIPv6(formData.ping_ipv6)) {
          pingip6Error.value = 'IPv6地址格式不正确';
          return false;
        } else {
          pingip6Error.value = '';
        }
        
        try {
          const response = await axios.post(
            '/lua/system_control.lua',
            new URLSearchParams({ 
              act: 'network_diagnosis',
              act_type: 'ping_ip6',
              ip: formData.ping_ipv6
            }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );
          // console.log(response);
          if (response.data.code === 200) {
            let data = [];
            
            data.push({'label': '接受回应时间(毫秒)', 'value': response.data.data.ms6});
                
            ping6Data.value = data;
          } else {
            Message.error({
              content: response.data.err,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };               

      return {
        formData,
        arpData,
        hasPermission,
        arpipError,
        showArp,
        pingipError,
        ping,
        pingData,
        pingip6Error,
        ping6,
        ping6Data,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .hint {
    flex: 1; /* 占据剩余空间 */
    text-align: left;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }
  
  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px; /* 设置子元素之间的间距 */
    // margin-left: 25%;
    margin-top: 5%;
  }

  /* 为上传按钮添加居中样式 */
  .upload {
    display: block;
    margin: 0 auto;
  }

  /* 为按钮添加居中样式 */
  .button {
    text-align: center;
  }
  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .arco-input-wrapper {
    margin: 10px 0 !important;
  }
  
  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>