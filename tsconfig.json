{"compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "node", "noImplicitAny": false, "checkJs": false, "strictNullChecks": false, "strict": false, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "lib": ["es2020", "dom"], "skipLibCheck": true, "allowJs": true}, "include": ["src/**/*", "src/**/*.vue"], "exclude": ["node_modules"]}