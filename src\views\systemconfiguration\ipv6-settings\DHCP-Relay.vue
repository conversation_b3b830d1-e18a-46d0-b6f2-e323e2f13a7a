<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 3 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item
        :rules="rules.dhcp_relay_local_addr"
        field="dhcp_relay_local_addr"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">IPv6 dhcp relay local address: </span>
        </template>
        <a-input
          v-model="formData.dhcp_relay_local_addr"
          :style="{ width: '200px' }"
          placeholder=""
        />
      </a-form-item>
      <a-form-item
        :rules="rules.dhcp_relay_srv_addr"
        field="dhcp_relay_srv_addr"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">IPv6 dhcp relay server address: </span>
        </template>
        <a-input
          v-model="formData.dhcp_relay_srv_addr"
          :style="{ width: '200px' }"
          placeholder=""
        />
      </a-form-item>
      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          style="margin-top: 2%"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>Submit</template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      const formData = reactive({
        dhcp_relay_local_addr: '',
        dhcp_relay_srv_addr: '',
      });
      const rules = {
        dhcp_relay_local_addr: [
          { required: true, message: 'DHCP中继本地地址不能为空' },
        ],
        dhcp_relay_srv_addr: [
          { required: true, message: 'DHCP中继服务器地址不能为空' },
        ],
      };

      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_ipv6_setting.lua',
            new URLSearchParams({ act: 'relay' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            const data = response.data.data;
            formData.dhcp_relay_local_addr = data.dhcp_relay_local_addr || '';
            formData.dhcp_relay_srv_addr = data.dhcp_relay_srv_addr || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          // Arco Design表单验证失败时会返回errors对象
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_ipv6_setting.lua',
            new URLSearchParams({
              act: 'relay',
              act_type: 'mod',
              dhcp_relay_local_addr: String(formData.dhcp_relay_local_addr),
              dhcp_relay_srv_addr: String(formData.dhcp_relay_srv_addr),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        saveAction,
        hasPermission,
        formRef,
        rules,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
