<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="24">
        <a-space>
          <!-- 新建按钮 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            Add
          </a-button>

          <a-button @click="handleDelete">
            <template #icon>
              <icon-delete />
            </template>
            Delete
          </a-button>

          <a-button @click="handleImport">
            <template #icon>
              <icon-import />
            </template>
            Import
          </a-button>

          <a-button @click="handleExportResult">
            <template #icon>
              <icon-export />
            </template>
            Export Result
          </a-button>

          <a-button @click="handleExportAll">
            <template #icon>
              <icon-export />
            </template>
            Export all whitelist
          </a-button>

          <!-- 刷新按钮 -->
          <a-button @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            Activate
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <!-- 列表 -->
    <a-table
      row-key="mac"
      :columns="columns"
      :data="data"
      style="margin-top: 20px"
      :pagination="false"
      :row-selection="rowSelection"
    >
      <template #operate="{ record }">
        <a-space>
          <a-button type="primary" size="small" @click="editRecord(record)">
            Edit
          </a-button>
          <a-button
            type="primary"
            size="small"
            status="danger"
            @click="deleteRecord(record)"
          >
            Delete
          </a-button>
        </a-space>
      </template>
    </a-table>
  </a-card>

  <!-- 添加/编辑MAC地址模态框 -->
  <a-modal
    v-model:visible="isModalVisible"
    title="Add MAC Address"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
    :width="800"
  >
    <a-form :model="formData" :rules="rules" ref="formRef">
      <a-form-item label="MAC Address" field="mac">
        <a-input
          v-model="formData.mac"
          style="width: 300px"
          placeholder="请输入MAC地址"
        />
      </a-form-item>
      <a-form-item label="Name" field="name">
        <a-input
          v-model="formData.name"
          style="width: 300px"
          placeholder="请输入名称"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 导入黑名单模态框 -->
  <a-modal
    v-model:visible="isImportModalVisible"
    title="Import blacklist"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleImportConfirm"
    @cancel="handleImportCancel"
    :width="600"
  >
    <div>
      <div style="margin-bottom: 16px">
        <label style="display: block; margin-bottom: 8px; font-weight: 500"
          >Import file</label
        >
        <a-upload
          ref="uploadRef"
          :file-list="fileList"
          :auto-upload="false"
          :show-file-list="false"
          @change="handleFileChange"
          accept=".txt,.csv"
        >
          <a-button>
            <template #icon>
              <icon-upload />
            </template>
            Choose File
          </a-button>
        </a-upload>
        <span v-if="selectedFileName" style="margin-left: 10px; color: #666">
          {{ selectedFileName }}
        </span>
      </div>

      <div style="margin-bottom: 16px">
        <div style="font-weight: 500; margin-bottom: 8px">Note:</div>
        <div style="color: #666; font-size: 14px; line-height: 1.5">
          1. Only import content files separated by spaces is supported
          <a-link style="margin-left: 4px" @click="downloadSample">
            (Sample download)
          </a-link>
          <br />
          2. MAC addresses cannot be repeated, and only new ones are supported
          <br />
          3. The username cannot have spaces
        </div>
      </div>

      <div>
        <div style="font-weight: 500; margin-bottom: 8px"
          >Format description</div
        >
        <a-textarea
          v-model="formatDescription"
          :rows="8"
          readonly
          placeholder="MAC Name"
          style="background-color: #f7f8fa; resize: none"
        />
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns = [
        {
          title: 'MAC',
          dataIndex: 'mac',
          align: 'center' as const,
          width: 200,
        },
        {
          title: 'Name',
          dataIndex: 'name',
          align: 'center' as const,
          width: 200,
        },
        {
          title: 'Operate',
          dataIndex: 'operate',
          slotName: 'operate',
          align: 'center' as const,
          width: 150,
        },
      ];

      const data = ref<
        {
          mac: string;
          name: string;
        }[]
      >([]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);
      const isImportModalVisible = ref(false);

      // 导入相关数据
      const fileList = ref([]);
      const selectedFileName = ref('');
      const formatDescription = ref(
        '00-00-00-8B-3E-76 name1\n00-00-00-8A-C9-20 name1\n00-00-00-8A-74-13 name2'
      );
      const uploadRef = ref();

      // 表格选择
      const selectedRowKeys = ref<string[]>([]);
      const rowSelection = reactive({
        type: 'checkbox' as const,
        showCheckedAll: true,
        onChange: (rowKeys: string[]) => {
          selectedRowKeys.value = rowKeys;
        },
      });

      // 表单数据
      const formData = reactive({
        mac: '', // MAC地址
        name: '', // 名称
      });

      // 表单规则
      const rules = {
        mac: [{ required: true, message: 'MAC地址不能为空' }],
        name: [{ required: true, message: '名称不能为空' }],
      };

      const formRef = ref();

      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/user_mac.lua',
            new URLSearchParams({ act: 'get_white_list' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              data.value = response.data.data;
            } else {
              data.value = [];
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
            data.value = [];
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
          data.value = [];
        }
      };

      // 激活配置
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 删除选中项
      const handleDelete = () => {
        if (selectedRowKeys.value.length === 0) {
          Message.warning('请选择要删除的项');
          return;
        }

        // 实际应调用删除API
        // 这里仅做前端删除
        data.value = data.value.filter(
          (item) => !selectedRowKeys.value.includes(item.mac)
        );
        selectedRowKeys.value = [];
        Message.success('删除成功');
      };

      // 导入
      const handleImport = () => {
        isImportModalVisible.value = true;
      };

      // 文件选择处理
      const handleFileChange = (fileList: any) => {
        if (fileList.length > 0) {
          selectedFileName.value = fileList[0].name;
        } else {
          selectedFileName.value = '';
        }
      };

      // 下载样例文件
      const downloadSample = () => {
        const content =
          '00-00-00-8B-3E-76 name1\n00-00-00-8A-C9-20 name1\n00-00-00-8A-74-13 name2';
        const blob = new Blob([content], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'sample.txt';
        a.click();
        window.URL.revokeObjectURL(url);
      };

      // 导入确认
      const handleImportConfirm = (done: any) => {
        if (!selectedFileName.value) {
          Message.warning('请选择要导入的文件');
          done(false);
          return;
        }

        // 这里应该处理文件上传和解析逻辑
        Message.success('导入成功');
        isImportModalVisible.value = false;
        done(true);
      };

      // 导入取消
      const handleImportCancel = () => {
        selectedFileName.value = '';
        fileList.value = [];
        isImportModalVisible.value = false;
      };

      // 导出结果
      const handleExportResult = () => {
        Message.info('导出结果功能暂未实现');
      };

      // 导出全部热点
      const handleExportAll = () => {
        Message.info('导出全部热点功能暂未实现');
      };

      // 显示模态框
      const showModal = () => {
        // 重置表单
        formData.mac = '';
        formData.name = '';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleBeforeOk = (done: any) => {
        formRef.value.validate().then((errors: any) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 添加到列表
          data.value.push({
            mac: formData.mac,
            name: formData.name,
          });

          Message.success('添加成功');
          done(true); // 允许模态框关闭
        });
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      // 编辑记录
      const editRecord = (record: { mac: string; name: string }) => {
        formData.mac = record.mac;
        formData.name = record.name;
        isModalVisible.value = true;
      };

      // 删除单个记录
      const deleteRecord = (record: { mac: string; name: string }) => {
        const index = data.value.findIndex((item) => item.mac === record.mac);
        if (index > -1) {
          data.value.splice(index, 1);
          Message.success('删除成功');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        handleRefresh,
        isRefreshing,
        formData,
        rules,
        formRef,
        isModalVisible,
        showModal,
        handleBeforeOk,
        handleCancel,
        handleDelete,
        handleImport,
        handleExportResult,
        handleExportAll,
        rowSelection,
        selectedRowKeys,
        editRecord,
        deleteRecord,
        // 导入相关
        isImportModalVisible,
        fileList,
        selectedFileName,
        formatDescription,
        uploadRef,
        handleFileChange,
        downloadSample,
        handleImportConfirm,
        handleImportCancel,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
