<!-- 映射出口组件 -->
<template>
  <div v-if="isComponentVisible" class="container">
    <div v-if="isRefreshing" class="overlay">
      <div class="loader"></div>
    </div>
    <Breadcrumb :items="['menu.system-configuration', 'menu.Map_export']" />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane key="1" title="绑定出口名" v-if="hasPermission('bindExitName')">
        <a-card class="general-card" :title="$t('menu.Map_export')">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="10">
                    <a-form-item field="number" :label="$t('映射出口地址')">
                      <a-input
                        v-model="searchQuery"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        style="margin-left: 10px"
                        @click="filterData"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <a-col :span="12">
              <a-space>
                <!-- 新建按钮 -->
                <a-button
                  id="bindExitNameAdd"
                  type="primary"
                  :disabled="!hasPermission('bindExitNameAdd')"
                  @click="showModal"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  {{ $t('searchTable.operation.create') }}
                </a-button>
                <a-upload action="/">
                  <template #upload-button>
                    <!-- <a-button>
                  {{ $t('searchTable.operation.import') }}
                </a-button> -->
                  </template>
                </a-upload>
              </a-space>
            </a-col>
            <!-- 右侧按钮 -->
            <a-col
              :span="12"
              style="display: flex; align-items: center; justify-content: end"
            >
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>

          <!-- 列表 -->
          <a-table
            :columns="columns9"
            :data="filteredData"
            column-resizable
            style="margin-top: 20px"
            :pagination="false"
          >
            <!-- 删除键位 -->
            <template #option="{ record }">
              <a-button
                id="bindExitNameDelete"
                type="primary"
                :disabled="!hasPermission('bindExitNameDelete')"
                @click="deleteRow(record)"
              >
                <template #icon>
                  <IconEye />
                </template>
                <template #default>删除</template>
              </a-button>
            </template>
          </a-table>
        </a-card>
        <a-modal
          v-model:visible="isModalVisible"
          title="新建"
          width="40%"
          draggable
          :mask-closable="false"
          :unmount-on-close="false"
          @before-ok="handleOk"
          @cancel="handleCancel"
        >
          <a-form :model="formData" :rules="rules" ref="formRef">
            <a-form-item label="映射出口地址" field="ip">
              <a-input v-model="formData.ip" />
            </a-form-item>
            <a-form-item label="绑定出口名" field="bind_name">
              <a-select
                v-model="formData.bind_name"
                :options="exitNameOptions"
                @change="handleExitNameChange"
              />
            </a-form-item>
            <a-form-item v-if="false" label="组ID" field="groupId">
              <a-input v-model="formData.groupId" disabled />
            </a-form-item>
          </a-form>
        </a-modal>
      </a-tab-pane>

      <a-tab-pane key="2" title="绑定拨号出口名" v-if="hasPermission('bindDialExitName')">
        <a-card class="general-card" :title="$t('menu.Map_export')">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery2 }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item field="number" :label="$t('绑定出口名')">
                      <a-input
                        v-model="searchQuery2"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        @click="filterData2"
                        style="margin-left: 10px"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <a-col :span="12">
              <a-space>
                <!-- 新建按钮 -->
                <a-button
                  id="bindDialExitNameAdd"
                  :disabled="!hasPermission('bindDialExitNameAdd')"
                  type="primary"
                  @click="showModal2"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  {{ $t('searchTable.operation.create') }}
                </a-button>
                <a-upload action="/">
                  <template #upload-button> </template>
                </a-upload>
              </a-space>
            </a-col>
            <!-- 右侧按钮 -->
            <a-col
              :span="12"
              style="display: flex; align-items: center; justify-content: end"
            >
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh2">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>

          <!-- 列表 -->
          <a-table
            :columns="columns10"
            :data="filteredData2"
            column-resizable
            style="margin-top: 20px"
            :pagination="false"
          >
            <!-- 绑定出口名列 -->
            <template #bind_name="{ record }">
              <span>{{ record.bind_name }}</span>
            </template>

            <!-- 操作列 -->
            <template #option="{ record }">
              <a-button
                id="bindDialExitNameDelete"
                type="primary"
                :disabled="!hasPermission('bindDialExitNameDelete')"
                @click="deleteRow2(record)"
              >
                <template #icon>
                  <IconEye />
                </template>
                <template #default>删除</template>
              </a-button>
            </template>
          </a-table>
        </a-card>

        <a-modal
          v-model:visible="isModalVisible2"
          title="新建"
          width="40%"
          draggable
          @ok="handleOk2"
          @cancel="handleCancel2"
        >
          <a-form :model="formData2" :rules="rules2" ref="formRef2">
            <a-form-item label="绑定出口名" field="bind_name">
              <a-select
                v-model="formData2.bind_name"
                :options="exitNameOptions"
                @change="handleExitNameChange2"
              />
            </a-form-item>
            <a-form-item v-if="false" label="组ID" field="groupId">
              <a-input v-model="formData2.groupId" disabled />
            </a-form-item>
          </a-form>
        </a-modal>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
  } from 'vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import {
    handleNotification1,
    handleNotification2,
  } from '../../../utils/info';

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      // 定义options的类型为OptionValue，明确其结构
      const optionsip = ref([
        { label: '0', value: '0' },
        { label: '1', value: '1' },
        { label: '2', value: '2' },
        { label: '3', value: '3' },
      ]);

      // 定义出口名选项
      const exitNameOptions = ref<
        Array<{ label: string; value: string; groupId: string }>
      >([]);

      // 定义固定地址出口选项
      const fixedExitNameOptions = ref<
        Array<{ label: string; value: string; groupId: string }>
      >([]);

      // 定义拨号认证出口选项
      const dialExitNameOptions = ref<
        Array<{ label: string; value: string; groupId: string }>
      >([]);
      // 验证IP地址格式
      const validateIpAddress = (ip: string): boolean => {
        const ipRegex =
          /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
      };
      const columns9 = [
        { title: '映射出口地址', dataIndex: 'addr', slotName: 'addr' },
        { title: '绑定出口名', dataIndex: 'bind_name', slotName: 'bind_name' },
        { title: '操作', dataIndex: 'option', slotName: 'option' },
      ];
      const columns10 = [
        { title: '绑定出口名', dataIndex: 'bind_name', slotName: 'bind_name' },
        { title: '操作', dataIndex: 'option', slotName: 'option' },
      ];

      // 数据存储
      const data = ref<any[]>([]);
      const data2 = ref<any[]>([]);
      const filteredData = ref<any[]>([]);
      const filteredData2 = ref<any[]>([]);

      const isModalVisible = ref(false);
      const isModalVisible2 = ref(false);
      const isComponentVisible = ref(true);
      const isRefreshing = ref(false);
      const currentTab = ref('1');
      const activeTabKey = ref('1');

      const formData = reactive({
        ip: '',
        bind_name: '',
        groupId: '',
      });

      const formData2 = reactive({
        bind_name: '',
        groupId: '',
      });

      const rules = {
        ip: [
          { required: true, message: '映射出口地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateIpAddress(value)) {
                callback('映射出口地址格式不正确，应为：***********');
              } else {
                callback();
              }
            },
          },
        ],
        bind_name: [{ required: true, message: '绑定出口名不能为空' }],
      };

      const rules2 = {
        bind_name: [{ required: true, message: '绑定出口名不能为空' }],
      };

      const formRef = ref();
      const formRef2 = ref();
      const searchQuery = ref('');
      const searchQuery2 = ref('');

      // 获取第一个标签页数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'map_agent' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            data.value = response.data.data || [];
            filteredData.value = [...data.value];
            console.log('映射出口数据更新:', data.value);
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取映射出口数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 获取第二个标签页数据
      const fetchData2 = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'map_agent_oeclient' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            data2.value = response.data.data || [];
            filteredData2.value = [...data2.value];
            console.log('绑定出口名数据更新:', data2.value);
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取绑定出口名数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 获取出口名列表
      const fetchExitNames = async () => {
        try {
          // 创建两个用于存储出口名选项的数组
          let fixedExitOptions: Array<{
            label: string;
            value: string;
            groupId: string;
          }> = [];
          let dialExitOptions: Array<{
            label: string;
            value: string;
            groupId: string;
          }> = [];

          // 获取固定地址出口数据
          const response1 = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'multi_output' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response1.data.code === 200) {
            fixedExitOptions = response1.data.data.map((item: any) => ({
              label: item.out_name || '',
              value: item.out_name || '',
              groupId: item.group_id || '',
            }));
            console.log('固定出口名选项更新:', fixedExitOptions);
          }

          // 获取拨号认证出口数据
          const response2 = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'multi_output_oeclient' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response2.data.code === 200) {
            dialExitOptions = response2.data.data.map((item: any) => ({
              label: item.out_name || '',
              value: item.out_name || '',
              groupId: item.group_id || '',
            }));
            console.log('拨号出口名选项更新:', dialExitOptions);
          }

          // 根据当前活动标签选择对应的选项数据
          if (activeTabKey.value === '1') {
            exitNameOptions.value = fixedExitOptions;
          } else {
            exitNameOptions.value = dialExitOptions;
          }

          // 存储两种出口选项，以便在标签切换时使用
          fixedExitNameOptions.value = fixedExitOptions;
          dialExitNameOptions.value = dialExitOptions;
        } catch (error) {
          console.error('获取出口名列表失败:', error);
          Message.error('获取出口名列表失败');
        }
      };

      // 第一个标签页的删除处理
      const deleteRow = async (record: {
        addr: string;
        bind_name: string;
        group_id?: string;
      }) => {
        try {
          if (!hasPermission('bindExitNameDelete')) {
            Message.error('您没有权限');
            return;
          }
          const { addr, bind_name: bindName, group_id: recordGroupId } = record;

          // 查找对应的组ID
          let groupId = recordGroupId || '';
          if (!groupId) {
            // 如果记录中没有group_id，尝试从选项中查找
            const selectedExit = fixedExitNameOptions.value.find(
              (item) => item.value === bindName
            );
            if (selectedExit) {
              groupId = selectedExit.groupId;
              console.log('查找到的组ID:', groupId);
            }
          }

          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'output',
              type_act: 'map_agent_del',
              out_name: bindName,
              ip: addr,
              group_id: groupId,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData();
            Message.success('删除成功');
          } else {
            Message.error(response.data.err || '删除失败');
          }
        } catch (error) {
          console.error('删除映射出口失败:', error);
          Message.error('删除失败');
        }
      };

      // 第二个标签页的删除处理
      const deleteRow2 = async (record: {
        bind_name: string;
        group_id?: string;
      }) => {
        try {
          if (!hasPermission('bindDialExitNameDelete')) {
            Message.error('您没有权限');
            return;
          }
          const { bind_name: bindName, group_id: recordGroupId } = record;

          // 查找对应的组ID
          let groupId = recordGroupId || '';
          if (!groupId) {
            // 如果记录中没有group_id，尝试从选项中查找
            const selectedExit = dialExitNameOptions.value.find(
              (item) => item.value === bindName
            );
            if (selectedExit) {
              groupId = selectedExit.groupId;
              console.log('查找到的组ID:', groupId);
            }
          }

          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'output',
              type_act: 'map_agent_oeclient_del',
              out_name: bindName,
              group_id: groupId,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData2();
            Message.success('删除成功');
          } else if (
            response.data.err &&
            response.data.err.includes('output bind agent-address and rdr')
          ) {
            // 特定错误消息处理
            Message.error(
              '该出口名有绑定的映射关系，请先删除映射关系再删除出口名'
            );
          } else {
            Message.error(response.data.err || '删除失败');
          }
        } catch (error) {
          console.error('删除绑定出口名失败:', error);
          Message.error('删除失败');
        }
      };

      const showModal = () => {
        if (!hasPermission('bindExitNameAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 确保第一个标签页使用固定出口名选项
        exitNameOptions.value = fixedExitNameOptions.value;
        isModalVisible.value = true;
      };

      const showModal2 = () => {
        if (!hasPermission('bindDialExitNameAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 确保第二个标签页使用拨号出口名选项
        exitNameOptions.value = dialExitNameOptions.value;
        isModalVisible2.value = true;
      };

      // 第一个标签页的添加处理
      const handleOk = async (done) => {
        if (!hasPermission('bindExitNameAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 只通过表单验证规则验证
        formRef.value.validate((errors) => {
          if (errors) {
            // 表单验证失败
            Message.error('表单验证失败，请检查输入');
            done(false); // 阻止模态框关闭
            return;
          }

          const { ip, bind_name: bindName, groupId } = formData;

          axios
            .post(
              '/lua/set_cfg.lua',
              new URLSearchParams({
                act: 'output',
                type_act: 'map_agent_add',
                out_name: bindName,
                ip,
                group_id: groupId,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )

            .then((response) => {
              if (response.data.code === 200) {
                fetchData();
                Message.success('添加成功');
                isModalVisible.value = false;
                formData.ip = '';
                formData.bind_name = '';
                formData.groupId = '';
                done(true); // 允许模态框关闭
                isModalVisible.value = false;
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });
        return false;
      };

      // 第二个标签页的添加处理
      const handleOk2 = async () => {
        try {
          if (!hasPermission('bindDialExitNameAdd')) {
            Message.error('您没有权限');
            return;
          }
          await formRef2.value.validate();

          const { bind_name: bindName, groupId } = formData2;

          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'output',
              type_act: 'map_agent_oeclient_add',
              out_name: bindName,
              group_id: groupId,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData2();
            Message.success('添加成功');
            isModalVisible2.value = false;
            formData2.bind_name = '';
            formData2.groupId = '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('添加绑定出口名失败:', error);
          Message.error('请填写完整信息');
        }
      };

      const handleCancel = () => {
        isModalVisible.value = false;
      };

      const handleCancel2 = () => {
        isModalVisible2.value = false;
      };

      // 检查标签页权限并找到第一个可用的标签页
      const findFirstAvailableTab = () => {
        const tabPermissions = [
          { key: '1', permission: 'bindExitName' },
          { key: '2', permission: 'bindDialExitName' },
        ];

        const availableTab = tabPermissions.find((tab) =>
          hasPermission(tab.permission)
        );

        if (availableTab) {
          return availableTab.key;
        }

        // 如果没有任何标签有权限，可能需要显示一个提示或者空白页面
        Message.error('您没有权限');
        return null;
      };

      // 添加标签页切换处理函数
      const handleTabChange = (key: string) => {
        // 检查用户是否有权限访问目标标签页
        let hasAccess = false;

        if (key === '1') {
          hasAccess = hasPermission('bindExitName');
        } else if (key === '2') {
          hasAccess = hasPermission('bindDialExitName');
        }

        if (!hasAccess) {
          Message.error('您没有权限');

          // 尝试找到下一个有权限的标签页
          const newKey = findFirstAvailableTab();
          if (newKey && newKey !== activeTabKey.value) {
            activeTabKey.value = newKey;
          }
        } else {
          // 有权限访问，更新当前活动标签
          activeTabKey.value = key;

          // 根据当前标签页设置对应的选项数据
          if (key === '1') {
            exitNameOptions.value = fixedExitNameOptions.value;
            fetchData();
          } else if (key === '2') {
            exitNameOptions.value = dialExitNameOptions.value;
            fetchData2();
          }
        }
      };
      // 刷新第一个标签页数据
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 刷新第二个标签页数据
      const handleRefresh2 = () => {
        isRefreshing.value = true;
        fetchData2().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 第一个标签页的过滤处理
      const filterData = () => {
        if (searchQuery.value.trim() === '') {
          filteredData.value = [...data.value];
          Message.error('请填写映射出口地址');
          return;
        }

        filteredData.value = data.value.filter(
          (item) =>
            item.addr.includes(searchQuery.value) ||
            item.bind_name.includes(searchQuery.value)
        );

        if (filteredData.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      // 第二个标签页的过滤处理
      const filterData2 = () => {
        if (searchQuery2.value.trim() === '') {
          filteredData2.value = [...data2.value];
          Message.error('请填写绑定出口名');
          return;
        }

        filteredData2.value = data2.value.filter((item) =>
          item.bind_name.includes(searchQuery2.value)
        );

        if (filteredData2.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      // 处理出口名变化
      const handleExitNameChange = (value: string) => {
        // 根据选择的出口名找到对应的组ID
        const selectedExit = exitNameOptions.value.find(
          (item) => item.value === value
        );
        if (selectedExit) {
          formData.groupId = selectedExit.groupId;
          console.log('出口名变化，对应组ID:', formData.groupId);
        } else {
          formData.groupId = '';
        }
      };

      // 处理出口名变化 - 第二个表单
      const handleExitNameChange2 = (value: string) => {
        // 根据选择的出口名找到对应的组ID
        const selectedExit = exitNameOptions.value.find(
          (item) => item.value === value
        );
        if (selectedExit) {
          formData2.groupId = selectedExit.groupId;
          console.log('出口名变化 (表单2)，对应组ID:', formData2.groupId);
        } else {
          formData2.groupId = '';
        }
      };

      // 组件加载时获取数据和初始化标签页
      onMounted(() => {
        // 获取出口名选项
        fetchExitNames().then(() => {
          // 查找第一个有权限的标签页
          const firstAvailableTab = findFirstAvailableTab();

          if (firstAvailableTab) {
            activeTabKey.value = firstAvailableTab;

            // 根据初始标签页设置对应的选项数据
            if (firstAvailableTab === '1') {
              exitNameOptions.value = fixedExitNameOptions.value;
              fetchData();
            } else if (firstAvailableTab === '2') {
              exitNameOptions.value = dialExitNameOptions.value;
              fetchData2();
            }
          }
        });
      });

      onBeforeUnmount(() => {});

      return {
        columns9,
        columns10,
        data,
        data2,
        handleTabChange,
        currentTab,
        activeTabKey,
        exitNameOptions,
        handleExitNameChange,
        handleExitNameChange2,
        handleNotification1,
        handleNotification2,
        deleteRow,
        deleteRow2,
        isModalVisible,
        isModalVisible2,
        formData,
        formData2,
        rules,
        rules2,
        formRef,
        formRef2,
        showModal,
        showModal2,
        handleOk,
        handleOk2,
        handleCancel,
        handleCancel2,
        isComponentVisible,
        handleRefresh,
        handleRefresh2,
        isRefreshing,
        searchQuery,
        searchQuery2,
        filterData,
        filterData2,
        filteredData,
        filteredData2,
        optionsip,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
</style>
