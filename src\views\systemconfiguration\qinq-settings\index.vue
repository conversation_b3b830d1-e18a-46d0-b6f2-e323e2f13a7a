<!-- Qinq控制 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.rate-settings']" />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane
        key="1"
        title="功能开关"
        v-if="hasPermission('bindMultiplePort')"
      >
        <Function :active="activeTabKey === '1'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane
        key="2"
        title="Vlan白名单"
        v-if="hasPermission('bindMultiplePort')"
      >
        <VlanWhitelist :active="activeTabKey === '2'" />
      </a-tab-pane>
      <!-- 标签页3 -->
      <a-tab-pane
        key="3"
        title="mac黑名单"
        v-if="hasPermission('bindMultiplePort')"
      >
        <MacBlacklist :active="activeTabKey === '3'" />
      </a-tab-pane>
      <!-- 标签页4 -->
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  import Function from './function.vue';
  import VlanWhitelist from './vlan-whitelist.vue';
  import MacBlacklist from './mac-blacklist.vue';

  export default defineComponent({
    components: {
      Function,
      VlanWhitelist,
      MacBlacklist,
    },
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const activeTabKey = ref('1');

      const handleTabChange = (key: string) => {
        activeTabKey.value = key;
      };

      return {
        activeTabKey,
        handleTabChange,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
</style>
