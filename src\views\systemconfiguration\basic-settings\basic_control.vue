<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="web_setup" class="uniform-form-item">
          <template #label>
            <span class="form-label">页面选路</span>
          </template>
          <a-tooltip content="是否支持调用本机cgi" position="tl" background-color="#3491FA">
            <a-select :style="{width:'100px'}" v-model="formData.web_setup">
              <a-option value="enable">是</a-option>
              <a-option value="disable">否</a-option>
            </a-select>
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="same_name_share_bw" class="uniform-form-item">
          <template #label>
            <span class="form-label">共享带宽</span>
          </template>
          <a-tooltip content="相同的用户名是否共享带宽" position="tl" background-color="#3491FA">
            <a-select :style="{width:'100px'}" v-model="formData.same_name_share_bw">
              <a-option value="enable">是</a-option>
              <a-option value="disable">否</a-option>
            </a-select>
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="force_fc_none" class="uniform-form-item">
          <template #label>
            <span class="form-label">端口流控</span>
          </template>
          <a-tooltip content="配置需要重启服务" position="tl" background-color="#3491FA">
            <a-select :style="{width:'100px'}" v-model="formData.force_fc_none">
              <a-option value="enable">启动</a-option>
              <a-option value="disable">关闭</a-option>
            </a-select>
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="check_pass_idle" class="uniform-form-item">
          <template #label>
            <span class="form-label">直通用户闲置检查</span>
          </template>
          <a-tooltip content="是否检查pass session(直通会话)" position="tl" background-color="#3491FA">
            <a-select :style="{width:'100px'}" v-model="formData.check_pass_idle">
              <a-option value="enable">是</a-option>
              <a-option value="disable">否</a-option>
            </a-select>
          </a-tooltip>
        </a-form-item>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" class="form-row">      
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="macname_auth" class="uniform-form-item">
          <template #label>
            <span class="form-label">启动MAC地址认证</span>
          </template>
          <a-tooltip content="是否启动用户MAC地址作为用户名认证" position="tl" background-color="#3491FA">
            <a-select :style="{width:'100px'}" v-model="formData.macname_auth">
              <a-option value="enable">是</a-option>
              <a-option value="disable">否</a-option>
            </a-select>
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="free_intra_flow" class="uniform-form-item">
          <template #label>
            <span class="form-label">内网流量免计费</span>
          </template>
          <a-select :style="{width:'100px'}" v-model="formData.free_intra_flow">
            <a-option value="enable">是</a-option>
            <a-option value="disable">否</a-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="debug_level" class="uniform-form-item">
          <template #label>
            <span class="form-label">调试pppoe拨号开关</span>
          </template>
          <a-select :style="{width:'100px'}" v-model="formData.debug_level">
            <a-option value="enable">开启</a-option>
            <a-option value="disable">关闭</a-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="chk_mac" class="uniform-form-item">
          <template #label>
            <span class="form-label">启动检查会话mac是否匹配</span>
          </template>
          <a-select :style="{width:'100px'}" v-model="formData.chk_mac">
            <a-option value="enable">是</a-option>
            <a-option value="disable">否</a-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" class="form-row">      
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="chk_vlan" class="uniform-form-item">
          <template #label>
            <span class="form-label">启动检查会话vlan是否匹配</span>
          </template>
          <a-select :style="{width:'100px'}" v-model="formData.chk_vlan">
            <a-option value="enable">是</a-option>
            <a-option value="disable">否</a-option>
          </a-select>
        </a-form-item>
      </a-col>      
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="ext_create_ses" class="uniform-form-item">
          <template #label>
            <span class="form-label">portal认证主动创建会话</span>
          </template>
          <a-tooltip content="如果没有会话是否创建" position="tl" background-color="#3491FA">
            <a-select :style="{width:'100px'}" v-model="formData.ext_create_ses">
              <a-option value="enable">是</a-option>
              <a-option value="disable">否</a-option>
            </a-select>
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="pre_vlan_format" class="uniform-form-item">
          <template #label>
            <span class="form-label">兼容老的后台系统的vlan数据格式</span>
          </template>
          <a-select :style="{width:'100px'}" v-model="formData.pre_vlan_format">
            <a-option value="enable">是</a-option>
            <a-option value="disable">否</a-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="bandwidth_ctrl_strict" class="uniform-form-item">
          <template #label>
            <span class="form-label">严格控制带宽</span>
          </template>
          <a-select :style="{width:'100px'}" v-model="formData.bandwidth_ctrl_strict">
            <a-option value="enable">开启</a-option>
            <a-option value="disable">关闭</a-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item label="" field="force_link_mac" class="uniform-form-item">
          <template #label>
            <span class="form-label">强制用户接入为2层接入</span>
          </template>
          <a-tooltip content="桥接模式下有时需要指定" position="tl" background-color="#3491FA">
            <a-select :style="{width:'100px'}" v-model="formData.force_link_mac">
              <a-option value="enable">是</a-option>
              <a-option value="disable">否</a-option>
            </a-select>
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="mac_match_dhcp" class="uniform-form-item">
          <template #label>
            <span class="form-label">启动检查用户地址匹配dhcp分配地址</span>
          </template>
          <a-select :style="{width:'100px'}" v-model="formData.mac_match_dhcp">
            <a-option value="enable">开启</a-option>
            <a-option value="disable">关闭</a-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :xs="22" :sm="10" :md="10" :lg="6">
        <a-form-item field="acct_flow_user" class="uniform-form-item">
          <template #label>
            <span class="form-label">统计数据包方向</span>
          </template>
          <a-tooltip content="流量计费用户方向" position="tl" background-color="#3491FA">
            <a-select :style="{width:'100px'}" v-model="formData.acct_flow_user">
              <a-option value="enable">开启</a-option>
              <a-option value="disable">关闭</a-option>
            </a-select>
          </a-tooltip>
        </a-form-item>
      </a-col>
    </a-row>
    
    <a-button :disabled="!hasPermission('BasicControlSubmit')" type="primary" style="margin-top: 2%"
      @click="saveAction">
      <template #icon>
        <icon-check />
      </template>
      <template #default>提交</template>
    </a-button>
  </a-card>

</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed,
    watch
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from "@/utils/validate";

  export default defineComponent({
    props: {
      active: Boolean
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      const formData = reactive({
        web_setup: 'disable',
        same_name_share_bw: 'disable',
        force_fc_none: 'disable',
        check_pass_idle: 'disable',
        macname_auth: 'disable',
        free_intra_flow: 'disable',
        debug_level: 'disable',
        chk_mac: 'disable',
        chk_vlan: 'disable',
        ext_create_ses: 'disable',
        pre_vlan_format: 'disable',
        bandwidth_ctrl_strict: 'disable',
        force_link_mac: 'disable',
        mac_match_dhcp: 'disable',
        acct_flow_user: 'disable'
      });
      
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_basic_setting.lua',
            new URLSearchParams({ act: 'basic_control' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          // console.log(response);
          
          if (response.data.code === 200) {
            formData.web_setup = response.data.data.web_setup || 'disable';
            formData.same_name_share_bw = response.data.data.same_name_share_bw || 'disable';  
            formData.force_fc_none = response.data.data.force_fc_none || 'disable';
            formData.check_pass_idle = response.data.data.check_pass_idle || 'disable';
            formData.macname_auth = response.data.data.macname_auth || 'disable';
            formData.free_intra_flow = response.data.data.free_intra_flow || 'disable';
            formData.debug_level = response.data.data.debug_level || 'disable';
            formData.chk_mac = response.data.data.chk_mac || 'disable';
            formData.chk_vlan = response.data.data.chk_vlan || 'disable';
            formData.ext_create_ses = response.data.data.ext_create_ses || 'disable';
            formData.pre_vlan_format = response.data.data.pre_vlan_format || 'disable';
            formData.bandwidth_ctrl_strict = response.data.data.bandwidth_ctrl_strict || 'disable';
            formData.force_link_mac = response.data.data.force_link_mac || 'disable';
            formData.mac_match_dhcp = response.data.data.mac_match_dhcp || 'disable';  
            formData.acct_flow_user  = response.data.data.acct_flow_user || 'disable';                     
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        try {
          if (!hasPermission('BasicControlSubmit')) {
            Message.error('您没有权限');
            return;
          }  
                    
          const response = await axios.post(
            '/lua/set_cfg_basic_setting.lua',
            new URLSearchParams({
              act: 'basic_control',
              debug_level: formData.debug_level,
              check_pass_idle: formData.check_pass_idle,
              ext_create_ses: formData.ext_create_ses,
              macname_auth: formData.macname_auth,
              web_setup: formData.web_setup,
              same_name_share_bw: formData.same_name_share_bw,
              free_intra_flow: formData.free_intra_flow,
              pre_vlan_format: formData.pre_vlan_format,
              chk_mac: formData.chk_mac,
              chk_vlan: formData.chk_vlan,
              force_fc_none: formData.force_fc_none,
              bandwidth_ctrl_strict: formData.bandwidth_ctrl_strict,
              force_link_mac: formData.force_link_mac,
              mac_match_dhcp: formData.mac_match_dhcp,
              acct_flow_user: formData.acct_flow_user,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };
      
      watch(() => props.active, (newVal) => {
        if (newVal) {
          fetchData();
        }
      });
      
      onMounted(() => {
        if (props.active) {
          fetchData();
        }        
      });

      return {
        formData,
        saveAction,
        hasPermission,
      };
    },
  });
</script>

<style scoped> 
  .form-row {
    margin-bottom: 16px;
  }
  
  .uniform-form-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .form-label {
    display: inline-block;
    margin-bottom: 2px;
    white-space: normal;
    word-break: break-word;
    width: 100%;
  }

  .uniform-form-item >>> .arco-form-item-wrapper {
    width: 100%;
  }

  /* 中屏幕及以上时改为水平布局 */
  @media (min-width: 768px) {
    .uniform-form-item {
      flex-direction: row;
      align-items: center;
    }
    
    .form-label {
      width: 160px;
      text-align: right;
      padding-right: 1px;
      margin-bottom: 0;
    }
  }

  /* 大屏幕时调整标签宽度 */
  @media (min-width: 1200px) {
    .form-label {
      width: 180px;
    }
  }
  
  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>