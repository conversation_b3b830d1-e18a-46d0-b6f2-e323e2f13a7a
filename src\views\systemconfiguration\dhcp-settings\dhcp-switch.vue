<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- DHCP端口配置表格 -->
    <a-table
      row-key="portLabel"
      :columns="columns"
      :data="data"
      style="margin-top: 20px"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
    >
      <template #port0="{ record }">
        <a-radio-group v-model="record.port0">
          <a-radio value="enable">{{ $t('enable') }}</a-radio>
          <a-radio value="disable">{{ $t('disable') }}</a-radio>
        </a-radio-group>
      </template>
      <template #port1="{ record }">
        <a-radio-group v-model="record.port1">
          <a-radio value="enable">{{ $t('enable') }}</a-radio>
          <a-radio value="disable">{{ $t('disable') }}</a-radio>
        </a-radio-group>
      </template>
      <template #port2="{ record }">
        <a-radio-group v-model="record.port2">
          <a-radio value="enable">{{ $t('enable') }}</a-radio>
          <a-radio value="disable">{{ $t('disable') }}</a-radio>
        </a-radio-group>
      </template>
      <template #port3="{ record }">
        <a-radio-group v-model="record.port3">
          <a-radio value="enable">{{ $t('enable') }}</a-radio>
          <a-radio value="disable">{{ $t('disable') }}</a-radio>
        </a-radio-group>
      </template>
      <template #port4="{ record }">
        <a-radio-group v-model="record.port4">
          <a-radio value="enable">{{ $t('enable') }}</a-radio>
          <a-radio value="disable">{{ $t('disable') }}</a-radio>
        </a-radio-group>
      </template>
      <template #operation>
        <a-button type="primary" @click="handleSubmit">
          <template #icon>
            <icon-edit />
          </template>
          {{ $t('submit') }}
        </a-button>
      </template>
    </a-table>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, watch } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface DhcpPortData {
    portLabel: string;
    port0: string;
    port1: string;
    port2: string;
    port3: string;
    port4: string;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: '端口',
          dataIndex: 'portLabel',
          width: 100,
          align: 'center',
        },
        {
          title: '0',
          dataIndex: 'port0',
          slotName: 'port0',
          width: 150,
          align: 'center',
        },
        {
          title: '1',
          dataIndex: 'port1',
          slotName: 'port1',
          width: 150,
          align: 'center',
        },
        {
          title: '2',
          dataIndex: 'port2',
          slotName: 'port2',
          width: 150,
          align: 'center',
        },
        {
          title: '3',
          dataIndex: 'port3',
          slotName: 'port3',
          width: 150,
          align: 'center',
        },
        {
          title: '4',
          dataIndex: 'port4',
          slotName: 'port4',
          width: 150,
          align: 'center',
        },
        {
          title: '操作',
          slotName: 'operation',
          width: 100,
          align: 'center',
        },
      ];

      const data = ref<DhcpPortData[]>([
        {
          portLabel: 'DHCP',
          port0: 'disable',
          port1: 'disable',
          port2: 'disable',
          port3: 'disable',
          port4: 'disable',
        },
      ]);

      const isLoading = ref(false);
      const isSubmitting = ref(false);

      // 保存原始数据，用于检测是否有更改
      const originalData = ref<DhcpPortData | null>(null);

      // 获取DHCP端口配置
      const fetchData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'dhcp_switch' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 重置所有端口为禁用
            const row = data.value[0];
            row.port0 = 'disable';
            row.port1 = 'disable';
            row.port2 = 'disable';
            row.port3 = 'disable';
            row.port4 = 'disable';

            if (response.data.data && Array.isArray(response.data.data)) {
              // 根据接口数据设置启用状态
              response.data.data.forEach((item: any) => {
                if (item.port && item.dhcp === 'enable') {
                  const portKey = `port${item.port}` as keyof DhcpPortData;
                  if (portKey in row) {
                    (row as any)[portKey] = 'enable';
                  }
                }
              });

              console.log('获取到的DHCP配置数据:', response.data.data);
              console.log('处理后的表格数据:', data.value[0]);

              // 保存原始数据副本
              originalData.value = JSON.parse(JSON.stringify(row));
            }
          } else {
            Message.error({
              content: response.data.err || '获取DHCP配置失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取DHCP配置失败:', error);
          Message.error('获取DHCP配置失败');
        } finally {
          isLoading.value = false;
        }
      };

      // 提交所有端口配置
      const handleSubmit = async () => {
        isSubmitting.value = true;
        try {
          const currentData = data.value[0];
          const changedPorts = [];

          // 检查哪些端口有变化
          [0, 1, 2, 3, 4].forEach((i) => {
            const portKey = `port${i}` as keyof DhcpPortData;
            if (currentData[portKey] !== originalData.value?.[portKey]) {
              changedPorts.push({
                port: i,
                value: currentData[portKey],
              });
            }
          });

          // 创建提交数据
          const formData = new URLSearchParams();
          formData.append('act', 'dhcp_switch_batch');

          // 添加每个变更的端口信息
          changedPorts.forEach((port, index) => {
            formData.append(`port[${index}]`, port.port.toString());
            formData.append(`dhcp[${index}]`, port.value);
          });

          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            formData,
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success('DHCP端口配置已成功提交');
            fetchData(); // 刷新数据以确保状态正确
          } else {
            Message.error({
              content: response.data.err || '提交失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('提交DHCP配置失败:', error);
          Message.error('提交请求失败');
        } finally {
          isSubmitting.value = false;
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        isLoading,
        isSubmitting,
        handleSubmit,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .dhcp-table {
    margin-bottom: 20px;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }

  :deep(.arco-table-th) {
    text-align: center;
    background-color: #f7f8fa;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }

  .submit-container {
    margin-top: 20px;
    text-align: right;
  }
</style>
