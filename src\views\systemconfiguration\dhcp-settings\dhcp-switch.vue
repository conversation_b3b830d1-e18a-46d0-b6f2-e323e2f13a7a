<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-table
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      class="dhcp-table"
    >
      <template #dhcp="{ record }">
        <a-select
          v-model="record.dhcp"
          :style="{ width: '100px' }"
          @change="handleDhcpChange(record)"
        >
          <a-option value="enable">启用</a-option>
          <a-option value="disable">禁用</a-option>
        </a-select>
      </template>
    </a-table>

    <div class="action-buttons">
      <a-button
        type="primary"
        @click="saveAction"
        :disabled="!hasPermission('evrrpSubmit')"
      >
        <template #icon>
          <icon-check />
        </template>
        提交
      </a-button>
    </div>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  export default defineComponent({
    props: {
      active: <PERSON><PERSON><PERSON>,
    },
    setup(props) {
      const { hasPermission } = usePermission();

      const columns = [
        { title: '端口', dataIndex: 'portLabel', width: 100 },
        { title: '0', dataIndex: 'port0', width: 100 },
        { title: '1', dataIndex: 'port1', width: 100 },
        { title: '2', dataIndex: 'port2', width: 100 },
        { title: '3', dataIndex: 'port3', width: 100 },
        { title: '4', dataIndex: 'port4', width: 100 },
      ];

      const tableData = reactive([
        {
          portLabel: 'DHCP',
          port0: 'disable',
          port1: 'disable',
          port2: 'disable',
          port3: 'disable',
          port4: 'disable',
        },
      ]);

      const fetchData = async () => {
        try {
          const response = await axios.post('/lua/get_dhcp_config.lua');

          if (response.data.code === 200 && response.data.data) {
            // 重置所有端口为禁用
            const row = tableData[0];
            row.port0 = 'disable';
            row.port1 = 'disable';
            row.port2 = 'disable';
            row.port3 = 'disable';
            row.port4 = 'disable';

            // 根据接口数据设置启用状态
            response.data.data.forEach((item: any) => {
              const portKey = `port${item.port}`;
              if (row.hasOwnProperty(portKey)) {
                row[portKey] = item.dhcp;
              }
            });
          }
        } catch (error) {
          Message.error('获取DHCP配置失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }

        try {
          const row = tableData[0];
          const dhcpData = [];

          // 构建提交数据
          for (let i = 0; i <= 4; i++) {
            const portKey = `port${i}`;
            dhcpData.push({
              port: i.toString(),
              dhcp: row[portKey],
            });
          }

          const response = await axios.post(
            '/lua/set_dhcp_config.lua',
            { data: dhcpData },
            { headers: { 'Content-Type': 'application/json' } }
          );

          if (response.data.code === 200) {
            Message.success('DHCP配置保存成功');
          } else {
            Message.error(response.data.err || 'DHCP配置保存失败');
          }
        } catch (error) {
          Message.error('保存请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        tableData,
        saveAction,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .dhcp-table {
    margin-bottom: 20px;
  }

  .action-buttons {
    text-align: left;
    margin-top: 20px;
  }

  :deep(.arco-table-th) {
    text-align: center;
    background-color: #f7f8fa;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }
</style>
