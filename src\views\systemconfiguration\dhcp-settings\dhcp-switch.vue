<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-space>
          <a-button
            type="primary"
            @click="saveAction"
            :disabled="!hasPermission('evrrpSubmit')"
            :loading="isSaving"
          >
            <template #icon>
              <icon-check />
            </template>
            保存配置
          </a-button>
        </a-space>
      </a-col>
      <!-- 右侧按钮 -->
      <a-col
        :span="12"
        style="display: flex; align-items: center; justify-content: end"
      >
        <!-- 刷新按钮 -->
        <a-tooltip content="刷新">
          <div class="action-icon" @click="handleRefresh">
            <icon-refresh size="18" />
          </div>
        </a-tooltip>
      </a-col>
    </a-row>

    <!-- DHCP端口配置表格 -->
    <a-table
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
      class="dhcp-table"
    >
      <template #port0="{ record }">
        <a-select
          v-model="record.port0"
          :style="{ width: '100px' }"
          @change="handleDhcpChange"
        >
          <a-option value="enable">启用</a-option>
          <a-option value="disable">禁用</a-option>
        </a-select>
      </template>
      <template #port1="{ record }">
        <a-select
          v-model="record.port1"
          :style="{ width: '100px' }"
          @change="handleDhcpChange"
        >
          <a-option value="enable">启用</a-option>
          <a-option value="disable">禁用</a-option>
        </a-select>
      </template>
      <template #port2="{ record }">
        <a-select
          v-model="record.port2"
          :style="{ width: '100px' }"
          @change="handleDhcpChange"
        >
          <a-option value="enable">启用</a-option>
          <a-option value="disable">禁用</a-option>
        </a-select>
      </template>
      <template #port3="{ record }">
        <a-select
          v-model="record.port3"
          :style="{ width: '100px' }"
          @change="handleDhcpChange"
        >
          <a-option value="enable">启用</a-option>
          <a-option value="disable">禁用</a-option>
        </a-select>
      </template>
      <template #port4="{ record }">
        <a-select
          v-model="record.port4"
          :style="{ width: '100px' }"
          @change="handleDhcpChange"
        >
          <a-option value="enable">启用</a-option>
          <a-option value="disable">禁用</a-option>
        </a-select>
      </template>
    </a-table>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, ref, reactive, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface DhcpPortData {
    portLabel: string;
    port0: string;
    port1: string;
    port2: string;
    port3: string;
    port4: string;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns = [
        { title: '端口', dataIndex: 'portLabel', width: 100 },
        { title: '0', dataIndex: 'port0', slotName: 'port0', width: 100 },
        { title: '1', dataIndex: 'port1', slotName: 'port1', width: 100 },
        { title: '2', dataIndex: 'port2', slotName: 'port2', width: 100 },
        { title: '3', dataIndex: 'port3', slotName: 'port3', width: 100 },
        { title: '4', dataIndex: 'port4', slotName: 'port4', width: 100 },
      ];

      const tableData = reactive<DhcpPortData[]>([
        {
          portLabel: 'DHCP',
          port0: 'disable',
          port1: 'disable',
          port2: 'disable',
          port3: 'disable',
          port4: 'disable',
        },
      ]);

      const isLoading = ref(false);
      const isSaving = ref(false);

      // 获取DHCP端口配置
      const fetchData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'dhcp_switch' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              // 重置所有端口为禁用
              const row = tableData[0];
              row.port0 = 'disable';
              row.port1 = 'disable';
              row.port2 = 'disable';
              row.port3 = 'disable';
              row.port4 = 'disable';

              // 根据接口数据设置启用状态
              response.data.data.forEach((item: any) => {
                const portKey = `port${item.port}` as keyof DhcpPortData;
                if (portKey in row && typeof item.dhcp_switch === 'string') {
                  (row as any)[portKey] = item.dhcp_switch;
                }
              });
            }
          } else {
            Message.error({
              content: response.data.err || '获取DHCP配置失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取DHCP配置失败:', error);
          Message.error('获取DHCP配置失败');
        } finally {
          isLoading.value = false;
        }
      };

      // 刷新数据
      const handleRefresh = () => {
        fetchData().then(() => {
          Message.success('刷新成功');
        });
      };

      // 处理DHCP开关变化
      const handleDhcpChange = () => {
        console.log('DHCP开关变化');
      };

      // 保存DHCP配置
      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }

        isSaving.value = true;
        try {
          const row = tableData[0];
          const submitData = [];

          // 构建提交数据
          for (let i = 0; i <= 4; i++) {
            const portKey = `port${i}` as keyof DhcpPortData;
            submitData.push({
              port: i.toString(),
              dhcp_switch: row[portKey],
            });
          }

          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'dhcp_switch',
              data: JSON.stringify(submitData),
            }),
            {
              headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            }
          );

          if (response.data.code === 200) {
            Message.success('DHCP配置保存成功');
            // 重新获取数据以确保同步
            await fetchData();
          } else {
            Message.error({
              content: response.data.err || 'DHCP配置保存失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('保存DHCP配置失败:', error);
          Message.error('保存请求失败');
        } finally {
          isSaving.value = false;
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        tableData,
        isLoading,
        isSaving,
        handleRefresh,
        handleDhcpChange,
        saveAction,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .dhcp-table {
    margin-bottom: 20px;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }

  :deep(.arco-table-th) {
    text-align: center;
    background-color: #f7f8fa;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }
</style>
