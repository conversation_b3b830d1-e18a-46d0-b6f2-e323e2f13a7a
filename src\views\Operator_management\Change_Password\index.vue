<template>
  <div class="container">
    <Breadcrumb :items="['menu.Operator management', 'menu.Change_Password']" />

    <a-card title="修改密码">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-row :gutter="20" :style="{ marginBottom: '20px' }">
          <a-col :span="8">
            <a-card
              title="修改密码"
              :bordered="false"
              :style="{ width: '100%' }"
            >
              <template #extra> </template>
              <div class="password-form">
                <div class="form-item">
                  <span>当前登录账号：</span>
                  <a-input
                    v-model="formData.login_id"
                    class="input-field"
                    disabled
                  />
                </div>
                <div class="form-item">
                  <span>原密码：</span>
                  <a-input-password
                    v-model="formData.original_pwd"
                    class="input-field"
                  />
                </div>
                <div class="form-item">
                  <span>新密码：</span>
                  <a-input-password
                    v-model="formData.new_pwd"
                    class="input-field"
                  />
                </div>
                <div class="form-item">
                  <span>确认新密码：</span>
                  <a-input-password
                    v-model="formData.confirm_pwd"
                    class="input-field"
                  />
                </div>
                <br />

                <div class="button">
                  <a-button
                    id="changePasswordSubmit"
                    type="primary"
                    @click="submitPassword"
                  >
                    <template #icon>
                      <icon-check />
                    </template>
                    <template #default>提交</template>
                  </a-button>

                  <a-button
                    type="secondary"
                    style="margin-left: 10px"
                    @click="resetForm"
                  >
                    <template #icon>
                      <icon-refresh />
                    </template>
                    <template #default>重置</template>
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  // 修改密码功能 - 所有用户都可以访问，无需权限校验
  import { reactive, onMounted } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';

  const userStore = useUserStore();

  const formData = reactive({
    login_id: '',
    original_pwd: '',
    new_pwd: '',
    confirm_pwd: '',
  });
  // 重置表单
  const resetForm = () => {
    formData.original_pwd = '';
    formData.new_pwd = '';
    formData.confirm_pwd = '';
  };

  // 提交密码修改
  const submitPassword = async () => {
    // 表单验证
    if (!formData.login_id) {
      Message.error('请输入登录账号');
      return;
    }
    if (!formData.original_pwd) {
      Message.error('请输入原密码');
      return;
    }
    if (!formData.new_pwd) {
      Message.error('请输入新密码');
      return;
    }
    if (!formData.confirm_pwd) {
      Message.error('请输入确认密码');
      return;
    }
    if (formData.new_pwd !== formData.confirm_pwd) {
      Message.error('新密码和确认密码不一致');
      return;
    }

    try {
      const response = await axios.post(
        '/lua/operator.lua',
        new URLSearchParams({
          act: 'mod_pwd',
          login_id: formData.login_id,
          original_pwd: formData.original_pwd,
          new_pwd: formData.new_pwd,
          confirm_pwd: formData.confirm_pwd,
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data.code === 200) {
        Message.success(response.data.result || '密码修改成功');
        resetForm();
      } else {
        Message.error(response.data.result || '密码修改失败');
      }
    } catch (error) {
      console.error('Error changing password:', error);
      Message.error('密码修改请求失败');
    }
  };

  onMounted(() => {
    // 获取当前登录用户信息
    formData.login_id = userStore.userInfo?.accountId || '';
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .password-form {
    width: 100%;
  }

  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .form-item span {
    width: 120px;
    text-align: right;
    margin-right: 12px;
  }

  .input-field {
    flex: 1;
  }

  .button {
    margin-top: 20px;
    text-align: center;
  }

  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
</style>
