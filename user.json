{
  "menu": [
    {
      "id": "systemStatus",
      "name": "系统状态",
      "submenu": [
        {
          "id": "portInfo",
          "name": "端口信息"
        },
        {
          "id": "chartInfo",
          "name": "图表信息"
        },
        {
          "id": "dialStatus",
          "name": "拨号状态",
          "action": [
            {
              "id": "dialStatusFilter",
              "name": "筛选"
            },
            {
              "id": "dialStatusExport",
              "name": "导出状态账号"
            }
          ]
        },
        {
          "id": "sessionQuery",
          "name": "会话查询"
        },
        {
          "id": "exitStatus",
          "name": "出口状态"
        }
      ]
    },
    {
      "id": "systemControl",
      "name": "系统控制",
      "submenu": [
        {"id": "closeSession","name": "关闭会话"},
        {"id": "triggerSession", "name": "触发会话" },
        {"id": "triggerReadGroup", "name": "触发读组" },
        {"id": "functionSwitchSubmit", "name": "arp检测网关提交" },
        {"id": "sessionExitSubmit", "name": "会话选择出口提交" }
      ]
    },
    {
      "id": "systemConfig",
      "name": "系统配置",
      "submenu": [
        {
          "id": "accessPort",
          "name": "接入端口",
          "submenu": [{ "id": "accessPortSubmit", "name": "提交" }]
        },
        {
          "id": "multiExit",
          "name": "多出口",
          "submenu": [
            {
              "id": "fixExit",
              "name": "固定地址出口",
              "submenu": [
                { "id": "fixExitAdd", "name": "添加" },
                { "id": "fixExitDelete", "name": "删除" }
              ]
            },
            {
              "id": "samePortMultiAddr",
              "name": "同端口多地址",
              "submenu": [
                { "id": "samePortMultiAddrAdd", "name": "添加" },
                { "id": "samePortMultiAddrDelete", "name": "删除" }
              ]
            },
            {
              "id": "dialAuthExit",
              "name": "拨号认证出口",
              "submenu": [
                { "id": "dialAuthExitAdd", "name": "添加" },
                { "id": "dialAuthExitDelete", "name": "删除" }
              ]
            }
          ]
        },
        {
          "id": "dialAccess",
          "name": "拨号接入",
          "submenu": [{ "id": "dialAccessSubmit", "name": "提交" }]
        },
        {
          "id": "userWhitelist",
          "name": "用户白名单",
          "submenu": [
            { "id": "userWhitelistAdd", "name": "添加" },
            { "id": "userWhitelistDelete", "name": "删除" }
          ]
        },
        {
          "id": "natUserSegments",
          "name": "NAT用户地址段",
          "submenu": [
            { "id": "natUserSegmentsAdd", "name": "添加" },
            { "id": "natUserSegmentsDelete", "name": "删除" }
          ]
        },
        {
          "id": "accessRoutingTable",
          "name": "接入路由表",
          "submenu": [
            { "id": "accessRoutingTableAdd", "name": "添加" },
            { "id": "accessRoutingTableDelete", "name": "删除" }
          ]
        },
        {
          "id": "reverseMapping",
          "name": "反向映射",
          "submenu": [
            {
              "id": "mapUDP",
              "name": "UDP",
              "submenu": [
                { "id": "mapUDPAdd", "name": "添加" },
                { "id": "mapUDPDelete", "name": "删除" }
              ]
            },
            {
              "id": "mapTCP",
              "name": "TCP",
              "submenu": [
                { "id": "mapTCPAdd", "name": "添加" },
                { "id": "mapTCPDelete", "name": "删除" }
              ]
            },
            {
              "id": "mapIP",
              "name": "IP",
              "submenu": [
                { "id": "mapIPAdd", "name": "添加" },
                { "id": "mapIPDelete", "name": "删除" }
              ]
            }
          ]
        },
        {
          "id": "auditSettings",
          "name": "审计设置",
          "submenu": [{ "id": "auditSettingsSubmit", "name": "提交" }]
        },
        {
          "id": "natControl",
          "name": "NAT控制",
          "submenu": [{ "id": "natControlSubmit", "name": "提交" }]
        },
        {
          "id": "dnsDirection",
          "name": "DNS定向",
          "submenu": [
            { "id": "dnsDirectionAdd", "name": "添加" },
            { "id": "dnsDirectionDelete", "name": "删除" }
          ]
        },
        {
          "id": "mappingExit",
          "name": "映射出口",
          "submenu": [
            {
              "id": "bindExitName",
              "name": "绑定出口名",
              "submenu": [
                { "id": "bindExitNameAdd", "name": "添加" },
                { "id": "bindExitNameDelete", "name": "删除" }
              ]
            },
            {
              "id": "bindDialExitName",
              "name": "绑定拨号出口名",
              "submenu": [
                { "id": "bindDialExitNameAdd", "name": "添加" },
                { "id": "bindDialExitNameDelete", "name": "删除" }
              ]
            }
          ]
        },
        {
          "id": "kniPort",
          "name": "kni端口",
          "submenu": [{ "id": "kniPortSubmit", "name": "提交" }]
        },
        { "id": "restartVM", "name": "重启虚机" },
        { "id": "restartSystem", "name": "重启系统" },
        
        { "id": "authorizeUpgrade", "name": "授权升级" ,
      "submenu": [{ "id": "upgradeSoftware", "name": "升级软件" },
      { "id": "upgradeSubmit", "name": "提交" }]},
        {
          "id": "downloadUploadConfig",
          "name": "下载/上传配置",
          "submenu": [
            { "id": "downloadUploadConfigImport", "name": "导入" },
            { "id": "downloadUploadConfigExport", "name": "导出" }
          ]
        },
        { "id": "factoryReset", "name": "恢复出厂配置" }
      ]
    },
    {
      "id": "vmSettings",
      "name": "虚机设置",
      "submenu": [
        {
          "id": "networkPort",
          "name": "网络端口",
          "submenu": [{ "id": "networkPortSubmit", "name": "提交" }]
        },
        {
          "id": "cpuResources",
          "name": "CPU资源",
          "submenu": [{ "id": "cpuResourcesSubmit", "name": "提交" }]
        },
        {
          "id": "memoryChannels",
          "name": "内存通道",
          "submenu": [{ "id": "memoryChannelsSubmit", "name": "提交" }]
        },
        {
          "id": "memoryRows",
          "name": "内存行列",
          "submenu": [{ "id": "memoryRowsSubmit", "name": "提交" }]
        }
      ]
    },
    {
      "id": "hostSettings",
      "name": "主机设置",
      "submenu": [
        {
          "id": "networkSetting",
          "name": "网络设置",
          "submenu": [{ "id": "networkSettingSubmit", "name": "提交" }]
        }
      ]
    },
    {
      "id": "operatorManagement",
      "name": "操作员管理",
      "submenu": [
        {
          "id": "operators",
          "name": "操作员",
          "submenu": [
            { "id": "operatorsAdd", "name": "添加" },
            { "id": "operatorsedit", "name": "编辑" },
            { "id": "operatorsDelete", "name": "删除" }
          ]
        },
        {
          "id": "changePassword",
          "name": "修改密码",
          "submenu": [{ "id": "changePasswordSubmit", "name": "提交" }]
        },
        {
          "id": "projectTitle",
          "name": "项目标题",
          "submenu": [{ "id": "projectTitleSubmit", "name": "提交" }]
        }
      ]
    },
    {
      "id": "operationPermissions",
      "name": "操作权限",
      "submenu": [
        { "id": "RolePermissions", "name": "角色权限" ,
        "submenu": [
        { "id": "operationPermissionsAdd", "name": "添加" },
        { "id": "operationPermissionsEdit", "name": "编辑" },
        { "id": "operationPermissionsDelete", "name": "删除" }]
      }
      ]
        
     
    }
  ]
}


{
  "data": {
      "group": [
          {
              "menu": [
                  {
                      "resource": "vmSettings"
                  },
                  {
                      "resource": "operationPermissions"
                  },
                  {
                      "resource": "operatorManagement"
                  },
                  {
                      "resource": "systemControl"
                  },
                  {
                      "resource": "systemStatus"
                  },
                  {
                      "resource": "hostSettings"
                  },
                  {
                      "resource": "accessRoutingTable"
                  },
                  {
                      "resource": "reverseMapping"
                  },
                  {
                      "resource": "auditSettings"
                  },
                  {
                      "resource": "natControl"
                  },
                  {
                      "resource": "dnsDirection"
                  },
                  {
                      "resource": "mappingExit"
                  },
                  {
                      "resource": "kniPort"
                  },
                  {
                      "resource": "restartVM"
                  },
                  {
                      "resource": "restartSystem"
                  },
                  {
                      "resource": "upgradeSoftware"
                  },
                  {
                      "resource": "authorizeUpgrade"
                  },
                  {
                      "resource": "downloadUploadConfig"
                  },
                  {
                      "resource": "factoryReset"
                  },
                  {
                      "resource": "natUserSegments"
                  },
                  {
                      "resource": "dialAccess"
                  },
                  {
                      "resource": "multiExit"
                  },
                  {
                      "resource": "accessPort"
                  },
                  {
                      "resource": "userWhitelistAdd"
                  },
                  {
                      "resource": "networkPort"
                  },
                  {
                      "resource": "networkPortSubmit"
                  },
                  {
                      "resource": "cpuResources"
                  },
                  {
                      "resource": "cpuResourcesSubmit"
                  },
                  {
                      "resource": "memoryChannels"
                  },
                  {
                      "resource": "memoryChannelsSubmit"
                  },
                  {
                      "resource": "memoryRows"
                  },
                  {
                      "resource": "memoryRowsSubmit"
                  },
                  {
                      "resource": "operationPermissionsAdd"
                  },
                  {
                      "resource": "operationPermissionsEdit"
                  },
                  {
                      "resource": "operationPermissionsDelete"
                  },
                  {
                      "resource": "operators"
                  },
                  {
                      "resource": "operatorsAdd"
                  },
                  {
                      "resource": "operatorsedit"
                  },
                  {
                      "resource": "operatorsDelete"
                  },
                  {
                      "resource": "changePassword"
                  },
                  {
                      "resource": "changePasswordSubmit"
                  },
                  {
                      "resource": "projectTitle"
                  },
                  {
                      "resource": "projectTitleSubmit"
                  },
                  {
                      "resource": "closeSession"
                  },
                  {
                      "resource": "triggerSession"
                  },
                  {
                      "resource": "triggerReadGroup"
                  },
                  {
                      "resource": "statusInfo"
                  },
                  {
                      "resource": "portInfo"
                  },
                  {
                      "resource": "chartInfo"
                  },
                  {
                      "resource": "dialStatus"
                  },
                  {
                      "resource": "dialStatusFilter"
                  },
                  {
                      "resource": "dialStatusExport"
                  },
                  {
                      "resource": "sessionQuery"
                  },
                  {
                      "resource": "exitStatus"
                  },
                  {
                      "resource": "networkSetting"
                  },
                  {
                      "resource": "networkSettingSubmit"
                  },
                  {
                      "resource": "accessRoutingTableAdd"
                  },
                  {
                      "resource": "accessRoutingTableDelete"
                  },
                  {
                      "resource": "mapUDP"
                  },
                  {
                      "resource": "mapUDPAdd"
                  },
                  {
                      "resource": "mapUDPDelete"
                  },
                  {
                      "resource": "mapTCP"
                  },
                  {
                      "resource": "mapTCPAdd"
                  },
                  {
                      "resource": "mapTCPDelete"
                  },
                  {
                      "resource": "mapIP"
                  },
                  {
                      "resource": "mapIPAdd"
                  },
                  {
                      "resource": "mapIPDelete"
                  },
                  {
                      "resource": "auditSettingsSubmit"
                  },
                  {
                      "resource": "natControlSubmit"
                  },
                  {
                      "resource": "dnsDirectionAdd"
                  },
                  {
                      "resource": "dnsDirectionDelete"
                  },
                  {
                      "resource": "bindExitName"
                  },
                  {
                      "resource": "bindExitNameAdd"
                  },
                  {
                      "resource": "bindExitNameDelete"
                  },
                  {
                      "resource": "bindDialExitName"
                  },
                  {
                      "resource": "bindDialExitNameAdd"
                  },
                  {
                      "resource": "bindDialExitNameDelete"
                  },
                  {
                      "resource": "kniPortSubmit"
                  },
                  {
                      "resource": "downloadUploadConfigImport"
                  },
                  {
                      "resource": "downloadUploadConfigExport"
                  },
                  {
                      "resource": "natUserSegmentsAdd"
                  },
                  {
                      "resource": "natUserSegmentsDelete"
                  },
                  {
                      "resource": "dialAccessSubmit"
                  },
                  {
                      "resource": "fixExit"
                  },
                  {
                      "resource": "fixExitAdd"
                  },
                  {
                      "resource": "fixExitDelete"
                  },
                  {
                      "resource": "samePortMultiAddr"
                  },
                  {
                      "resource": "samePortMultiAddrAdd"
                  },
                  {
                      "resource": "samePortMultiAddrDelete"
                  },
                  {
                      "resource": "dialAuthExit"
                  },
                  {
                      "resource": "dialAuthExitAdd"
                  },
                  {
                      "resource": "dialAuthExitDelete"
                  },
                  {
                      "resource": "functionSwitch"
                  },
                  {
                      "resource": "functionSwitchSubmit"
                  },
                  {
                      "resource": "accessPortSubmit"
                  },
                  {
                      "resource": "userWhitelistDelete"
                  },
                  {
                      "resource": "userWhitelist"
                  },
                  {
                      "resource": "systemConfig"
                  },
                  {
                      "resource": "RolePermissions"
                  },
                  {
                      "resource": "sessionExitSubmit"
                  },
                  {
                      "resource": "upgradeSubmit"
                  }
              ],
              "name": "超级管理员",
              "gid": 1
          },
        
         
      ]
  }
  "code": 200
}
{
  "data": {
      "udp_res": [
          {
              "udp_in_port": "70",
              "udp_out_addr": "5.6.4.8",
              "udp_out_name": "dianxin",
              "udp_in_addr": "4.58.64.4",
              "udp_out_port": "60"
          },
          {
              "udp_in_port": "70",
              "udp_out_addr": "*******",
              "udp_out_name": "dianxin",
              "udp_in_addr": "*******",
              "udp_out_port": "60"
          }
      ],
      "tcp_res": [
          {
              "tcp_in_port": "20",
              "tcp_out_port": "10",
              "tcp_out_addr": "*******",
              "tcp_in_addr": "*******",
              "tcp_out_name": "cmcc"
          }
      ],
      "ip_res": [
          {
              "tcp_out_addr": "********",
              "tcp_in_addr": "*******",
              "tcp_out_name": "cmcc"
          }
      ]
  },
  "code": 200
}