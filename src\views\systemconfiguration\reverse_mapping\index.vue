<!-- 反向映射组件 -->
<template>
  <div v-if="isComponentVisible" class="container">
    <div v-if="isRefreshing" class="overlay">
      <div class="loader"></div>
    </div>
    <Breadcrumb
      :items="['menu.system-configuration', 'menu.reverse_mapping']"
    />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane key="1" title="UDP" v-if="hasPermission('mapUDP')">
        <a-card class="general-card" :title="$t('UDP')">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item field="number" :label="$t('出口名')">
                      <a-input
                        v-model="searchQuery"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        @click="filterData"
                        style="margin-left: 10px"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <a-col :span="12">
              <a-space>
                <!-- 新建按钮 -->
                <a-button
                  id="mapUDPAdd"
                  :disabled="!hasPermission('mapUDPAdd')"
                  type="primary"
                  @click="showModal"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  {{ $t('searchTable.operation.create') }}
                </a-button>
                <a-upload action="/">
                  <template #upload-button>
                    <!-- <a-button>
                  {{ $t('searchTable.operation.import') }}
                </a-button> -->
                  </template>
                </a-upload>
              </a-space>
            </a-col>
            <!-- 右侧按钮 -->
            <a-col
              :span="12"
              style="display: flex; align-items: center; justify-content: end"
            >
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>
          <!-- 列表 -->
          <a-table
            :columns="columns6"
            :data="filteredData"
            column-resizable
            style="margin-top: 20px"
            :pagination="false"
          >
            <template #out_name="{ record }">
              <span>{{ record.out_name }}</span>
            </template>

            <template #out_addr="{ record }">
              <span>{{ record.out_addr }}</span>
            </template>

            <template #out_port="{ record }">
              <span>{{ record.out_port }}</span>
            </template>

            <template #in_addr="{ record }">
              <span>{{ record.in_addr }}</span>
            </template>
            <template #in_port="{ record }">
              <span>{{ record.in_port }}</span>
            </template>

            <!-- 删除键位 -->
            <template #option="{ record }">
              <a-button
                id="mapUDPDelete"
                :disabled="!hasPermission('mapUDPDelete')"
                type="primary"
                @click="deleteRow(record)"
              >
                <template #icon>
                  <IconEye />
                </template>
                <template #default>删除</template>
              </a-button>
            </template>
          </a-table>
        </a-card>
        <a-modal
          v-model:visible="isModalVisible"
          title="新建"
          draggable
          @ok="handleOk"
          @cancel="handleCancel"
        >
          <a-col :span="24">
            <a-alert type="info" banner closable>
              <template #icon> </template>
              <template #title>udp数据包外部地址定向到内部地址</template>
            </a-alert>
          </a-col>
          <br />
          <a-form :model="formData" :rules="rules" ref="formRef">
            <a-form-item label="出口名" field="out_name">
              <a-select
                v-model="formData.out_name"
                :options="exitNameOptions"
                @change="handleExitNameChange"
              />
            </a-form-item>
            <a-form-item v-if="false" label="组ID" field="groupId">
              <a-input v-model="formData.groupId" disabled />
            </a-form-item>
            <a-form-item label="外部地址" field="out_addr">
              <a-input v-model="formData.out_addr" />
            </a-form-item>
            <a-form-item label="外部端口" field="out_port">
              <a-input v-model="formData.out_port" />
            </a-form-item>
            <a-form-item label="内部地址" field="in_addr">
              <a-input v-model="formData.in_addr" />
            </a-form-item>
            <a-form-item label="内部端口" field="in_port">
              <a-input v-model="formData.in_port" />
            </a-form-item>
          </a-form>
        </a-modal>
      </a-tab-pane>

      <a-tab-pane key="2" title="TCP" v-if="hasPermission('mapTCP')">
        <a-card class="general-card" :title="$t('TCP')">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery2 }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item field="number" :label="$t('出口名')">
                      <a-input
                        v-model="searchQuery2"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        @click="filterData2"
                        style="margin-left: 10px"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <a-col :span="12">
              <a-space>
                <!-- 新建按钮 -->
                <a-button
                  id="mapTCPAdd"
                  :disabled="!hasPermission('mapTCPAdd')"
                  type="primary"
                  @click="showModal2"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  {{ $t('searchTable.operation.create') }}
                </a-button>
                <a-upload action="/">
                  <template #upload-button>
                    <!-- <a-button>
                  {{ $t('searchTable.operation.import') }}
                </a-button> -->
                  </template>
                </a-upload>
              </a-space>
            </a-col>
            <!-- 右侧按钮 -->
            <a-col
              :span="12"
              style="display: flex; align-items: center; justify-content: end"
            >
              <!-- <a-button>
            <template #icon>
              <icon-download />
            </template>
            {{ $t('searchTable.operation.download') }}
          </a-button> -->
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh2">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>
          <a-table
            :columns="columns6"
            :data="filteredData2"
            column-resizable
            :pagination="false"
            style="margin-top: 20px"
          >
            <template #out_name="{ record }">
              <span>{{ record.out_name }}</span>
            </template>

            <template #out_addr="{ record }">
              <span>{{ record.out_addr }}</span>
            </template>

            <template #out_port="{ record }">
              <span>{{ record.out_port }}</span>
            </template>

            <template #in_addr="{ record }">
              <span>{{ record.in_addr }}</span>
            </template>
            <template #in_port="{ record }">
              <span>{{ record.in_port }}</span>
            </template>

            <template #option="{ record }">
              <a-button
                id="mapTCPDelete"
                :disabled="!hasPermission('mapTCPDelete')"
                type="primary"
                @click="deleteRow2(record)"
              >
                <template #icon>
                  <IconEye />
                </template>
                <template #default>删除</template>
              </a-button>
            </template>
          </a-table>
        </a-card>
        <a-modal
          v-model:visible="isModalVisible2"
          title="新建"
          draggable
          @ok="handleOk2"
          @cancel="handleCancel2"
        >
          <a-col :span="24">
            <a-alert type="info" banner closable>
              <template #icon> </template>
              <template #title>tcp数据包外部地址定向到内部地址</template>
            </a-alert>
          </a-col>
          <br />
          <a-form :model="formData2" :rules="rules" ref="formRef2">
            <a-form-item label="出口名" field="out_name">
              <a-select
                v-model="formData2.out_name"
                :options="exitNameOptions"
                @change="handleExitNameChange2"
              />
            </a-form-item>
            <a-form-item v-if="false" label="组ID" field="groupId">
              <a-input v-model="formData2.groupId" disabled />
            </a-form-item>
            <a-form-item label="外部地址" field="out_addr">
              <a-input v-model="formData2.out_addr" />
            </a-form-item>
            <a-form-item label="外部端口" field="out_port">
              <a-input v-model="formData2.out_port" />
            </a-form-item>
            <a-form-item label="内部地址" field="in_addr">
              <a-input v-model="formData2.in_addr" />
            </a-form-item>
            <a-form-item label="内部端口" field="in_port">
              <a-input v-model="formData2.in_port" />
            </a-form-item>
          </a-form>
        </a-modal>
      </a-tab-pane>

      <a-tab-pane key="3" title="IP" v-if="hasPermission('mapIP')">
        <a-card class="general-card" :title="$t('IP')">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery3 }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item field="number" :label="$t('出口名')">
                      <a-input
                        v-model="searchQuery3"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        @click="filterData3"
                        style="margin-left: 10px"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <a-col :span="12">
              <a-space>
                <!-- 新建按钮 -->
                <a-button
                  id="mapIPAdd"
                  :disabled="!hasPermission('mapIPAdd')"
                  type="primary"
                  @click="showModal3"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  {{ $t('searchTable.operation.create') }}
                </a-button>
                <a-upload action="/">
                  <template #upload-button>
                    <!-- <a-button>
                  {{ $t('searchTable.operation.import') }}
                </a-button> -->
                  </template>
                </a-upload>
              </a-space>
            </a-col>
            <!-- 右侧按钮 -->
            <a-col
              :span="12"
              style="display: flex; align-items: center; justify-content: end"
            >
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh3">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>
          <a-table
            :columns="columns7"
            :data="filteredData3"
            :pagination="false"
            style="margin-top: 20px"
          >
            <template #out_name="{ record }">
              <span>{{ record.out_name }}</span>
            </template>
            <template #out_addr="{ record }">
              <span>{{ record.out_addr }}</span>
            </template>
            <template #in_addr="{ record }">
              <span>{{ record.in_addr }}</span>
            </template>

            <template #option="{ record }">
              <a-button
                id="mapIPDelete"
                :disabled="!hasPermission('mapIPDelete')"
                type="primary"
                @click="deleteRow3(record)"
              >
                <template #icon>
                  <IconEye />
                </template>
                <template #default>删除</template>
              </a-button>
            </template>
          </a-table>
        </a-card>
        <a-modal
          v-model:visible="isModalVisible3"
          title="新建"
          draggable
          @ok="handleOk3"
          @cancel="handleCancel3"
        >
          <a-col :span="24">
            <a-alert type="info" banner closable>
              <template #icon> </template>
              <template #title>外部地址定向到内部地址，IP地址一一映射</template>
            </a-alert>
          </a-col>
          <br />
          <a-form :model="formData3" :rules="rules" ref="formRef3">
            <a-form-item label="出口名" field="out_name">
              <a-select
                v-model="formData3.out_name"
                :options="exitNameOptions"
                @change="handleExitNameChange3"
              />
            </a-form-item>
            <a-form-item v-if="false" label="组ID" field="groupId">
              <a-input v-model="formData3.groupId" disabled />
            </a-form-item>
            <a-form-item label="外部IP地址" field="out_addr">
              <a-input v-model="formData3.out_addr" />
            </a-form-item>
            <a-form-item label="内部IP地址" field="in_addr">
              <a-input v-model="formData3.in_addr" />
            </a-form-item>
          </a-form>
        </a-modal>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import {
    handleNotification1,
    handleNotification2,
  } from '../../../utils/info';

  // 定义数据接口
  export interface ExportInfo {
    exportName: string;
    groupId: string;
    portaddresses: string;
  }

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // 验证IP地址格式
      const validateIpAddress = (ip: string): boolean => {
        const ipRegex =
          /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
      };

      // 定义options的类型为OptionValue，明确其结构
      const exitNameOptions = ref<
        Array<{ label: string; value: string; groupId: string }>
      >([]);

      const formData = reactive({
        // key: '1',
        out_name: '',
        groupId: '',
        out_addr: '',
        out_port: '',
        in_addr: '',
        type_act: '',
        in_port: '',
      });

      const formData2 = reactive({
        // key: '1',
        out_name: '',
        groupId: '',
        out_addr: '',
        out_port: '',
        in_addr: '',
        type_act: '',
        in_port: '',
      });

      const formData3 = reactive({
        type_act: '',
        out_name: '',
        groupId: '',
        out_addr: '',
        in_addr: '',
      });
      // 定义columns数组中元素的类型

      const columns6 = [
        {
          title: '出口名',
          dataIndex: 'out_name',
          slotName: 'out_name',
        },
        {
          title: '外部地址',
          dataIndex: 'out_addr',
          slotName: 'out_addr',
        },
        {
          title: '外部端口',
          dataIndex: 'out_port',
          slotName: 'out_port',
        },
        {
          title: '内部地址',
          dataIndex: 'in_addr',
          slotName: 'in_addr',
        },
        {
          title: '内部端口',
          dataIndex: 'in_port',
          slotName: 'in_port',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];

      const columns7 = [
        {
          title: '出口名',
          dataIndex: 'out_name',
          slotName: 'out_name',
        },
        {
          title: '外部IP地址',
          dataIndex: 'out_addr',
          slotName: 'out_addr',
        },
        {
          title: '内部IP地址',
          dataIndex: 'in_addr',
          slotName: 'in_addr',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];
      const data = ref<any[]>([]);
      const data2 = ref<any[]>([]);
      const data3 = ref<any[]>([]);

      const filteredData = ref<any[]>([]);
      const filteredData2 = ref<any[]>([]);
      const filteredData3 = ref<any[]>([]);

      const isModalVisible = ref(false);
      const isModalVisible2 = ref(false);
      const isModalVisible3 = ref(false);
      const isRefreshing = ref(false);
      const isComponentVisible = ref(true);
      const searchQuery = ref('');
      const searchQuery2 = ref('');
      const searchQuery3 = ref('');
      const rules = {
        out_name: [{ required: true, message: '出口名不能为空' }],
        out_addr: [
          { required: true, message: '外部地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateIpAddress(value)) {
                callback('IP地址格式不正确，应为：***********');
              } else {
                callback();
              }
            },
          },
        ],
        out_port: [{ required: true, message: '外部端口不能为空' }],
        in_addr: [
          { required: true, message: '内部地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateIpAddress(value)) {
                callback('IP地址格式不正确，应为：***********');
              } else {
                callback();
              }
            },
          },
        ],
        in_port: [{ required: true, message: '内部端口不能为空' }],
      };
      const formRef = ref();
      const formRef2 = ref();
      const formRef3 = ref();
      // 获取数据
      // fetchData函数
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'backward_map' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 从嵌套结构中提取UDP数据并映射字段
            if (response.data.data && response.data.data.udp_res) {
              data.value = response.data.data.udp_res.map((item: any) => ({
                out_name: item.udp_out_name || '',
                out_addr: item.udp_out_addr || '',
                out_port: item.udp_out_port || '',
                in_addr: item.udp_in_addr || '',
                in_port: item.udp_in_port || '',
              }));
            } else {
              data.value = [];
            }

            filteredData.value = [...data.value];
            console.log('UDP数据更新:', data.value);
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取UDP数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      const fetchData2 = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'backward_map' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 从嵌套结构中提取TCP数据并映射字段
            if (response.data.data && response.data.data.tcp_res) {
              data2.value = response.data.data.tcp_res.map((item: any) => ({
                out_name: item.tcp_out_name || '',
                out_addr: item.tcp_out_addr || '',
                out_port: item.tcp_out_port || '',
                in_addr: item.tcp_in_addr || '',
                in_port: item.tcp_in_port || '',
              }));
            } else {
              data2.value = [];
            }

            filteredData2.value = [...data2.value];
            console.log('TCP数据更新:', data2.value);
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取TCP数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      const fetchData3 = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'backward_map' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 从嵌套结构中提取IP数据并映射字段
            if (response.data.data && response.data.data.ip_res) {
              data3.value = response.data.data.ip_res.map((item: any) => ({
                out_name: item.tcp_out_name || '', // 注意：IP数据也使用tcp前缀
                out_addr: item.tcp_out_addr || '',
                in_addr: item.tcp_in_addr || '',
              }));
            } else {
              data3.value = [];
            }

            filteredData3.value = [...data3.value];
            console.log('IP数据更新:', data3.value);
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取IP数据失败:', error);
          Message.error('获取数据失败');
        }
      };
      // 删除行
      const deleteRow = async (record: {
        type_act: string;
        out_name: string;
        out_addr: string;
        out_port: string;
        in_addr: string;
        in_port: string;
      }) => {
        try {
          const {
            out_name: outName,
            out_addr: outAddr,
            out_port: outPort,
            in_addr: inAddr,
            in_port: inPort,
          } = record;

          // 获取组ID
          let groupId = '';
          // 尝试从出口名选项中找到对应的组ID
          const selectedExit = exitNameOptions.value.find(
            (item) => item.value === outName
          );
          if (selectedExit) {
            groupId = selectedExit.groupId;
          }

          // 发送删除请求
          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'output',
              type_act: 'backward_map',
              pro_act: 'udp_del',
              out_name: outName,
              out_addr: outAddr,
              out_port: outPort,
              in_addr: inAddr,
              in_port: inPort,
              group_id: groupId, // 添加组ID参数
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 删除成功后重新获取数据
            await fetchData();
            Message.success('删除成功');
          } else {
            Message.error('删除失败');
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('删除失败');
        }
      };

      const deleteRow2 = async (record: {
        type_act: string;
        out_name: string;
        out_addr: string;
        out_port: string;
        in_addr: string;
        in_port: string;
      }) => {
        try {
          const {
            out_name: outName,
            out_addr: outAddr,
            out_port: outPort,
            in_addr: inAddr,
            in_port: inPort,
          } = record;

          // 获取组ID
          let groupId = '';
          // 尝试从出口名选项中找到对应的组ID
          const selectedExit = exitNameOptions.value.find(
            (item) => item.value === outName
          );
          if (selectedExit) {
            groupId = selectedExit.groupId;
          }

          // 发送删除请求
          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'output',
              type_act: 'backward_map',
              pro_act: 'tcp_del',
              out_name: outName,
              out_addr: outAddr,
              out_port: outPort,
              in_addr: inAddr,
              in_port: inPort,
              group_id: groupId, // 添加组ID参数
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 删除成功后重新获取数据
            await fetchData2();
            Message.success('删除成功');
          } else {
            Message.error('删除失败');
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('删除失败');
        }
      };

      const deleteRow3 = async (record: {
        type_act: string;
        out_name: string;
        out_addr: string;
        in_addr: string;
      }) => {
        try {
          const {
            out_name: outName,
            out_addr: outAddr,
            in_addr: inAddr,
          } = record;

          // 获取组ID
          let groupId = '';
          // 尝试从出口名选项中找到对应的组ID
          const selectedExit = exitNameOptions.value.find(
            (item) => item.value === outName
          );
          if (selectedExit) {
            groupId = selectedExit.groupId;
          }

          // 发送删除请求
          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'output',
              type_act: 'backward_map',
              pro_act: 'ip_del',
              out_name: outName,
              out_addr: outAddr,
              in_addr: inAddr,
              group_id: groupId, // 添加组ID参数
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 删除成功后重新获取数据
            await fetchData3();
            Message.success('删除成功');
          } else {
            Message.error('删除失败');
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('删除失败');
        }
      };

      // 显示模态框
      const showModal = () => {
        if (!hasPermission('mapUDPAdd')) {
          Message.error('您没有权限');
          return;
        }
        isModalVisible.value = true;
      };
      const showModal2 = () => {
        if (!hasPermission('mapTCPAdd')) {
          Message.error('您没有权限');
          return;
        }
        isModalVisible2.value = true;
      };
      const showModal3 = () => {
        if (!hasPermission('mapIPAdd')) {
          Message.error('您没有权限');
          return;
        }
        isModalVisible3.value = true;
      };

      // 处理确认
      const handleOk = async (done) => {
        // 验证表单
        formRef.value.validate((errors) => {
          if (errors) {
            // 表单验证失败
            Message.error('表单验证失败，请检查输入');
            done(false); // 阻止模态框关闭
            return;
          }

          const {
            out_name: outName,
            out_addr: outAddr,
            out_port: outPort,
            in_addr: inAddr,
            in_port: inPort,
            groupId,
          } = formData;

          // 发送添加请求
          axios
            .post(
              '/lua/set_cfg.lua',
              new URLSearchParams({
                act: 'output',
                type_act: 'backward_map',
                pro_act: 'udp_add',
                out_name: outName,
                out_addr: outAddr,
                out_port: outPort,
                in_addr: inAddr,
                in_port: inPort,
                group_id: groupId,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )

            .then((response) => {
              if (response.data.code === 200) {
                fetchData();
                Message.success('添加成功');
                isModalVisible.value = false;
                formData.out_name = ''; // 清空表单
                formData.out_addr = '';
                formData.out_port = '';
                formData.in_addr = '';
                formData.in_port = '';
                formData.groupId = '';
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });
        return false;
      };

      // 处理确认
      const handleOk2 = async (done) => {
        // 验证表单
        formRef2.value.validate((errors) => {
          if (errors) {
            // 表单验证失败
            Message.error('表单验证失败，请检查输入');
            done(false); // 阻止模态框关闭
            return;
          }

          const {
            out_name: outName,
            out_addr: outAddr,
            out_port: outPort,
            in_addr: inAddr,
            in_port: inPort,
            groupId,
          } = formData2;

          // 发送添加请求
          axios
            .post(
              '/lua/set_cfg.lua',
              new URLSearchParams({
                act: 'output',
                type_act: 'backward_map',
                pro_act: 'tcp_add',
                out_name: outName,
                out_addr: outAddr,
                out_port: outPort,
                in_addr: inAddr,
                in_port: inPort,
                group_id: groupId,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )
            .then((response) => {
              if (response.data.code === 200) {
                fetchData2();
                Message.success('添加成功');
                isModalVisible2.value = false;
                formData2.out_name = '';
                formData2.out_addr = '';
                formData2.out_port = '';
                formData2.in_addr = '';
                formData2.in_port = '';
                formData2.groupId = '';
                done(true); // 允许模态框关闭
                isModalVisible2.value = false;
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });
        return false;
      };

      const handleOk3 = async (done) => {
        // 验证表单
        formRef3.value.validate((errors) => {
          if (errors) {
            // 表单验证失败
            Message.error('表单验证失败，请检查输入');
            done(false); // 阻止模态框关闭
            return;
          }

          const {
            out_name: outName,
            out_addr: outAddr,
            in_addr: inAddr,
            groupId,
          } = formData3;

          // 发送添加请求
          axios
            .post(
              '/lua/set_cfg.lua',
              new URLSearchParams({
                act: 'output',
                type_act: 'backward_map',
                pro_act: 'ip_add',
                out_name: outName,
                out_addr: outAddr,
                in_addr: inAddr,
                group_id: groupId,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )
            .then((response) => {
              if (response.data.code === 200) {
                fetchData3();
                Message.success('添加成功');
                isModalVisible3.value = false;
                formData3.out_name = '';
                formData3.out_addr = '';
                formData3.in_addr = '';
                done(true); // 允许模态框关闭
                formData3.groupId = '';
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });
        return false;
      };

      // 过滤数据
      const filterData = () => {
        const rawData = data.value;

        if (searchQuery.value.trim() === '') {
          filteredData.value = [...rawData];
          Message.error('请填写出口名');
          return;
        }

        filteredData.value = rawData.filter((item) =>
          item.ip.includes(searchQuery.value)
        );

        if (filteredData.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      const filterData2 = () => {
        const rawData = data.value;

        if (searchQuery.value.trim() === '') {
          filteredData.value = [...rawData];
          Message.error('请填写出口名');
          return;
        }

        filteredData.value = rawData.filter((item) =>
          item.ip.includes(searchQuery.value)
        );

        if (filteredData.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      const filterData3 = () => {
        const rawData = data.value;

        if (searchQuery.value.trim() === '') {
          filteredData.value = [...rawData];
          Message.error('请填写出口名');
          return;
        }

        filteredData.value = rawData.filter((item) =>
          item.ip.includes(searchQuery.value)
        );

        if (filteredData.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      const handleCancel = () => {
        isModalVisible.value = false;
      };
      const handleCancel2 = () => {
        isModalVisible2.value = false;
      };
      const handleCancel3 = () => {
        isModalVisible3.value = false;
      };
      const activeTabKey = ref('1');
      // 检查标签页权限并找到第一个可用的标签页
      const findFirstAvailableTab = () => {
        const tabPermissions = [
          { key: '1', permission: 'mapUDP' },
          { key: '2', permission: 'mapTCP' },
          { key: '3', permission: 'mapIP' },
        ];

        const availableTab = tabPermissions.find((tab) =>
          hasPermission(tab.permission)
        );

        if (availableTab) {
          return availableTab.key;
        }

        // 如果没有任何标签有权限，可能需要显示一个提示或者空白页面
        Message.error('您没有权限');
        return null;
      };

      // 获取出口名列表
      const fetchExitNames = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'multi_output' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 将接口数据保存为选项数据，包含出口名和组ID
            exitNameOptions.value = response.data.data.map((item: any) => ({
              label: item.out_name || '',
              value: item.out_name || '',
              groupId: item.group_id || '', // 接收服务器返回的下划线格式，但在内部使用驼峰格式
            }));
            console.log('出口名选项更新:', exitNameOptions.value);
          }
        } catch (error) {
          console.error('获取出口名列表失败:', error);
          Message.error('获取出口名列表失败');
        }
      };

      // 添加标签页切换处理函数
      const handleTabChange = (key: string) => {
        // 检查用户是否有权限访问目标标签页
        let hasAccess = false;

        if (key === '1') {
          hasAccess = hasPermission('mapUDP');
        } else if (key === '2') {
          hasAccess = hasPermission('mapTCP');
        } else if (key === '3') {
          hasAccess = hasPermission('mapIP');
        }

        if (!hasAccess) {
          Message.error('您没有权限');

          // 尝试找到下一个有权限的标签页
          const newKey = findFirstAvailableTab();
          if (newKey && newKey !== activeTabKey.value) {
            activeTabKey.value = newKey;
          }
        } else {
          // 有权限访问，更新当前活动标签
          activeTabKey.value = key;

          // 加载对应标签页的数据
          if (key === '1') {
            fetchData();
          } else if (key === '2') {
            fetchData2();
          } else if (key === '3') {
            fetchData3();
          }
        }
      };

      // 修改刷新函数，为不同标签页提供专用刷新
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 添加第二个标签页的刷新函数
      const handleRefresh2 = () => {
        isRefreshing.value = true;
        fetchData2().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 添加第三个标签页的刷新函数
      const handleRefresh3 = () => {
        isRefreshing.value = true;
        fetchData3().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 处理出口名变化
      const handleExitNameChange = (value: string) => {
        // 根据选择的出口名找到对应的组ID
        const selectedExit = exitNameOptions.value.find(
          (item) => item.value === value
        );
        if (selectedExit) {
          formData.groupId = selectedExit.groupId;
          console.log('出口名变化，对应组ID:', formData.groupId);
        } else {
          formData.groupId = '';
        }
      };

      // 处理出口名变化 - 第二个表单
      const handleExitNameChange2 = (value: string) => {
        // 根据选择的出口名找到对应的组ID
        const selectedExit = exitNameOptions.value.find(
          (item) => item.value === value
        );
        if (selectedExit) {
          formData2.groupId = selectedExit.groupId;
          console.log('出口名变化 (表单2)，对应组ID:', formData2.groupId);
        } else {
          formData2.groupId = '';
        }
      };

      // 处理出口名变化 - 第三个表单
      const handleExitNameChange3 = (value: string) => {
        // 根据选择的出口名找到对应的组ID
        const selectedExit = exitNameOptions.value.find(
          (item) => item.value === value
        );
        if (selectedExit) {
          formData3.groupId = selectedExit.groupId;
          console.log('出口名变化 (表单3)，对应组ID:', formData3.groupId);
        } else {
          formData3.groupId = '';
        }
      };

      // 组件加载时获取数据和初始化标签页
      onMounted(() => {
        // 获取出口名选项
        fetchExitNames();

        // 查找第一个有权限的标签页
        const firstAvailableTab = findFirstAvailableTab();

        if (firstAvailableTab) {
          activeTabKey.value = firstAvailableTab;

          // 加载对应标签页的数据
          if (firstAvailableTab === '1') {
            fetchData();
          } else if (firstAvailableTab === '2') {
            fetchData2();
          } else if (firstAvailableTab === '3') {
            fetchData3();
          }
        }
      });
      onBeforeUnmount(() => {});

      return {
        columns6,
        columns7,
        data,
        data2,
        data3,
        handleTabChange,
        handleNotification1,
        handleNotification2,
        filterData,
        filterData2,
        filterData3,
        searchQuery,
        searchQuery2,
        searchQuery3,
        isModalVisible,
        isModalVisible2,
        isModalVisible3,
        isComponentVisible,
        isRefreshing,
        handleRefresh,
        handleRefresh2,
        handleRefresh3,
        handleCancel,
        handleCancel2,
        handleCancel3,
        handleOk,
        handleOk2,
        handleOk3,
        handleExitNameChange,
        handleExitNameChange2,
        handleExitNameChange3,
        deleteRow,
        deleteRow2,
        deleteRow3,
        formData,
        formData2,
        formData3,
        formRef,
        formRef2,
        formRef3,
        filteredData,
        filteredData2,
        filteredData3,
        rules,
        showModal3,
        showModal2,
        showModal,
        activeTabKey,
        exitNameOptions,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }

  .arco-alert-with-title {
    padding: 0px 5px;
    text-align: center;
    justify-content: center;
    align-items: center;
  }
</style>
