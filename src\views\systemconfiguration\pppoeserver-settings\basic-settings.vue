<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 3 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="oesrv_acname" class="uniform-form-item">
        <template #label>
          <span class="form-label">ACname：</span>
        </template>
        <a-tooltip
          content="不填缺省为Amnoon"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_acname"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item field="oesrv_service" class="uniform-form-item">
        <template #label>
          <span class="form-label">服务名：</span>
        </template>
        <a-tooltip
          content="不填缺省为internet"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_service"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item field="oesrv_init_oses" class="uniform-form-item">
        <template #label>
          <span class="form-label">最大并发认证会话数：</span>
        </template>
        <a-tooltip
          content="不填缺省为2048"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_init_oses"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item field="oesrv_mtu" class="uniform-form-item">
        <template #label>
          <span class="form-label">会话数 MTU：</span>
        </template>
        <a-tooltip
          content="不填缺省为1492"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_mtu"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item field="oesrv_echo_timeout" class="uniform-form-item">
        <template #label>
          <span class="form-label">发送LCP echo 闲置时间：</span>
        </template>
        <a-tooltip
          content="单位：秒，不填缺省为90"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_echo_timeout"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item field="oesrv_echo_interval" class="uniform-form-item">
        <template #label>
          <span class="form-label">发送LCP echo 间隔时间：</span>
        </template>
        <a-tooltip
          content="单位：秒，不填缺省为10"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_echo_interval"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item field="oesrv_echo_die" class="uniform-form-item">
        <template #label>
          <span class="form-label">LCP 闲置断线时间：</span>
        </template>
        <a-tooltip
          content="单位：秒，不填缺省为150"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_echo_die"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item field="oesrv_tcp_max_seg" class="uniform-form-item">
        <template #label>
          <span class="form-label">调整TCP会话MSS数值：</span>
        </template>
        <a-tooltip
          content="单位：秒，不填缺省为1452，0表示不控制"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_tcp_max_seg"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip> </a-form-item
      ><a-form-item field="oesrv_limit_pado" class="uniform-form-item">
        <template #label>
          <span class="form-label">减速回应pppoe会话数：</span>
        </template>
        <a-tooltip
          content="pppoe会话数据达到该数值，减速回应PPPOE连接"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_limit_pado"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>

      <a-form-item field="oesrv_chk_srv" class="uniform-form-item">
        <template #label>
          <span class="form-label">是否检查会话service匹配：</span>
        </template>
        <a-select :style="{ width: '100px' }" v-model="formData.oesrv_chk_srv">
          <a-option value="disable">否</a-option>
          <a-option value="enable">是 </a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="oesrv_reject_ccp" class="uniform-form-item">
        <template #label>
          <span class="form-label">PPP会话是否拒绝CCP：</span>
        </template>
        <a-select
          :style="{ width: '100px' }"
          v-model="formData.oesrv_reject_ccp"
        >
          <a-option value="disable">否</a-option>
          <a-option value="enable">是 </a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="oesrv_chap_auth" class="uniform-form-item">
        <template #label>
          <span class="form-label">是否采用CHAP认证：</span>
        </template>
        <a-select
          :style="{ width: '100px' }"
          v-model="formData.oesrv_chap_auth"
        >
          <a-option value="disable">否</a-option>
          <a-option value="enable">是 </a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="oesrv_chk_mac" class="uniform-form-item">
        <template #label>
          <span class="form-label">限制单mac会话数：</span>
        </template>
        <a-select :style="{ width: '100px' }" v-model="formData.oesrv_chk_mac">
          <a-option value="disable">否</a-option>
          <a-option value="enable">是 </a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="oesrv_strict_cfg" class="uniform-form-item">
        <template #label>
          <span class="form-label">严格执行AAA配置：</span>
        </template>
        <a-select
          :style="{ width: '100px' }"
          v-model="formData.oesrv_strict_cfg"
        >
          <a-option value="disable">否</a-option>
          <a-option value="enable">是 </a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="oesrv_only_input" class="uniform-form-item">
        <template #label>
          <span class="form-label">只处理PPPOE封装：</span>
        </template>
        <a-select
          :style="{ width: '100px' }"
          v-model="formData.oesrv_only_input"
        >
          <a-option value="disable">否</a-option>
          <a-option value="enable">是 </a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="default_pool" class="uniform-form-item">
        <template #label>
          <span class="form-label">指定缺省地址池：</span>
        </template>
        <a-select :style="{ width: '100px' }" v-model="formData.default_pool">
          <a-option value="default-pool">不指定</a-option>
          <a-option value="1">1 </a-option>
          <a-option value="2">2 </a-option>
          <a-option value="3">3 </a-option>
          <a-option value="4">4 </a-option>
          <a-option value="5">5 </a-option>
          <a-option value="6">6 </a-option>
          <a-option value="7">7 </a-option>
          <a-option value="8">8 </a-option>
        </a-select>
      </a-form-item>

      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>提交</template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';
  import message from '@/utils/message';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      const formData = reactive({
        oesrv_acname: '',
        oesrv_service: '',
        oesrv_reject_ccp: '',
        oesrv_only_input: '',
        default_pool: '',
        oesrv_tcp_max_seg: '',
        oesrv_mtu: '',
        oesrv_chk_srv: '',
        oesrv_init_oses: '',
        oesrv_echo_timeout: '',
        oesrv_echo_interval: '',
        oesrv_echo_die: '',
        oesrv_chap_auth: '',
        oesrv_strict_cfg: '',
        oesrv_chk_mac: '',
        oesrv_limit_pado: '',
      });
      const rules = {
        oesrv_acname: [{ required: true, message: 'acname不能为空' }],
        oesrv_service: [{ required: true, message: '服务名不能为空' }],
      };
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_pppoe_server.lua',
            new URLSearchParams({ act: 'oesrv_basic' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.oesrv_acname = response.data.data.oesrv_acname || '';
            formData.oesrv_service = response.data.data.oesrv_service || '';
            formData.oesrv_reject_ccp =
              response.data.data.oesrv_reject_ccp || '';
            formData.oesrv_only_input =
              response.data.data.oesrv_only_input || '';
            formData.default_pool = response.data.data.default_pool || '';
            formData.oesrv_tcp_max_seg =
              response.data.data.oesrv_tcp_max_seg || '';
            formData.oesrv_mtu = response.data.data.oesrv_mtu || '';
            formData.oesrv_chk_srv = response.data.data.oesrv_chk_srv || '';
            formData.oesrv_init_oses = response.data.data.oesrv_init_oses || '';
            formData.oesrv_echo_timeout =
              response.data.data.oesrv_echo_timeout || '';
            formData.oesrv_echo_interval =
              response.data.data.oesrv_echo_interval || '';
            formData.oesrv_echo_die = response.data.data.oesrv_echo_die || '';
            formData.oesrv_chap_auth = response.data.data.oesrv_chap_auth || '';
            formData.oesrv_strict_cfg =
              response.data.data.oesrv_strict_cfg || '';
            formData.oesrv_chk_mac = response.data.data.oesrv_chk_mac || '';
            formData.oesrv_echo_interval =
              response.data.data.oesrv_echo_interval || '';
            formData.oesrv_limit_pado =
              response.data.data.oesrv_limit_pado || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          // Arco Design表单验证失败时会返回errors对象
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'oesrv_basic',
              act_type: 'mod',
              map_head: String(formData.map_head),
              map_tail: String(formData.map_tail),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        saveAction,
        hasPermission,
        rules,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
