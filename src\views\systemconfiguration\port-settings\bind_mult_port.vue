<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-transfer :data="data" :default-value="value">
      <template #source-title>
        <div> 未绑定端口 </div>
      </template>

      <template #target-title>
        <div> 已绑定端口 </div>
      </template>
    </a-transfer>

    <a-button
      :disabled="!hasPermission('BasicControlSubmit')"
      type="primary"
      style="margin-top: 2%"
      @click="saveAction"
    >
      <template #icon>
        <icon-check />
      </template>
      <template #default>提交</template>
    </a-button>
  </a-card>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed,
    watch,
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';

  export default defineComponent({
    setup() {
      const { hasPermission } = usePermission();

      const data = Array(8)
        .fill(undefined)
        .map((_, index) => ({
          value: `option${index + 1}`,
          label: `Option ${index + 1}`,
          disabled: false,
        }));
      const value = ['option1', 'option3', 'option5'];

      const saveAction = () => {
        Message.success('提交成功');
        // 这里添加实际的保存逻辑
      };

      return {
        data,
        value,
        hasPermission,
        saveAction,
      };
    },
  });
</script>
