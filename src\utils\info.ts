import { Notification } from '@arco-design/web-vue';

export const handleNotification1 = (): void => {
  Notification.info({
    content: '注意：配置后需要重新启动虚机才能生效!',
    closable: true,
    style: {
      width: '400px',
      height: '50px',
    },
  });
};

export const handleNotification2 = (): void => {
  Notification.info({
    content: '重置成功',
    closable: true,
    style: {
      width: '200px',
      height: '50px',
    },
  });
};

export const handleNotification3 = (): void => {
  Notification.info({
    content: '注意：升级后,需要重启设备。',
    closable: true,
    style: {
      width: '300px',
      height: '50px',
    },
  });
};
export const handleNotification4 = (): void => {
  Notification.info({
    content: '设置成功',
    closable: true,
    style: {
      width: '300px',
      height: '50px',
    },
  });
};

export const handleNotification5 = (): void => {
  Notification.error({
    content: 'IP地址不能为空',
    closable: true,
    style: {
      width: '300px',
      height: '50px',
    },
  });
};
