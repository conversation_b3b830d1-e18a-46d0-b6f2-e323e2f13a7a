<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- 标题和按钮区域 -->
    <div class="header-section">
      <h3 class="title">{{ $t('define_dhcp_pool_fixed_allocation_mac') }}</h3>
      <a-row style="margin-bottom: 16px">
        <a-col :span="24">
          <a-space>
            <a-button type="primary" @click="showAddModal">
              <template #icon>
                <icon-plus />
              </template>
              {{ $t('add') }}
            </a-button>
            <a-button @click="handleExport">
              <template #icon>
                <icon-export />
              </template>
              {{ $t('export') }}
            </a-button>
            <a-button @click="handleExportResult">
              <template #icon>
                <icon-download />
              </template>
              {{ $t('export_result') }}
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!-- 列表 -->
    <a-table
      row-key="ip"
      :columns="columns"
      :data="data"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
    >
      <template #ip="{ record }">
        <a-input
          v-model="record.ip"
          :placeholder="$t('please_enter_ip_address')"
          size="small"
        />
      </template>
      <template #mask="{ record }">
        <a-input
          v-model="record.mask"
          :placeholder="$t('please_enter_mac_address')"
          size="small"
        />
      </template>
      <template #desc="{ record }">
        <a-input
          v-model="record.desc"
          :placeholder="$t('please_enter_description')"
          size="small"
        />
      </template>
      <template #operation="{ rowIndex }">
        <a-button type="primary" size="small" @click="addRecord(rowIndex)">
          <template #icon>
            <icon-plus />
          </template>
          {{ $t('add') }}
        </a-button>
      </template>
    </a-table>

    <!-- 导入固定地址模态框 -->
    <a-modal
      v-model:visible="isAddModalVisible"
      :width="600"
      :title="$t('import_fixed_address')"
      draggable
      :mask-closable="false"
      :unmount-on-close="false"
      @before-ok="handleAddBeforeOk"
      @cancel="handleAddCancel"
    >
      <div>
        <div style="margin-bottom: 16px">
          <label style="display: block; margin-bottom: 8px; font-weight: 500">
            {{ $t('import_file') }}
          </label>
          <a-upload
            ref="uploadRef"
            :file-list="fileList"
            :auto-upload="false"
            :show-file-list="false"
            @change="handleFileChange"
            accept=".txt,.csv"
          >
            <a-button>
              <template #icon>
                <icon-upload />
              </template>
              {{ $t('choose_file') }}
            </a-button>
          </a-upload>
          <span v-if="selectedFileName" style="margin-left: 10px; color: #666">
            {{ selectedFileName }}
          </span>
        </div>

        <div style="margin-bottom: 16px">
          <div style="font-weight: 500; margin-bottom: 8px"
            >{{ $t('note') }}:</div
          >
          <div style="color: #666; font-size: 14px; line-height: 1.5">
            1. {{ $t('import_note_1') }}
            <a-link style="margin-left: 4px" @click="downloadSample">
              ({{ $t('sample_download') }})
            </a-link>
            <br />
            2. {{ $t('import_note_2') }}
          </div>
        </div>

        <div>
          <div style="font-weight: 500; margin-bottom: 8px">
            {{ $t('format_description_example') }}
          </div>
          <a-textarea
            v-model="formatDescription"
            :rows="8"
            readonly
            :placeholder="$t('ip_address_mac_address')"
            style="background-color: #f7f8fa; resize: none"
          />
        </div>
      </div>
    </a-modal>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';

  interface FixedAddressItem {
    ip: string;
    mask: string;
    desc: string;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();
      const { t } = useI18n();

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: t('ip_address'),
          dataIndex: 'ip',
          slotName: 'ip',
          align: 'center',
          width: 200,
        },
        {
          title: t('mac_address'),
          dataIndex: 'mask',
          slotName: 'mask',
          align: 'center',
          width: 200,
        },
        {
          title: t('description'),
          dataIndex: 'desc',
          slotName: 'desc',
          align: 'center',
          width: 200,
        },
        {
          title: t('operation'),
          dataIndex: 'operation',
          slotName: 'operation',
          align: 'center',
          width: 100,
        },
      ];

      const data = ref<FixedAddressItem[]>([]);
      const isLoading = ref(false);
      const isAddModalVisible = ref(false);

      // 导入相关数据
      const fileList = ref([]);
      const selectedFileName = ref('');
      const formatDescription = ref(
        '********** 00-00-00-8B-3E-76\n10.10.10.2 00-00-00-8A-C9-20\n10.10.10.3 00-00-00-8A-74-13'
      );
      const uploadRef = ref();

      // 新增表单数据
      const addFormData = reactive({
        ip: '',
        mask: '',
        desc: '',
      });

      // 新增表单引用
      const addFormRef = ref();

      // 新增表单验证规则
      const addRules = {
        ip: [
          { required: true, message: t('ip_address_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('ip_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        mask: [
          { required: true, message: t('mac_address_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
              if (!value || !macRegex.test(value)) {
                callback(t('mac_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
      };

      // 获取数据
      const fetchData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'dhcp_fix' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              data.value = response.data.data.map((item: any) => ({
                ip: item.ip || '',
                mask: item.mask || '',
                desc: item.desc || '',
              }));
            }
          } else {
            Message.error({
              content: response.data.err || t('get_data_failed'),
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error(t('get_data_failed'));
        } finally {
          isLoading.value = false;
        }
      };

      // 显示新增模态框
      const showAddModal = () => {
        addFormData.ip = '';
        addFormData.mask = '';
        addFormData.desc = '';
        isAddModalVisible.value = true;
      };

      // 处理导入确认
      const handleAddBeforeOk = async (done: any) => {
        if (!selectedFileName.value) {
          Message.warning(t('please_select_file_to_import'));
          done(false);
          return;
        }

        // 这里应该处理文件上传和解析逻辑
        Message.success(t('import_success'));
        isAddModalVisible.value = false;
        done(true);
      };

      // 处理新增取消
      const handleAddCancel = () => {
        selectedFileName.value = '';
        fileList.value = [];
        isAddModalVisible.value = false;
      };

      // 文件选择处理
      const handleFileChange = (fileList: any) => {
        if (fileList.length > 0) {
          selectedFileName.value = fileList[0].name;
        } else {
          selectedFileName.value = '';
        }
      };

      // 下载样例文件
      const downloadSample = () => {
        const content =
          '********** 00-00-00-8B-3E-76\n10.10.10.2 00-00-00-8A-C9-20\n10.10.10.3 00-00-00-8A-74-13';
        const blob = new Blob([content], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'sample.txt';
        a.click();
        window.URL.revokeObjectURL(url);
      };

      // 导出功能
      const handleExport = () => {
        Message.info(t('export_function_not_implemented'));
      };

      // 导出结果功能
      const handleExportResult = () => {
        Message.info(t('export_result_function_not_implemented'));
      };

      // 添加记录（在指定行下方添加新行）
      const addRecord = (rowIndex: number) => {
        const newRecord: FixedAddressItem = {
          ip: '',
          mask: '',
          desc: '',
        };

        // 在指定位置插入新记录
        data.value.splice(rowIndex + 1, 0, newRecord);
        Message.success(t('new_row_added_please_fill_info'));
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        isLoading,
        isAddModalVisible,
        addFormData,
        addFormRef,
        addRules,
        showAddModal,
        handleAddBeforeOk,
        handleAddCancel,
        handleExport,
        handleExportResult,
        addRecord,
        // 导入相关
        fileList,
        selectedFileName,
        formatDescription,
        uploadRef,
        handleFileChange,
        downloadSample,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .header-section {
    margin-bottom: 20px;
  }

  .title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 500;
    color: #1d2129;
  }

  :deep(.arco-table-th) {
    text-align: center;
    background-color: #f7f8fa;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }

  :deep(.arco-table-td input) {
    text-align: center;
    border: none;
    background: transparent;
  }

  :deep(.arco-table-td input:focus) {
    border: 1px solid #165dff;
    background: #fff;
  }
</style>
