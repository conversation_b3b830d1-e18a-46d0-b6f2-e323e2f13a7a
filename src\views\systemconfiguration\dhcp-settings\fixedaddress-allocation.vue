<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- 标题和按钮区域 -->
    <div class="header-section">
      <h3 class="title">定义dhcp pool需要固定分配地址的MAC地址</h3>
      <a-row style="margin-bottom: 16px">
        <a-col :span="24">
          <a-space>
            <a-button type="primary" @click="showAddModal">
              <template #icon>
                <icon-plus />
              </template>
              新增
            </a-button>
            <a-button @click="handleExport">
              <template #icon>
                <icon-export />
              </template>
              导出
            </a-button>
            <a-button @click="handleExportResult">
              <template #icon>
                <icon-download />
              </template>
              导出结果
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!-- 列表 -->
    <a-table
      row-key="ip"
      :columns="columns"
      :data="data"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
    >
      <template #ip="{ record }">
        <a-input v-model="record.ip" placeholder="请输入IP地址" size="small" />
      </template>
      <template #mask="{ record }">
        <a-input
          v-model="record.mask"
          placeholder="请输入MAC地址"
          size="small"
        />
      </template>
      <template #desc="{ record }">
        <a-input v-model="record.desc" placeholder="请输入描述" size="small" />
      </template>
      <template #operation="{ record, rowIndex }">
        <a-button type="primary" size="small" @click="addRecord(rowIndex)">
          <template #icon>
            <icon-plus />
          </template>
          添加
        </a-button>
      </template>
    </a-table>

    <!-- 新增模态框 -->
    <a-modal
      v-model:visible="isAddModalVisible"
      :width="600"
      title="新增固定地址分配"
      draggable
      :mask-closable="false"
      :unmount-on-close="false"
      @before-ok="handleAddBeforeOk"
      @cancel="handleAddCancel"
    >
      <a-form :model="addFormData" :rules="addRules" ref="addFormRef">
        <a-form-item label="IP地址" field="ip">
          <a-input
            v-model="addFormData.ip"
            style="width: 200px"
            placeholder="请输入IP地址"
          />
        </a-form-item>
        <a-form-item label="MAC地址" field="mask">
          <a-input
            v-model="addFormData.mask"
            style="width: 200px"
            placeholder="请输入MAC地址"
          />
        </a-form-item>
        <a-form-item label="描述" field="desc">
          <a-input
            v-model="addFormData.desc"
            style="width: 200px"
            placeholder="请输入描述"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';

  interface FixedAddressItem {
    ip: string;
    mask: string;
    desc: string;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: 'IP地址',
          dataIndex: 'ip',
          slotName: 'ip',
          align: 'center',
          width: 200,
        },
        {
          title: 'MAC地址',
          dataIndex: 'mask',
          slotName: 'mask',
          align: 'center',
          width: 200,
        },
        {
          title: '描述',
          dataIndex: 'desc',
          slotName: 'desc',
          align: 'center',
          width: 200,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          slotName: 'operation',
          align: 'center',
          width: 100,
        },
      ];

      const data = ref<FixedAddressItem[]>([]);
      const isLoading = ref(false);
      const isAddModalVisible = ref(false);

      // 新增表单数据
      const addFormData = reactive({
        ip: '',
        mask: '',
        desc: '',
      });

      // 新增表单引用
      const addFormRef = ref();

      // 新增表单验证规则
      const addRules = {
        ip: [
          { required: true, message: 'IP地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        mask: [
          { required: true, message: 'MAC地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
              if (!value || !macRegex.test(value)) {
                callback('MAC地址格式不正确，应为：11:22:33:44:55:66');
              } else {
                callback();
              }
            },
          },
        ],
      };

      // 获取数据
      const fetchData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'dhcp_fix' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              data.value = response.data.data.map((item: any) => ({
                ip: item.ip || '',
                mask: item.mask || '',
                desc: item.desc || '',
              }));
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        } finally {
          isLoading.value = false;
        }
      };

      // 显示新增模态框
      const showAddModal = () => {
        addFormData.ip = '';
        addFormData.mask = '';
        addFormData.desc = '';
        isAddModalVisible.value = true;
      };

      // 处理新增确认
      const handleAddBeforeOk = async (done: any) => {
        try {
          const errors = await addFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          // 这里可以调用新增API
          // const response = await axios.post(...);

          // 暂时直接添加到列表中
          data.value.push({
            ip: addFormData.ip,
            mask: addFormData.mask,
            desc: addFormData.desc,
          });

          Message.success('新增成功');
          done(true);
        } catch (error) {
          console.error('新增失败:', error);
          Message.error('新增失败');
          done(false);
        }
      };

      // 处理新增取消
      const handleAddCancel = () => {
        isAddModalVisible.value = false;
      };

      // 导出功能
      const handleExport = () => {
        Message.info('导出功能暂未实现');
      };

      // 导出结果功能
      const handleExportResult = () => {
        Message.info('导出结果功能暂未实现');
      };

      // 添加记录（在指定行下方添加新行）
      const addRecord = (rowIndex: number) => {
        const newRecord: FixedAddressItem = {
          ip: '',
          mask: '',
          desc: '',
        };

        // 在指定位置插入新记录
        data.value.splice(rowIndex + 1, 0, newRecord);
        Message.success('已添加新行，请填写相关信息');
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        isLoading,
        isAddModalVisible,
        addFormData,
        addFormRef,
        addRules,
        showAddModal,
        handleAddBeforeOk,
        handleAddCancel,
        handleExport,
        handleExportResult,
        addRecord,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .header-section {
    margin-bottom: 20px;
  }

  .title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 500;
    color: #1d2129;
  }

  :deep(.arco-table-th) {
    text-align: center;
    background-color: #f7f8fa;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }

  :deep(.arco-table-td input) {
    text-align: center;
    border: none;
    background: transparent;
  }

  :deep(.arco-table-td input:focus) {
    border: 1px solid #165dff;
    background: #fff;
  }
</style>
