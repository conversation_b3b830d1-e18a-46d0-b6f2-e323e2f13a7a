import type { Router, LocationQueryRaw } from 'vue-router';
import NProgress from 'nprogress'; // progress bar

import { useUserStore, useAppStore } from '@/store';
import { isLogin } from '@/utils/auth';

export default function setupUserLoginInfoGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    NProgress.start();
    const userStore = useUserStore();
    const appStore = useAppStore();

    if (isLogin()) {
      // 检查是否需要重新获取用户信息
      const needFetchUserInfo =
        !userStore.role || (userStore.role && from.fullPath === '/');

      if (needFetchUserInfo) {
        try {
          await userStore.info();

          // 强制获取最新的用户权限
          await userStore.fetchUserPermissions(false);

          // 强制重新加载菜单配置
          if (appStore.menuFromServer) {
            await appStore.fetchServerMenuConfig(false);
          }

          next();
        } catch (error) {
          await userStore.logout();
          next({
            name: 'login',
            query: {
              redirect: to.name,
              ...to.query,
            } as LocationQueryRaw,
          });
        }
      } else {
        // 确保用户权限已加载
        if (userStore.permissions.length === 0) {
          await userStore.fetchUserPermissions(false);

          // 如果菜单为空，重新加载菜单配置
          if (appStore.appAsyncMenus.length === 0 && appStore.menuFromServer) {
            await appStore.fetchServerMenuConfig(false);
          }
        }
        next();
      }
    } else {
      if (to.name === 'login') {
        next();
        return;
      }
      next({
        name: 'login',
        query: {
          redirect: to.name,
          ...to.query,
        } as LocationQueryRaw,
      });
    }
  });
}
