<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- 列表 -->
    <a-table
      row-key="ip"
      :columns="columns"
      :data="data"
      style="margin-top: 20px"
      :pagination="false"
    >
      <template #local_forward="{ record }">
        <a-radio-group v-model="record.local_forward">
          <a-radio value="enable">开启</a-radio>
          <a-radio value="disable">关闭</a-radio>
        </a-radio-group>
      </template>
      <template #block_same_cvlan="{ record }">
        <a-radio-group v-model="record.block_same_cvlan">
          <a-radio value="enable">是</a-radio>
          <a-radio value="disable">否</a-radio>
        </a-radio-group>
      </template>
      <template #operation="{ record }">
        <a-button type="primary" @click="editRecord(record)">
          <template #icon>
            <icon-edit />
          </template>
          编辑
        </a-button>
      </template>
    </a-table>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidVlanId } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: 'IP地址',
          width: 100,
          align: 'center',
          dataIndex: 'ip',
        },
        {
          title: '子网掩码',
          dataIndex: 'mask',
          align: 'center',
          width: 100,
        },
        {
          title: '端口',
          dataIndex: 'port',
          align: 'center',
          width: 100,
        },
        {
          title: '绑定vlan',
          dataIndex: 'vlan',
          align: 'center',
          width: 100,
        },
        {
          title: '二层转发',
          dataIndex: 'local_forward',
          width: 100,
          align: 'center',
          slotName: 'local_forward',
        },
        {
          title: '内层vlan相同不转发',
          dataIndex: 'block_same_cvlan',
          width: 100,
          align: 'center',
          slotName: 'block_same_cvlan',
        },
        {
          title: '操作',
          dataIndex: 'operation',
          slotName: 'operation',
          align: 'center',
          width: 100,
        },
      ];

      const data = ref<
        Array<{
          ip: string;
          mask: string;
          local_forward: string;
          block_same_cvlan: string;
        }>
      >([]);

      const isRefreshing = ref(false);

      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_other.lua',
            new URLSearchParams({ act: 'forward' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              data.value = response.data.data;
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 编辑记录
      const editRecord = async (record) => {
        try {
          // 克隆一个原始记录对象以便后续比对或回滚
          const originalRecord = { ...record };

          // 提交编辑的数据到服务器
          const response = await axios.post(
            '/lua/set_cfg_other.lua',
            new URLSearchParams({
              act: 'forward',
              act_type: 'mod',
              ip: record.ip,
              mask: record.mask,
              local_forward: record.local_forward,
              block_same_cvlan: record.block_same_cvlan,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success('修改成功');
            // 刷新数据
            fetchData();
          } else {
            Message.error({
              content: response.data.err || '修改失败',
              duration: 5000,
            });
            // 如果失败，可以考虑恢复原始数据
            Object.assign(record, originalRecord);
          }
        } catch (error) {
          console.error('编辑失败:', error);
          Message.error('编辑请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        editRecord,
        isRefreshing,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
