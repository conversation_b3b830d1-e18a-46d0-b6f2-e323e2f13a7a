<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-space>
          <!-- 新建按钮 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            添加mac厂家地址头
          </a-button>
        </a-space>
      </a-col>
      <!-- 右侧按钮 -->
      <a-col
        :span="12"
        style="display: flex; align-items: center; justify-content: end"
      >
        <!-- 刷新按钮 -->
        <a-tooltip content="刷新">
          <div class="action-icon" @click="handleRefresh">
            <icon-refresh size="18" />
          </div>
        </a-tooltip>
      </a-col>
    </a-row>

    <!-- 列表 -->
    <a-table
      :columns="columns"
      :data="data"
      style="margin-top: 20px"
      :pagination="false"
    >
      <template #operation="{ index }">
        <a-button status="danger" @click="deleteRow(index)">
          <template #icon>
            <icon-delete />
          </template>
          <template #default>删除</template>
        </a-button>
      </template>
    </a-table>
  </a-card>

  <a-modal
    v-model:visible="isModalVisible"
    title="mac厂家地址头"
    :width="700"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-form :model="formData" :rules="rules" ref="formRef">
      <a-form-item label="mac厂家地址头" field="data">
        <a-input v-model="formData.data" style="width: 200px" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidMacHead } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns = [
        {
          title: 'mac厂家地址头',
          dataIndex: 'data',
        },
        {
          title: '操作',
          dataIndex: 'operation',
          slotName: 'operation',
        },
      ];

      const data = ref<{ data: string }[]>([]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);

      // 表单数据
      const formData = reactive({
        data: '',
      });

      // 表单规则
      const rules = {
        data: [
          { required: true, message: 'mac厂家地址头不能为空' },
          {
            validator: (value, callback) => {
              if (!isValidMacHead(value)) {
                callback('格式不正确，请输入形如00-07-74-XX的格式');
              } else {
                callback();
              }
            },
          },
        ],
      };

      const formRef = ref();

      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_qinq_bind_mac.lua',
            new URLSearchParams({ act: 'blist_mac' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              data.value = response.data.data.map((item) => ({ data: item }));
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 刷新数据
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 显示模态框
      const showModal = () => {
        formData.data = '';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleBeforeOk = async (done) => {
        try {
          const errors = await formRef.value.validate();
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 添加到列表
          data.value.push({
            data: formData.data,
          });

          Message.success('添加成功');
          done(true); // 允许模态框关闭
        } catch (err) {
          console.error('表单验证异常:', err);
          done(false);
        }
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      // 删除行
      const deleteRow = (index: number) => {
        data.value.splice(index, 1);
        Message.success('删除成功');
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        handleRefresh,
        isRefreshing,
        formData,
        rules,
        formRef,
        isModalVisible,
        showModal,
        handleBeforeOk,
        handleCancel,
        deleteRow,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
