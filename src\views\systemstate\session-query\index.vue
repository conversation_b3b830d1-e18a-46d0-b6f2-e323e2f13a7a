<!-- 会话查询组件 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.session-query']" />
    <a-card title="">
      <div class="session">
        <span>会话信息：</span>
        <a-select :style="{width:'200px'}" @change="handleOptionChange"  v-model="formData.sessionType">
          <a-option value="ip">IP会话</a-option>
          <a-option value="pppoe">PPPOE Server会话</a-option>
          <a-option value="oeclient">PPPOE用户名找IP会话</a-option>
        </a-select>
        &nbsp;
        <span>{{ promptText }}</span>
        <a-input
          v-model="formData.session_name"
          placeholder=""
          :style="{ width: '200px' }"
        />
        <a-button type="primary" style="margin-left: 10px" @click="fetchData">
          <template #icon>
            <icon-search />
          </template>
          <template #default>查询</template>
        </a-button>
        <a-button
          id="closeSession"
          :disabled="!hasPermission('closeSession')"
          type="primary"
          style="margin-left: 10px"
          @click="closeSession"
        >
          <template #icon>
            <icon-close />
          </template>
          <template #default>强制下线</template>
        </a-button>
        
        <div :style="{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            gap: '16px',
            '--card-width': 'calc(50% - 8px)'
          }" class="card-container">
          <a-card 
            v-for="(item, index) in data" 
            :key="index"
            :style="{ width: 'calc(50% - 8px)' }" 
            title=""
          >
            <a-descriptions 
              style="margin-top: 20px" 
              :data="item" 
              title="" 
              :column="1"
            />
          </a-card>
        </div>
        
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { ref, reactive, onMounted, computed, defineComponent } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import usePermission from '@/hooks/permission';

  export default defineComponent({
    setup() {
      
      const { hasPermission } = usePermission();
      const promptText = ref('IP地址/用户名：');
      const session_ip = ref('');
      
      const handleOptionChange = (value: string) =>{
        promptText.value = value === 'oeclient' 
            ? 'PPPOE用户名：' 
            : 'IP地址/用户名：';
      };
      
      const data = ref([]);
      const ip = ref(''); // 响应式变量，用于存储用户输入的 IP 地址
      const formData = reactive({
        session_name: '',
        sessionType: '',
      });

      // 清空会话数据方法
      const clearSessionData = () => {
        data.value = [];
      };

      const fetchData = async () => {
        if (!formData.session_name) {
          Message.warning('请选择会话信息');
          return;
        }
        if (!formData.sessionType) {
          Message.warning('请输入');
          return;
        }
        try {
          const response = await axios.post(
            '/lua/system_info.lua',
            new URLSearchParams({
              act: 'session_search',
              session_name: formData.session_name,
              sessionType: formData.sessionType
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          // console.log(response);
          if (response.data.code === 200) {
            const formattedData: Record<string, any>[] = [];
            let currentPortData: Record<string, any> = [];
            let i = 0;
            response.data.data.forEach((item) => {         
              if(item.describe == "IP 地址"){
                session_ip.value = item.data;
              }
              
              if(i%5 == 0 && i > 1){
                formattedData.push(currentPortData);
                currentPortData = [];
              }
              
              currentPortData.push({'label': item.describe, 'value': item.data});             
              i++;
            });  
            if(currentPortData.length > 0)   
              formattedData.push(currentPortData);
            
            data.value = formattedData;
            // console.log(formattedData)
          } else {
            // 清空会话数据
            clearSessionData();
            Message.error(response.data.err || '查询失败');
          }
        } catch (error) {
          console.error('获取数据时出错:', error);
          clearSessionData();
          Message.error('查询会话请求失败');
        }
      };

      // 关闭会话方法
      const closeSession = async () => {
        if (!session_ip.value) {
          Message.warning('没有该IP地址：' + session_ip.value);
          return;
        }

        try {
          const response = await axios.post(
            '/lua/system_control.lua',
            new URLSearchParams({
              act: 'dis_session',
              ip: session_ip.value,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.success || '下线成功');
            // 关闭成功后清空数据
            clearSessionData();
          } else {
            Message.error(response.data.err || '下线失败');
          }
        } catch (error) {
          console.error('关闭会话错误:', error);
          Message.error('关闭会话请求失败');
        }
      };
    return {
      promptText,
      handleOptionChange,
      fetchData,
      hasPermission,
      ip,
      closeSession,
      data,
      formData
    };
},
  });
</script>

<style scoped>
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .session,
  .session2 {
    text-align: left;
    background-color: #fff;
    padding: 5px;
    white-space: nowrap;
  }

  .session {
    margin-bottom: 20px;
  }
  span {
    font-size: 20px;
  }
</style>
