<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.NAT_Control']" />

    <a-card title="NAT控制">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-col :span="8">
          <a-card title="NAT控制" :bordered="false" :style="{ width: '100%' }">
            <template #extra> </template>
            <div class="session">
              <div class="form-item">
                <span>UDP连接闲置断开时间：</span>
                <a-tooltip
                  content="单位(秒)，不填缺省为30"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.udp_timeout"
                    class="input-field"
                    :status="udpTimeoutError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="udpTimeoutError" class="error-message">{{
                  udpTimeoutError
                }}</div>
              </div>
              <div class="form-item">
                <span>TCP连接闲置断开时间：</span>
                <a-tooltip
                  content="单位：毫秒，不填缺省为180"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.tcp_timeout"
                    class="input-field"
                    :status="tcpTimeoutError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="tcpTimeoutError" class="error-message">{{
                  tcpTimeoutError
                }}</div>
              </div>
              <div class="form-item">
                <span>每次最大删除连接数：</span>
                <a-tooltip
                  content="不填缺省为2048"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.max_del_num_ps"
                    class="input-field"
                    :status="maxDelNumPsError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="maxDelNumPsError" class="error-message">{{
                  maxDelNumPsError
                }}</div>
              </div>
              <div class="form-item">
                <span>每个IP最大连接数：：</span>
                <a-tooltip
                  content="不填缺省为2048"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.max_con_num_pip"
                    class="input-field"
                    :status="maxConNumPipError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="maxConNumPipError" class="error-message">{{
                  maxConNumPipError
                }}</div>
              </div>
              <div class="form-item">
                <span>调整TCP通信MSS：</span>
                <a-tooltip
                  content="不填缺省为0，表示不启动该功能"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.tcp_mms"
                    class="input-field"
                    :status="tcpMmsError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="tcpMmsError" class="error-message">{{
                  tcpMmsError
                }}</div>
              </div>
              <br />
              <div class="form-item">
                <span>内网IP绑定外网映射地址：</span>
                <a-switch
                  v-model="bindSwitchValue"
                  @change="handleBindSwitchChange"
                />
                <span class="status-text">{{
                  bindSwitchValue ? '是' : '否'
                }}</span>
              </div>
              <br />
              <div class="form-item">
                <span
                  >拨号连接端口支持：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span
                >
                <a-switch
                  v-model="forwardSwitchValue"
                  @change="handleForwardSwitchChange"
                />
                <span class="status-text">{{
                  forwardSwitchValue ? '启动' : '关闭'
                }}</span>
              </div>
              <br />
              <div class="button">
                <a-button
                  id="natControlSubmit"
                  type="primary"
                  :disabled="!hasPermission('natControlSubmit')"
                  @click="handleNotification1"
                >
                  <template #icon>
                    <icon-check />
                  </template>
                  <template #default>提交</template>
                </a-button>

                <a-button
                  type="secondary"
                  style="margin-left: 10px"
                  @click="handleNotification2"
                >
                  <template #icon>
                    <icon-refresh />
                  </template>
                  <template #default>重置</template>
                </a-button>
              </div>
            </div>
          </a-card>
        </a-col>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import {
    handleNotification4,
    handleNotification1,
    handleNotification2 as handleNotification2Original,
  } from '../../../utils/info';

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formData = reactive({
        udp_timeout: '',
        tcp_timeout: '',
        max_del_num_ps: '',
        max_con_num_pip: '',
        tcp_mms: '',
        nat_bind_fix_ip: '',
        forward: '',
      });

      // 添加错误状态
      const udpTimeoutError = ref('');
      const tcpTimeoutError = ref('');
      const maxDelNumPsError = ref('');
      const maxConNumPipError = ref('');
      const tcpMmsError = ref('');

      // 开关状态值
      const bindSwitchValue = ref(false);
      const forwardSwitchValue = ref(false);

      // 验证数字输入
      const validateNumberInput = (
        input: string,
        min: number,
        max: number
      ): boolean => {
        if (!input) return true; // 允许为空，使用默认值
        const num = Number(input);
        return !Number.isNaN(num) && num >= min && num <= max;
      };

      // 处理内网IP绑定开关变化
      const handleBindSwitchChange = (value: boolean) => {
        formData.nat_bind_fix_ip = value ? 'enable' : 'disable';
      };

      // 处理拨号连接端口支持开关变化
      const handleForwardSwitchChange = (value: boolean) => {
        formData.forward = value ? 'enable' : 'disable';
      };

      onMounted(async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'nat_control' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            formData.udp_timeout = response.data.data.udp_timeout;
            formData.tcp_timeout = response.data.data.tcp_timeout;
            formData.max_del_num_ps = response.data.data.max_del_num_ps;
            formData.max_con_num_pip = response.data.data.max_con_num_pip;
            formData.tcp_mms = response.data.data.tcp_mms;
            formData.nat_bind_fix_ip = response.data.data.nat_bind_fix_ip;
            formData.forward = response.data.data.forward;

            // 设置开关初始状态
            bindSwitchValue.value = formData.nat_bind_fix_ip === 'enable';
            forwardSwitchValue.value = formData.forward === 'enable';
          } else {
            console.error('Failed to fetch data:', response.data);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      });

      // 校验所有字段
      const validateAllFields = (): boolean => {
        let isValid = true;

        // 验证UDP连接闲置断开时间
        if (
          formData.udp_timeout &&
          !validateNumberInput(formData.udp_timeout, 1, 3600)
        ) {
          udpTimeoutError.value = '请输入1-3600之间的有效秒数';
          isValid = false;
        } else {
          udpTimeoutError.value = '';
        }

        // 验证TCP连接闲置断开时间
        if (
          formData.tcp_timeout &&
          !validateNumberInput(formData.tcp_timeout, 1, 3600)
        ) {
          tcpTimeoutError.value = '请输入1-3600之间的有效秒数';
          isValid = false;
        } else {
          tcpTimeoutError.value = '';
        }

        // 验证每次最大删除连接数
        if (
          formData.max_del_num_ps &&
          !validateNumberInput(formData.max_del_num_ps, 1, 10000)
        ) {
          maxDelNumPsError.value = '请输入1-10000之间的有效数字';
          isValid = false;
        } else {
          maxDelNumPsError.value = '';
        }

        // 验证每个IP最大连接数
        if (
          formData.max_con_num_pip &&
          !validateNumberInput(formData.max_con_num_pip, 1, 10000)
        ) {
          maxConNumPipError.value = '请输入1-10000之间的有效数字';
          isValid = false;
        } else {
          maxConNumPipError.value = '';
        }

        // 验证TCP通信MSS
        if (
          formData.tcp_mms &&
          !validateNumberInput(formData.tcp_mms, 0, 1500)
        ) {
          tcpMmsError.value = '请输入0-1500之间的有效数字';
          isValid = false;
        } else {
          tcpMmsError.value = '';
        }

        return isValid;
      };

      const submitData = async () => {
        try {
          if (!hasPermission('natControlSubmit')) {
            Message.error('您没有权限');
            return;
          }

          // 在提交前验证所有字段
          if (!validateAllFields()) {
            Message.error({
              content: '请修正表单中的错误再提交',
              duration: 5000,
            });
            return;
          }

          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'nat_control',
              udp_timeout: formData.udp_timeout,
              tcp_timeout: formData.tcp_timeout,
              max_del_num_ps: formData.max_del_num_ps,
              max_con_num_pip: formData.max_con_num_pip,
              tcp_mms: formData.tcp_mms,
              nat_bind_fix_ip: formData.nat_bind_fix_ip,
              forward: formData.forward,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            Message.success(response.data.data.result);
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Error setting data:', error);
        }
      };

      // 自定义重置函数
      const resetForm = () => {
        formData.udp_timeout = '';
        formData.tcp_timeout = '';
        formData.max_del_num_ps = '';
        formData.max_con_num_pip = '';
        formData.tcp_mms = '';
        formData.nat_bind_fix_ip = 'disable';
        formData.forward = 'disable';

        // 重置开关状态
        bindSwitchValue.value = false;
        forwardSwitchValue.value = false;

        // 清除错误状态
        udpTimeoutError.value = '';
        tcpTimeoutError.value = '';
        maxDelNumPsError.value = '';
        maxConNumPipError.value = '';
        tcpMmsError.value = '';

        // 可以选择是否调用原始的handleNotification2函数
        // handleNotification2Original();
      };

      return {
        handleNotification4,
        handleNotification1: submitData, // 更新按钮点击事件处理函数
        handleNotification2: resetForm, // 使用自定义的重置函数
        formData,
        bindSwitchValue,
        forwardSwitchValue,
        handleBindSwitchChange,
        handleForwardSwitchChange,
        hasPermission,
        // 添加错误状态到返回值
        udpTimeoutError,
        tcpTimeoutError,
        maxDelNumPsError,
        maxConNumPipError,
        tcpMmsError,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .hint {
    flex: 1; /* 占据剩余空间 */
    text-align: left;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }

  /* 为上传按钮添加居中样式 */
  .upload {
    display: block;
    margin: 0 auto;
  }

  /* 为按钮添加居中样式 */
  .button {
    text-align: center;
  }

  .arco-input-wrapper {
    margin: 10px 0 !important;
  }

  .status-text {
    margin-left: 10px;
    font-size: 14px;
  }

  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>
