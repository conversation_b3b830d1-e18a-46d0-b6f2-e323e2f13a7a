<!-- 权限管理 -->
<template>
  <div v-if="isComponentVisible" class="container">
    <div v-if="isRefreshing" class="overlay">
      <div class="loader"></div>
    </div>
    <Breadcrumb :items="['menu.Operator management', 'menu.Role_Permission']" />
    <a-card class="general-card" :title="$t('menu.Role_Permission')">
      <!-- <a-row>
        <a-col :flex="1">
          <a-form
            :model="{ searchQuery }"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item
                  field="number"
                  :label="$t('permission_Group_name')"
                >
                  <a-input
                    v-model="searchQuery"
                    :placeholder="$t('searchTable.form.number.placeholder')"
                  />
                  <a-button
                    type="primary"
                    @click="filterData"
                    style="margin-left: 10px"
                  >
                    <template #icon>
                      <icon-search />
                    </template>
                    {{ $t('searchTable.form.search') }}
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-col :flex="'86px'" style="text-align: right"> </a-col>
      </a-row>
      <a-divider style="margin-top: 0" /> -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <!-- 新建按钮 -->
            <a-button
              id="operationPermissionsAdd"
              :disabled="!hasPermission('operationPermissionsAdd')"
              type="primary"
              @click="showModal"
            >
              <template #icon>
                <icon-plus />
              </template>
              {{ $t('searchTable.operation.create') }}
            </a-button>
          </a-space>
        </a-col>
        <!-- 右侧按钮 -->
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip :content="$t('searchTable.actions.refresh')">
            <div class="action-icon" @click="handleRefresh">
              <icon-refresh size="18" />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>
      <!-- 列表 -->
      <a-table
        :columns="columns"
        :data="paginatedData"
        style="margin-top: 20px"
        :pagination="false"
      >
        <template #gid="{ record }">
          <span>{{ record.gid }}</span>
        </template>

        <template #name="{ record }">
          <span>{{ record.name }}</span>
        </template>

        <template #resources="{ record }">
          <span>{{ formatResources(record.resources) }}</span>
        </template>

        <template #option="{ record }">
          <a-space>
            <a-button
              id="operationPermissionsEdit"
              :disabled="
                (record.gid === '1' &&
                  userStore.userInfo?.accountId != 'admin') ||
                !hasPermission('operationPermissionsEdit')
              "
              type="primary"
              @click="editPermission(record)"
            >
              <template #icon>
                <icon-edit />
              </template>
              <template #default>编辑</template>
            </a-button>
            <a-button
              id="operationPermissionsDelete"
              :disabled="
                record.gid === '1' ||
                !hasPermission('operationPermissionsDelete')
              "
              type="primary"
              status="danger"
              @click="showDeleteConfirm(record)"
            >
              <template #icon>
                <icon-delete />
              </template>
              <template #default>删除</template>
            </a-button>
          </a-space>
        </template>
      </a-table>

      <a-pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="filteredData.length"
        show-total
        show-size-changer
        show-jumper
        show-page-size
        style="margin-top: 20px"
        @change="handlePageChange"
      />
    </a-card>

    <a-modal
      v-model:visible="isModalVisible"
      :title="isEdit ? '编辑权限组' : '新建权限组'"
      :width="700"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules">
        <a-form-item label="权限组名称" field="name">
          <a-input v-model="formData.name" />
        </a-form-item>
        <a-form-item label="页面菜单权限" field="resources">
          <a-tree
            v-model:checked-keys="formData.resources"
            v-model:half-checked-keys="formData.halfCheckedKeys"
            :data="menuTree"
            checkable
            :default-expand-all="true"
            :check-strictly="true"
            :field-names="{
              key: 'id',
              title: 'name',
              children: 'children',
            }"
            @check="handleMenuCheck"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 删除确认弹窗 -->
    <a-modal
      v-model:visible="isDeleteConfirmVisible"
      title="确认删除"
      @ok="confirmDelete"
      @cancel="cancelDelete"
    >
      <p>确定要删除权限组 "{{ deleteRecord.name }}" 吗？此操作不可撤销。</p>
    </a-modal>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, computed } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore, useAppStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';

  interface PermissionGroup {
    gid: string;
    name: string;
    resources: string[];
  }

  interface MenuItem {
    id: string;
    name: string;
    children?: MenuItem[];
  }

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const appStore = useAppStore();
      const { hasPermission } = usePermission();
      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: '权限组ID',
          dataIndex: 'gid',
          align: 'center',
          slotName: 'gid',
          width: 100,
        },
        {
          title: '权限组名称',
          dataIndex: 'name',
          slotName: 'name',
          align: 'center',
          width: 150,
        },
        // {
        //   title: '页面菜单权限',
        //   dataIndex: 'resources',
        //   slotName: 'resources',
        //   align: 'center',
        // },
        {
          title: '操作',
          dataIndex: 'option',
          align: 'center',
          slotName: 'option',
          width: 200,
        },
      ];

      const data = ref<PermissionGroup[]>([]);
      const filteredData = ref<PermissionGroup[]>([]);
      const menuTree = ref<MenuItem[]>([]);
      const allMenus = ref<Record<string, string>>({});

      const isModalVisible = ref(false);
      const isEdit = ref(false);
      const formData = reactive({
        gid: '',
        name: '',
        resources: [] as string[],
        halfCheckedKeys: [] as string[],
      });

      // 删除确认相关变量
      const isDeleteConfirmVisible = ref(false);
      const deleteRecord = ref<PermissionGroup>({} as PermissionGroup);

      const rules = {
        name: [{ required: true, message: '权限组名称不能为空' }],
        resources: [{ required: true, message: '请选择至少一个页面菜单权限' }],
      };

      const formRef = ref();
      const isComponentVisible = ref(true);
      const isRefreshing = ref(false);
      const searchQuery = ref('');

      const currentPage = ref(1);
      const pageSize = ref(10);

      const paginatedData = computed(() => {
        const start = (currentPage.value - 1) * pageSize.value;
        return filteredData.value.slice(start, start + pageSize.value);
      });

      const handlePageChange = () => {
        // 前端分页无需额外操作
        //console.log('当前页:', currentPage.value);
      };

      // 判断菜单是否需要刷新并执行刷新
      const refreshMenuIfNeeded = async () => {
        try {
          // 清除菜单缓存并重新获取
          appStore.clearServerMenu();

          // 使用强制刷新选项，确保菜单被重新加载
          await appStore.fetchServerMenuConfig(true);

          // 重新获取当前用户权限，使用强制刷新选项
          await userStore.fetchUserPermissions(true);

          //Message.success('菜单已更新');
        } catch (error) {          
          if (process.env.NODE_ENV === 'development') {
            console.error('刷新菜单失败:', error);
            //Message.error('菜单更新失败');
          }
        }
      };

      // 格式化资源显示
      const formatResources = (resources: string[]) => {
        if (!resources || resources.length === 0) return '无权限';
        return resources.map((id) => allMenus.value[id] || id).join(', ');
      };

      // 将扁平的菜单数据转换为树形结构
      const transformMenuToTree = (menuData: any[]): MenuItem[] => {
        const result: MenuItem[] = [];
        const menuMap: Record<string, MenuItem> = {};

        // 首先创建所有菜单项的映射
        menuData.forEach((item) => {
          menuMap[item.id] = {
            id: item.id,
            name: item.name,
            children: [],
          };
        });

        // 然后构建树形结构
        menuData.forEach((item) => {
          if (item.submenu) {
            // 如果有子菜单，递归处理
            const children = transformMenuToTree(item.submenu);
            menuMap[item.id].children = children;
          }

          // 如果有 action，将其添加为子节点
          if (item.action) {
            const actionChildren = transformMenuToTree(item.action);
            if (menuMap[item.id].children) {
              menuMap[item.id].children = [
                ...menuMap[item.id].children,
                ...actionChildren,
              ];
            } else {
              menuMap[item.id].children = actionChildren;
            }
          }

          // 将顶级菜单添加到结果中
          if (!item.parentId) {
            result.push(menuMap[item.id]);
          }
        });

        return result;
      };

      // 获取所有菜单
      const fetchMenus = async () => {
        try {
          const response = await axios.post(
            '/lua/permission.lua',
            new URLSearchParams({
              act: 'get_menu',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            menuTree.value = transformMenuToTree(response.data.data.menu || []);

            const buildMenuMap = (items: MenuItem[]) => {
              items.forEach((item) => {
                allMenus.value[item.id] = item.name;
                if (item.children && item.children.length > 0) {
                  buildMenuMap(item.children);
                }
              });
            };

            buildMenuMap(menuTree.value);
          } else {
            Message.error(response.data?.result || '获取菜单失败');
          }
        } catch (error) {
          console.error('Fetch menus error:', error);
          Message.error('获取菜单失败');
        }
      };

      // 获取所有权限组
      const fetchPermissionGroups = async () => {
        try {
          isRefreshing.value = true;

          const response = await axios.post(
            '/lua/permission.lua',
            new URLSearchParams({
              act: 'get_group',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 检查response.data.data是否存在
            const groupData =
              response.data.data?.group || response.data.data.group || [];

            data.value =
              groupData.map((group: any) => {
                const resources =
                  group.menu?.map((item: any) => item.resource) || [];
                return {
                  gid: group.gid.toString(),
                  name: group.name,
                  resources,
                };
              }) || [];

            filteredData.value = [...data.value];
            
          } else {
            console.error('📢 权限组API返回数据为空');
            Message.error(response.data?.result || '获取权限组失败');
          }
        } catch (error) {
          console.error('📢 获取权限组失败，详细错误:', error);
          if (axios.isAxiosError(error)) {
            console.error('📢 请求配置:', error.config);
            console.error('📢 响应数据:', error.response?.data);
          }
          Message.error('获取权限组失败');
        } finally {
          isRefreshing.value = false;
        }
      };

      // 删除权限组
      const deletePermission = async (record: PermissionGroup) => {
        try {
          if (!hasPermission('operationPermissionsDelete')) {
            Message.error('您没有权限');
            return;
          }

          // 检查是否是超级管理员组(GID=1)且当前用户不是超级管理员
          // if (record.gid === '1' && !userStore.role.includes('超级管理员')) {
          //   Message.error('删除失败，只有超级管理员可以删除超级管理员权限组');
          //   return;
          // }

          const response = await axios.post(
            '/lua/permission.lua',
            new URLSearchParams({
              act: 'settings',
              set_type: 'del',
              gid: record.gid,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
         
          fetchPermissionGroups();
          if (response.data.code === 200) {
            await fetchPermissionGroups(); // 重新获取数据
            Message.success('删除成功');

            // 删除权限组后也刷新菜单
            await refreshMenuIfNeeded();
          } else {
            Message.error((response as any)?.result || '删除失败');
          }
        } catch (error) {
          console.error('Delete error:', error);
          Message.error('删除失败');
        }
      };

      // 显示删除确认弹窗
      const showDeleteConfirm = (record: PermissionGroup) => {
        if (!hasPermission('operationPermissionsDelete')) {
          Message.error('您没有权限');
          return;
        }

        // 检查是否是超级管理员组且当前用户不是超级管理员
        // if (record.gid === '1' && !userStore.role.includes('超级管理员')) {
        //   Message.error('删除失败，只有超级管理员可以删除超级管理员权限组');
        //   return;
        // }

        deleteRecord.value = record;
        isDeleteConfirmVisible.value = true;
      };

      // 确认删除
      const confirmDelete = () => {
        deletePermission(deleteRecord.value);
        isDeleteConfirmVisible.value = false;
      };

      // 取消删除
      const cancelDelete = () => {
        isDeleteConfirmVisible.value = false;
      };

      // 计算半选状态的节点
      const calculateHalfCheckedKeys = (checkedKeys: string[]) => {
        const halfCheckedKeys: string[] = [];
        const childToParentMap: Record<string, string[]> = {};
        const parentToChildrenMap: Record<string, string[]> = {};

        // 构建子节点到父节点的映射和父节点到子节点的映射
        const buildMaps = (items: MenuItem[], parentId?: string) => {
          items.forEach((item) => {
            if (parentId) {
              if (!childToParentMap[item.id]) {
                childToParentMap[item.id] = [];
              }
              childToParentMap[item.id].push(parentId);

              if (!parentToChildrenMap[parentId]) {
                parentToChildrenMap[parentId] = [];
              }
              parentToChildrenMap[parentId].push(item.id);
            }

            if (item.children && item.children.length > 0) {
              buildMaps(item.children, item.id);
            }
          });
        };

        buildMaps(menuTree.value);

        // 对于每个有子节点的父节点，检查其子节点的选中状态
        Object.keys(parentToChildrenMap).forEach((parentId) => {
          const children = parentToChildrenMap[parentId];
          const checkedChildren = children.filter((id) =>
            checkedKeys.includes(id)
          );

          // 如果部分子节点被选中但不是全部，父节点为半选状态
          if (
            checkedChildren.length > 0 &&
            checkedChildren.length < children.length
          ) {
            halfCheckedKeys.push(parentId);
          }
          // 如果所有子节点都被选中，父节点应该被完全选中
          else if (checkedChildren.length === children.length) {
            // 将父节点添加到选中列表中（如果不在里面）
            if (!checkedKeys.includes(parentId)) {
              checkedKeys.push(parentId);
            }
          }
        });

        return halfCheckedKeys;
      };

      // 编辑权限组
      const editPermission = async (record: PermissionGroup) => {
        if (!hasPermission('operationPermissionsEdit')) {
          Message.error('您没有权限');
          return;
        }

        // 检查是否是超级管理员组且当前用户不是超级管理员
        // if (record.gid !== '1' && !userStore.role.includes('超级管理员')) {
        //   Message.error('编辑失败，只有超级管理员可以编辑超级管理员权限组');
        //   return;
        // }

        isEdit.value = true;
        formData.gid = record.gid;
        formData.name = record.name;
        formData.resources = [...record.resources];

        // 计算半选状态的节点
        const halfCheckedKeys = calculateHalfCheckedKeys(formData.resources);
        formData.halfCheckedKeys = halfCheckedKeys;

        isModalVisible.value = true;
      };

      // 处理权限树节点选中事件
      /*const handleMenuCheck = (checkedKeys: string[], e: any) => {
        // 记录当前选中的keys
        const currentCheckedKeys = [...checkedKeys];

        // 获取所有父子节点的映射关系
        const childToParentMap: Record<string, string[]> = {};
        const parentToChildrenMap: Record<string, string[]> = {};

        // 递归构建映射关系
        const buildMaps = (items: MenuItem[], parentId?: string) => {
          items.forEach((item) => {
            if (parentId) {
              if (!childToParentMap[item.id]) {
                childToParentMap[item.id] = [];
              }
              childToParentMap[item.id].push(parentId);

              if (!parentToChildrenMap[parentId]) {
                parentToChildrenMap[parentId] = [];
              }
              parentToChildrenMap[parentId].push(item.id);
            }

            if (item.children && item.children.length > 0) {
              buildMaps(item.children, item.id);
            }
          });
        };

        buildMaps(menuTree.value);

        // 如果是选中操作
        if (e.checked) {
          // 如果选中的是父节点，添加所有子节点
          if (e.node.children && e.node.children.length > 0) {
            const getAllChildrenIds = (node: any): string[] => {
              let ids: string[] = [];
              if (node.children && node.children.length > 0) {
                node.children.forEach((child: any) => {
                  ids.push(child.id);
                  ids = [...ids, ...getAllChildrenIds(child)];
                });
              }
              return ids;
            };

            const childIds = getAllChildrenIds(e.node);
            currentCheckedKeys.push(...childIds);
          }
        } else if (e.node.children && e.node.children.length > 0) {
          // 如果取消选中一个父节点，移除所有子节点
          const getAllChildrenIds = (node: any): string[] => {
            let ids: string[] = [];
            if (node.children && node.children.length > 0) {
              node.children.forEach((child: any) => {
                ids.push(child.id);
                ids = [...ids, ...getAllChildrenIds(child)];
              });
            }
            return ids;
          };

          const childIds = getAllChildrenIds(e.node);
          childIds.forEach((id) => {
            const index = currentCheckedKeys.indexOf(id);
            if (index > -1) {
              currentCheckedKeys.splice(index, 1);
            }
          });
        }

        // 去重
        const uniqueCheckedKeys = Array.from(new Set(currentCheckedKeys));

        // 更新选中状态和半选状态
        formData.resources = uniqueCheckedKeys;
        formData.halfCheckedKeys = calculateHalfCheckedKeys(uniqueCheckedKeys);
      };
      */
     // 处理权限树节点选中事件
      const handleMenuCheck = (checkedKeys: string[], e: any) => {
        // 记录当前选中的keys
        const currentCheckedKeys = [...checkedKeys];

        // 获取所有父子节点的映射关系
        const childToParentMap: Record<string, string[]> = {};
        const parentToChildrenMap: Record<string, string[]> = {};

        // 递归构建映射关系
        const buildMaps = (items: MenuItem[], parentId?: string) => {
          items.forEach((item) => {
            if (parentId) {
              if (!childToParentMap[item.id]) {
                childToParentMap[item.id] = [];
              }
              childToParentMap[item.id].push(parentId);

              if (!parentToChildrenMap[parentId]) {
                parentToChildrenMap[parentId] = [];
              }
              parentToChildrenMap[parentId].push(item.id);
            }

            if (item.children && item.children.length > 0) {
              buildMaps(item.children, item.id);
            }
          });
        };

        buildMaps(menuTree.value);

        // 如果是选中操作
        if (e.checked) {
          // 1. 如果选中的是最底层节点，自动选中所有父节点
          if (!e.node.children || e.node.children.length === 0) {
            const getAllParentIds = (nodeId: string): string[] => {
              let parentIds: string[] = [];
              if (childToParentMap[nodeId]) {
                childToParentMap[nodeId].forEach((parentId) => {
                  parentIds.push(parentId);
                  parentIds = [...parentIds, ...getAllParentIds(parentId)];
                });
              }
              return parentIds;
            };

            const parentIds = getAllParentIds(e.node.id);
            currentCheckedKeys.push(...parentIds);
          }
          
          // 2. 如果选中的是父节点，添加所有子节点
          if (e.node.children && e.node.children.length > 0) {
            const getAllChildrenIds = (node: any): string[] => {
              let ids: string[] = [];
              if (node.children && node.children.length > 0) {
                node.children.forEach((child: any) => {
                  ids.push(child.id);
                  ids = [...ids, ...getAllChildrenIds(child)];
                });
              }
              return ids;
            };

            const childIds = getAllChildrenIds(e.node);
            currentCheckedKeys.push(...childIds);
          }
        } else {
          // 如果是取消选中操作
          if (e.node.children && e.node.children.length > 0) {
            // 如果取消选中一个父节点，移除所有子节点
            const getAllChildrenIds = (node: any): string[] => {
              let ids: string[] = [];
              if (node.children && node.children.length > 0) {
                node.children.forEach((child: any) => {
                  ids.push(child.id);
                  ids = [...ids, ...getAllChildrenIds(child)];
                });
              }
              return ids;
            };

            const childIds = getAllChildrenIds(e.node);
            childIds.forEach((id) => {
              const index = currentCheckedKeys.indexOf(id);
              if (index > -1) {
                currentCheckedKeys.splice(index, 1);
              }
            });
          }
        }

        // 去重
        const uniqueCheckedKeys = Array.from(new Set(currentCheckedKeys));

        // 更新选中状态和半选状态
        formData.resources = uniqueCheckedKeys;
        formData.halfCheckedKeys = calculateHalfCheckedKeys(uniqueCheckedKeys);
      };

      const showModal = () => {
        if (!hasPermission('operationPermissionsAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 重置表单数据
        isEdit.value = false;
        formData.gid = '';
        formData.name = '';
        formData.resources = [];
        isModalVisible.value = true;
      };

      // 提交表单
      const handleOk = async () => {
        try {
          await formRef.value.validate();

          // 编辑模式下，检查是否是超级管理员组且当前用户不是超级管理员
          if (
            isEdit.value &&
            formData.gid === '1' &&
            !userStore.role.includes('超级管理员')
          ) {
            Message.error('只有超级管理员可以修改超级管理员权限组');
            return;
          }

          // 获取选中的所有节点和半选节点
          const checkedKeys = [...formData.resources];
          const halfCheckedKeys = [...formData.halfCheckedKeys];

          // 合并所有需要提交的权限ID
          const allKeys = new Set([...checkedKeys, ...halfCheckedKeys]);

          // 使用包含所有权限ID的最终资源列表
          const processedResources = Array.from(allKeys);


          const params = new URLSearchParams({
            act: 'settings',
            set_type: isEdit.value ? 'edit' : 'add',
            name: formData.name,
            resources: processedResources.join(','),
          });

          if (isEdit.value) {
            params.append('gid', formData.gid);
          }

          const response = await axios.post('/lua/permission.lua', params, {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          });

          if (response.data.code === 200) {

            await fetchPermissionGroups(); // 重新获取数据
            Message.success(isEdit.value ? '编辑成功' : '添加成功');
            isModalVisible.value = false;

            // 如果当前登录用户的权限可能受到影响，则刷新菜单
            await refreshMenuIfNeeded();

            // 记录权限变更时间戳到localStorage，用于其他用户检测权限变更
            localStorage.setItem(
              'permission_last_updated',
              new Date().getTime().toString()
            );

            // 设置一个标志，表示权限已变更
            localStorage.setItem('permissions_changed', 'true');
          } else {
            Message.error(
              response.data?.result || (isEdit.value ? '编辑失败' : '添加失败')
            );
          }
        } catch (error) {
          console.error('Submit error:', error);
          Message.error('提交失败，请检查表单');
        }
      };

      const handleCancel = () => {
        isModalVisible.value = false;
      };

      const handleRefresh = () => {
        fetchPermissionGroups().then(() => {
          Message.success('刷新成功');
        });
      };

      const filterData = () => {
        if (searchQuery.value.trim() === '') {
          filteredData.value = [...data.value];
          Message.error('请填写权限组名称');
          return;
        }

        filteredData.value = data.value.filter((item) =>
          item.name.includes(searchQuery.value)
        );

        if (filteredData.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      onMounted(() => {        
        fetchMenus();
        fetchPermissionGroups();
      });

      return {
        userStore,
        columns,
        data,
        filteredData,
        currentPage,
        pageSize,
        menuTree,
        formatResources,
        deletePermission,
        editPermission,
        isModalVisible,
        isEdit,
        formData,
        rules,
        formRef,
        showModal,
        handleOk,
        handleCancel,
        isComponentVisible,
        handleRefresh,
        isRefreshing,
        searchQuery,
        filterData,
        // 删除确认相关
        isDeleteConfirmVisible,
        deleteRecord,
        showDeleteConfirm,
        confirmDelete,
        cancelDelete,
        handleMenuCheck,
        calculateHalfCheckedKeys,
        hasPermission,
        paginatedData,
        handlePageChange,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
  }
</style>
