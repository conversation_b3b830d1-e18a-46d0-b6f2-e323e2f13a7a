<!-- 5G网络接入 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.rate-settings']" />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane
        key="1"
        title="功能开关"
        v-if="hasPermission('bindMultiplePort')"
      >
        <Function :active="activeTabKey === '1'" />
      </a-tab-pane>
      <a-tab-pane
        key="2"
        title="启动接入"
        v-if="hasPermission('bindMultiplePort')"
      >
        <access :active="activeTabKey === '2'" />
      </a-tab-pane>
      <a-tab-pane
        key="3"
        title="基本配置"
        v-if="hasPermission('bindMultiplePort')"
      >
        <basic :active="activeTabKey === '3'" />
      </a-tab-pane>
      <a-tab-pane
        key="4"
        title="地址池"
        v-if="hasPermission('bindMultiplePort')"
      >
        <addrpool :active="activeTabKey === '4'" />
      </a-tab-pane>
      <a-tab-pane
        key="5"
        title="固定地址"
        v-if="hasPermission('bindMultiplePort')"
      >
        <fixedaddr :active="activeTabKey === '5'" />
      </a-tab-pane>
      <a-tab-pane
        key="6"
        title="IPV6地址池"
        v-if="hasPermission('bindMultiplePort')"
      >
        <IPv6 :active="activeTabKey === '6'" />
      </a-tab-pane>
      <a-tab-pane
        key="7"
        title="认证失败定向"
        v-if="hasPermission('bindMultiplePort')"
      >
        <targeting :active="activeTabKey === '7'" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import Function from './function.vue';
  import access from './activate-access.vue';
  import basic from './basic-settings.vue';
  import addrpool from './addr-pool.vue';
  import fixedaddr from './fixed-addr.vue';
  import IPv6 from './IPV6-addr.vue';
  import targeting from './Authentication-failed-targeting.vue';

  export default defineComponent({
    components: {
      Function,
      access,
      basic,
      addrpool,
      fixedaddr,
      IPv6,
      targeting,
    },
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const activeTabKey = ref('1');

      const handleTabChange = (key: string) => {
        activeTabKey.value = key;
      };

      return {
        activeTabKey,
        handleTabChange,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
</style>
