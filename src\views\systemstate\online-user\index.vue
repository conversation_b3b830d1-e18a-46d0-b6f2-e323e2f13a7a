<!-- 多出口地址 -->
<template>
  <div v-if="isComponentVisible" class="container">
    <div v-if="isRefreshing" class="overlay">
      <div class="loader"></div>
    </div>
    <Breadcrumb
      :items="['menu.system-configuration', 'menu.online-user']"
    />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange" position="left">
      <a-tab-pane key="1" title="初始化用户" v-if="hasPermission('initUser')">
        <template #title>初始化用户</template>
        <a-card class="general-card" title="初始化用户">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item field="ip" label="IP地址">
                      <a-input
                        v-model="searchQuery"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        @click="filterData"
                        style="margin-left: 10px"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">

            <!-- 右侧按钮 -->
            <a-col
              style="display: flex; align-items: center; justify-content: end"
            >
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>

          <!-- 列表 -->
          <a-table
            :columns="columns_init"
            :data="paginatedData_init"
            column-resizable
            :pagination="false"
            style="margin-top: 20px"
          >
            <template #ip="{ rowIndex }">
              <span>{{ filteredData_init[rowIndex].ip }}</span>
            </template>

            <template #vlan="{ rowIndex }">
              <span>{{ filteredData_init[rowIndex].vlan }}</span>
            </template>
          </a-table>
              
          <a-pagination
            v-model:current="currentPage_init"
            v-model:page-size="pageSize_init"
            :total="filteredData_init.length"
            show-total
            show-size-changer
            show-jumper
            show-page-size
            style="margin-top: 20px"
          />
          
        </a-card>
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane key="2" v-if="hasPermission('authUser')">
        <template #title>成功认证用户</template>
        <a-card class="general-card" title="成功认证用户">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery2 }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item field="number" label="IP地址">
                      <a-input
                        v-model="searchQuery2"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        @click="filterData2"
                        style="margin-left: 10px"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <!-- 右侧按钮 -->
            <a-col
              style="display: flex; align-items: center; justify-content: end"
            >
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh2">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>

          <a-table
            :columns="columns_auth"
            :data="paginatedData_auth"
            column-resizable
            :pagination="false"
            style="margin-top: 20px"
          >
            <template #ip="{ rowIndex }">
              <span>{{ filteredData_auth[rowIndex].ip }}</span>
            </template>
            <template #vlan="{ rowIndex }">
              <span>{{ filteredData_auth[rowIndex].vlan }}</span>
            </template>
            <template #name="{ rowIndex }">
              <span>{{ filteredData_auth[rowIndex].name }}</span>
            </template>
            <template #ipv6="{ rowIndex }">
              <span>{{ filteredData_auth[rowIndex].ipv6 }}</span>
            </template>
            <template #oe_name="{ rowIndex }">
              <span>{{ filteredData_auth[rowIndex].oe_name }}</span>
            </template>
          </a-table>
           
          <a-pagination
            v-model:current="currentPage_auth"
            v-model:page-size="pageSize_auth"
            :total="filteredData_auth.length"
            show-total
            show-size-changer
            show-jumper
            show-page-size
            style="margin-top: 20px"
          />
          
        </a-card>

      </a-tab-pane>
      <!-- 标签页3 -->
      <a-tab-pane key="3" v-if="hasPermission('passUser')">
        <template #title>直通用户</template>
        <a-card class="general-card" title="直通用户">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery3 }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item field="number" label="IP地址">
                      <a-input
                        v-model="searchQuery3"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        @click="filterData3"
                        style="margin-left: 10px"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <!-- 右侧按钮 -->
            <a-col
              style="display: flex; align-items: center; justify-content: end"
            >
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh3">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>

          <!-- 列表 -->
          <a-table
            :columns="columns_pass"
            :data="paginatedData_pass"
            :pagination="false"
            style="margin-top: 20px"
          >
            <template #name="{ rowIndex }">
              <span>{{ filteredData_pass[rowIndex].name }}</span>
            </template>

            <template #ip="{ rowIndex }">
              <span>{{ filteredData_pass[rowIndex].ip }}</span>
            </template>

          </a-table> 
          
          <a-pagination
            v-model:current="currentPage_pass"
            v-model:page-size="pageSize_pass"
            :total="filteredData_pass.length"
            show-total
            show-size-changer
            show-jumper
            show-page-size
            style="margin-top: 20px"
          />
          
        </a-card>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed,
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import {
    handleNotification1,
    handleNotification2,
  } from '../../../utils/info';
  import usePermission from '@/hooks/permission';
  

  export default defineComponent({
    setup() {
      
      const { hasPermission } = usePermission();
       
      const columns_init = [
        {
          title: 'IP地址',
          dataIndex: 'ip',
        },
        {
          title: 'vlan',
          dataIndex: 'vlan',
        },
      ] as {
        title: string;
        dataIndex: string;
      }[];
      const columns_auth = [
        {
          title: 'IP地址',
          dataIndex: 'ip',
        },
        {
          title: 'vlan',
          dataIndex: 'vlan',
        },
        {
          title: '用户名',
          dataIndex: 'name',
        },
        {
          title: 'ipv6',
          dataIndex: 'ipv6',
        },
        {
          title: 'PPPOE用户名',
          dataIndex: 'oe_name',
        },
      ] as {
        title: string;
        dataIndex: string;
      }[];      
      const columns_pass = [
        {
          title: 'IP地址',
          dataIndex: 'ip',
        },
        {
          title: 'vlan',
          dataIndex: 'vlan',
        },
      ] as {
        title: string;
        dataIndex: string;
      }[];
 
      // 使用ref定义数据，方便后续更新
      const data_init = ref([
        {
          ip: '',
          vlan: '',
        },
      ]);

      const data_auth = ref([
        {
          ip: '',
          vlan: '',
          name: '',
          ipv6: '',
          oe_name: '',
        },
      ]);

      const data_pass = ref([
        {
          ip: '',
          vlan: '',
        },
      ]);
      
      const filteredData_init = ref([]);
      const filteredData_auth = ref([]);
      const filteredData_pass = ref([]);

      const currentPage_init = ref(1);
      const pageSize_init = ref(10);
      const currentPage_auth = ref(1);
      const pageSize_auth = ref(10);
      const currentPage_pass = ref(1);
      const pageSize_pass = ref(10);

      const paginatedData_init = computed(() => {
        const start = (currentPage_init.value - 1) * pageSize_init.value;
        return filteredData_init.value.slice(start, start + pageSize_init.value);
      });
      const paginatedData_auth = computed(() => {
        const start = (currentPage_auth.value - 1) * pageSize_auth.value;
        return filteredData_auth.value.slice(start, start + pageSize_auth.value);
      });
      const paginatedData_pass = computed(() => {
        const start = (currentPage_pass.value - 1) * pageSize_pass.value;
        return filteredData_pass.value.slice(start, start + pageSize_pass.value);
      });
      
      const isRefreshing = ref(false);

      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/system_info.lua',
            new URLSearchParams({ act: 'online_users' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          console.log(response)
          if (response.data.code === 200) {
            data_init.value =
              response.data.data.init_res.map((item: any) => ({
                ip: item.ip || '',
                vlan: item.vlan || '',
              })) || [];
            filteredData_init.value = [...data_init.value];
            
            data_auth.value =
              response.data.data.up_res.map((item: any) => ({
                ip: item.ip || '',
                vlan: item.vlan || '',
                name: item.name || '',
                ipv6: item.ipv6 || '',
                oe_name: item.oe_name || '',
              })) || [];
            filteredData_auth.value = [...data_auth.value];
            
            data_pass.value =
              response.data.data.pass_res.map((item: any) => ({
                ip: item.ip || '',
                vlan: item.vlan || '',
              })) || [];
            filteredData_pass.value = [...data_pass.value];
            
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      const activeTabKey = ref('1');

      // 检查标签页权限并找到第一个可用的标签页
      const findFirstAvailableTab = () => {
        const tabPermissions = [
          { key: '1', permission: 'initUser' },
          { key: '2', permission: 'authUser' },
          { key: '3', permission: 'passUser' },
        ];

        const availableTab = tabPermissions.find((tab) =>
          hasPermission(tab.permission)
        );

        if (availableTab) {
          return availableTab.key;
        }

        // 如果没有任何标签有权限，可能需要显示一个提示或者空白页面
        Message.error('您没有权限');
        return null;
      };

      // 修改刷新函数，为不同标签页提供专用刷新
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 添加第二个标签页的刷新函数
      const handleRefresh2 = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 添加第三个标签页的刷新函数
      const handleRefresh3 = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      const isComponentVisible = ref(true);

      const searchQuery = ref('');
      const searchQuery2 = ref('');
      const searchQuery3 = ref('');

      const filterData = () => {
        if (searchQuery.value.trim() === '') {
          filteredData_init.value = data_init.value; // 如果没有输入搜索条件，直接返回原始数据
          return;
        }
        filteredData_init.value = data_init.value.filter((item) =>
          item.ip.includes(searchQuery.value)
        );

      };

      const filterData2 = () => {
        console.log('搜索条件:', searchQuery2.value); // 调试信息
        if (searchQuery2.value.trim() === '') {
          filteredData_auth.value = data_auth.value; // 如果没有输入搜索条件，直接返回原始数据
          return;
        }

        filteredData_auth.value = data_auth.value.filter((item) =>
          item.ip.includes(searchQuery2.value)
        );
      };

      const filterData3 = () => {
        if (searchQuery3.value.trim() === '') {
          filteredData_pass.value = data_pass.value; // 如果没有输入搜索条件，直接返回原始数据
          return;
        }

        filteredData_pass.value = data_pass.value.filter((item) =>
          item.ip.includes(searchQuery3.value)
        );

      };

      // 添加标签页切换处理函数
      const handleTabChange = (key: string) => {
        // 检查用户是否有权限访问目标标签页
        let hasAccess = false;

        if (key === '1') {
          hasAccess = hasPermission('initUser');
        } else if (key === '2') {
          hasAccess = hasPermission('authUser');
        } else if (key === '3') {
          hasAccess = hasPermission('passUser');
        } 

        if (!hasAccess) {
          Message.error('您没有权限');

          // 尝试找到下一个有权限的标签页
          const newKey = findFirstAvailableTab();
          if (newKey && newKey !== activeTabKey.value) {
            activeTabKey.value = newKey;
          }
        } else {
          // 有权限访问，更新当前活动标签
          activeTabKey.value = key;

        }
      };
      // 组件加载时获取数据和初始化标签页
      onMounted(() => {
      

        // 查找第一个有权限的标签页
        const firstAvailableTab = findFirstAvailableTab();
        console.log(firstAvailableTab)
        if (firstAvailableTab) {
          activeTabKey.value = firstAvailableTab;
          fetchData();
        }
      });

      onBeforeUnmount(() => {});

      return {
        columns_init,
        columns_auth,
        columns_pass,
        data_init,
        data_auth,
        data_pass,
        handleNotification1,
        handleNotification2,
        handleTabChange,
        activeTabKey,
        handleRefresh,
        handleRefresh2,
        handleRefresh3,
        isComponentVisible,
        isRefreshing,
        searchQuery,
        searchQuery2,
        searchQuery3,
        filterData,
        filterData2,
        filterData3,
        filteredData_init,
        filteredData_auth,
        filteredData_pass,
        currentPage_init,
        pageSize_init,
        paginatedData_init,
        currentPage_auth,
        pageSize_auth,
        paginatedData_auth,
        currentPage_pass,
        pageSize_pass,
        paginatedData_pass,
        hasPermission
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .input-field {
    width: 135px;
    margin-left: 5px;
  }

  .hint {
    flex: 1;
    text-align: left;
    color: gray;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }

  .status-text {
    margin-left: 10px;
    font-size: 14px;
  }

  .session {
    text-align: left;
  }

  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .button {
    text-align: center;
  }

  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
</style>
