<!-- 特殊设置 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.rate-settings']" />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane
        key="1"
        title="专有客户端"
        v-if="hasPermission('bindMultiplePort')"
      >
        <proprietaryclient :active="activeTabKey === '1'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane
        key="2"
        title="主虚机互连"
        v-if="hasPermission('bindMultiplePort')"
      >
        <Mainvirtualmachineinterconnection :active="activeTabKey === '2'" />
      </a-tab-pane>
      <a-tab-pane
        key="3"
        title="pnp设置"
        v-if="hasPermission('bindMultiplePort')"
      >
        <pnpsettings :active="activeTabKey === '3'" />
      </a-tab-pane>
      <a-tab-pane
        key="4"
        title="二层转发"
        v-if="hasPermission('bindMultiplePort')"
      >
        <layer2forwarding :active="activeTabKey === '4'" />
      </a-tab-pane>
      <a-tab-pane
        key="5"
        title="vlan改变信息设置"
        v-if="hasPermission('bindMultiplePort')"
      >
        <VLANchangessettings :active="activeTabKey === '5'" />
      </a-tab-pane>
      <a-tab-pane
        key="6"
        title="热点放通"
        v-if="hasPermission('bindMultiplePort')"
      >
        <hotspotrelease :active="activeTabKey === '6'" />
      </a-tab-pane>
      <a-tab-pane
        key="7"
        title="固定mac"
        v-if="hasPermission('bindMultiplePort')"
      >
        <fixedmac :active="activeTabKey === '7'" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  import proprietaryclient from './proprietary-client.vue';
  import Mainvirtualmachineinterconnection from './Main-virtual-machine-interconnection.vue';
  import pnpsettings from './pnp-settings.vue';
  import layer2forwarding from './layer-2-forwarding.vue';
  import VLANchangessettings from './VLAN-changes-settings.vue';
  import hotspotrelease from './hot-spot-release.vue';
  import fixedmac from './fixed-mac.vue';

  export default defineComponent({
    components: {
      proprietaryclient,
      Mainvirtualmachineinterconnection,
      pnpsettings,
      layer2forwarding,
      VLANchangessettings,
      hotspotrelease,
      fixedmac,
    },
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const activeTabKey = ref('1');

      const handleTabChange = (key: string) => {
        activeTabKey.value = key;
      };

      return {
        activeTabKey,
        handleTabChange,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
</style>
