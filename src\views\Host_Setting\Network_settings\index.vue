<!-- 网络设置组件 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.Host Setting', 'menu.Network settings']" />

    <a-card title="网络设置">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-col :span="8">
          <a-card title="网络设置" :bordered="false" :style="{ width: '100%' }">
            <template #extra> </template>
            <div class="session">
              <div class="form-item">
                <span>端口：</span>
                <a-input
                  v-model="formData.port"
                  class="input-field"
                  :status="portError ? 'error' : undefined"
                />
                <div v-if="portError" class="error-message">{{
                  portError
                }}</div>
              </div>
              <div class="form-item">
                <span>IP地址：</span>
                <a-tooltip
                  content="格式为：***********"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.ip"
                    class="input-field"
                    :status="ipError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="ipError" class="error-message">{{ ipError }}</div>
              </div>
              <div class="form-item">
                <span>子网掩码：</span>
                <a-tooltip
                  content="格式为：*************"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.mask"
                    class="input-field"
                    :status="maskError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="maskError" class="error-message">{{
                  maskError
                }}</div>
              </div>
              <div class="form-item">
                <span>网关：</span>
                <a-tooltip
                  content="格式为：***********"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.gateway"
                    class="input-field"
                    :status="gatewayError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="gatewayError" class="error-message">{{
                  gatewayError
                }}</div>
              </div>
              <br />
              <div class="button">
                <!-- 使用div包裹按钮，确保权限指令只移除按钮而不影响页面结构 -->
                <div class="btn-wrapper">
                  <a-button
                    id="networkSettingSubmit"
                    type="primary"
                    @click="submitData"
                  >
                    <template #icon>
                      <icon-check />
                    </template>
                    <template #default>提交</template>
                  </a-button>
                </div>

                <div
                  class="btn-wrapper"
                  style="display: inline-block; margin-left: 10px"
                >
                  <a-button
                    id="networkSettingReset"
                    type="secondary"
                    @click="resetForm"
                  >
                    <template #icon>
                      <icon-refresh />
                    </template>
                    <template #default>重置</template>
                  </a-button>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import {
    handleNotification4,
    handleNotification3,
  } from '../../../utils/info';

  export default defineComponent({
    setup() {
      const formData = reactive({
        port: '',
        gateway: '',
        mask: '',
        ip: '',
        code: '',
      });

      // 添加错误状态
      const ipError = ref('');
      const maskError = ref('');
      const gatewayError = ref('');
      const portError = ref('');

      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // 验证IP地址格式
      const validateIpAddress = (ip: string): boolean => {
        const ipRegex =
          /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
      };

      // 验证子网掩码格式
      const validateMask = (mask: string): boolean => {
        // 首先验证是否符合IP地址格式
        if (!validateIpAddress(mask)) {
          return false;
        }

        // 检查是否是有效的子网掩码
        const parts = mask.split('.').map(Number);
        let binary = '';
        parts.forEach((part) => {
          binary += part.toString(2).padStart(8, '0');
        });

        // 有效的子网掩码应该是连续的1后面跟着连续的0
        return /^1*0*$/.test(binary);
      };

      // 验证端口
      const validatePort = (port: string): boolean => {
        if (!port) return false;

        const portNum = parseInt(port, 10);
        return !Number.isNaN(portNum) && portNum >= 1 && portNum <= 65535;
      };

      const fetchNetworkData = async () => {
        try {
          const response = await axios.post(
            '/lua/network.lua',
            new URLSearchParams({ act: 'get' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            formData.port = response.data.data.port;
            formData.gateway = response.data.data.gateway;
            formData.mask = response.data.data.mask;
            formData.ip = response.data.data.ip;
          } else {
            console.error('Failed to fetch data:', response.data);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      };

      onMounted(async () => {
        await fetchNetworkData();
      });

      // 校验所有字段
      const validateAllFields = (): boolean => {
        let isValid = true;

        // 验证端口
        if (!formData.port) {
          portError.value = '端口不能为空';
          isValid = false;
        } else if (!validatePort(formData.port)) {
          portError.value = '端口必须在1-65535之间';
          isValid = false;
        } else {
          portError.value = '';
        }

        // 验证IP地址
        if (!formData.ip) {
          ipError.value = 'IP地址不能为空';
          isValid = false;
        } else if (!validateIpAddress(formData.ip)) {
          ipError.value = 'IP地址格式不正确，应为：***********';
          isValid = false;
        } else {
          ipError.value = '';
        }

        // 验证子网掩码
        if (!formData.mask) {
          maskError.value = '子网掩码不能为空';
          isValid = false;
        } else if (!validateMask(formData.mask)) {
          maskError.value = '子网掩码格式不正确，应为：*************';
          isValid = false;
        } else {
          maskError.value = '';
        }

        // 验证网关
        if (!formData.gateway) {
          gatewayError.value = '网关不能为空';
          isValid = false;
        } else if (!validateIpAddress(formData.gateway)) {
          gatewayError.value = '网关格式不正确，应为：***********';
          isValid = false;
        } else {
          gatewayError.value = '';
        }

        return isValid;
      };

      // 提交数据
      const submitData = async () => {
        // 在提交前验证权限
        if (!hasPermission('networkSettingSubmit')) {
          Message.error('您没有权限');
          return;
        }

        // 在提交前验证所有字段
        if (!validateAllFields()) {
          Message.error({
            content: '请修正表单中的错误再提交',
            duration: 5000,
          });
          return;
        }

        try {
          console.log('提交的数据:', {
            port: formData.port,
            ip: formData.ip,
            mask: formData.mask,
            gateway: formData.gateway,
          });

          const response = await axios.post(
            '/lua/network.lua',
            new URLSearchParams({
              act: 'set',
              port: formData.port,
              ip: formData.ip,
              mask: formData.mask,
              gateway: formData.gateway,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            Message.success(response.data.result);
          } else {
            Message.error({
              content: response.data.result,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Error setting data:', error);
          Message.error('提交数据失败');
        }
      };

      // 重置表单
      const resetForm = async () => {
        try {
          // 清除所有错误提示
          ipError.value = '';
          maskError.value = '';
          gatewayError.value = '';
          portError.value = '';

          await fetchNetworkData(); // 重新获取网络数据
          Message.success('表单已重置');
        } catch (error) {
          console.error('Error resetting form:', error);
          Message.error('重置表单失败');
        }
      };

      return {
        handleNotification4,
        handleNotification3,
        submitData,
        resetForm,
        formData,
        ipError,
        maskError,
        gatewayError,
        portError,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .hint {
    flex: 1; /* 占据剩余空间 */
    text-align: left;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }

  /* 为上传按钮添加居中样式 */
  .upload {
    display: block;
    margin: 0 auto;
  }

  /* 为按钮添加居中样式 */
  .button {
    text-align: center;
  }

  /* 按钮包装器，确保权限指令只移除按钮而不影响页面结构 */
  .btn-wrapper {
    display: inline-block;
  }

  .arco-input-wrapper {
    margin: 10px 0 !important;
  }

  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>
