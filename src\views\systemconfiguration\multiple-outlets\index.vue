<!-- 多出口地址 -->
<template>
  <div v-if="isComponentVisible" class="container">
    <div v-if="isRefreshing" class="overlay">
      <div class="loader"></div>
    </div>
    <Breadcrumb
      :items="['menu.system-configuration', 'menu.multiple-outlets']"
    />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane key="1" title="固定地址出口" v-if="hasPermission('fixExit')">
        <template #title>固定地址出口</template>
        <a-card class="general-card" :title="$t('menu.multiple-outlets')">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item field="number" :label="$t('组ID')">
                      <a-input
                        v-model="searchQuery"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        @click="filterData"
                        style="margin-left: 10px"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <a-col :span="12">
              <a-space>
                <!-- 新建按钮 -->
                <a-button
                  id="fixExitAdd"
                  type="primary"
                  :disabled="!hasPermission('fixExitAdd')"
                  @click="showModal"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  {{ $t('searchTable.operation.create') }}
                </a-button>
              </a-space>
            </a-col>
            <!-- 右侧按钮 -->
            <a-col
              :span="12"
              style="display: flex; align-items: center; justify-content: end"
            >
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>

          <!-- 列表 -->
          <a-table
            :columns="columns"
            :data="paginatedData"
            column-resizable
            :pagination="false"
            style="margin-top: 20px"
          >
            <template #name="{ rowIndex }">
              <span>{{ filteredData[rowIndex].name }}</span>
            </template>

            <template #GroupID="{ rowIndex }">
              <span>{{ filteredData[rowIndex].GroupID }}</span>
            </template>

            <template #IPaddress="{ rowIndex }">
              <span>{{ filteredData[rowIndex].IPaddress }}</span>
            </template>

            <template #mask="{ rowIndex }">
              <span>{{ filteredData[rowIndex].mask }}</span>
            </template>
            <template #gateway="{ rowIndex }">
              <span>{{ filteredData[rowIndex].gateway }}</span>
            </template>

            <template #port="{ rowIndex }">
              <span>{{ filteredData[rowIndex].port }}</span>
            </template>

            <template #VLAN="{ rowIndex }">
              <span>{{ filteredData[rowIndex].VLAN }}</span>
            </template>

            <template #down_bw="{ rowIndex }">
              <span>{{ filteredData[rowIndex].down_bw }}</span>
            </template>

            <template #up_bw="{ rowIndex }">
              <span>{{ filteredData[rowIndex].up_bw }}</span>
            </template>
            <!-- 删除键位 -->
            <template #option="{ rowIndex }">
              <a-button
                id="fixExitDelete"
                :disabled="!hasPermission('fixExitDelete')"
                type="primary"
                @click="deleteRow(rowIndex)"
              >
                <template #icon>
                  <IconEye />
                </template>
                <template #default>删除</template>
              </a-button>
            </template>
          </a-table>
              
          <a-pagination
            v-model:current="currentPage"
            v-model:page-size="pageSize"
            :total="filteredData.length"
            show-total
            show-size-changer
            show-jumper
            show-page-size
            style="margin-top: 20px"
          />
          
        </a-card>
        <a-modal
          v-model:visible="isModalVisible"
          title="新建"
          draggable
          @ok="handleOk"
          @cancel="handleCancel"
        >
          <a-form :model="formData" :rules="rules" ref="formRef">
            <a-form-item label="出口名字" field="name">
              <a-input v-model="formData.name" />
            </a-form-item>
            <a-form-item label="组ID" field="GroupID">
              <a-select v-model="formData.GroupID" :options="optionsGroup" />
            </a-form-item>
            <a-form-item label="IP地址" field="IPaddress">
              <a-input v-model="formData.IPaddress" />
            </a-form-item>
            <a-form-item label="子网掩码" field="mask">
              <a-input v-model="formData.mask" />
            </a-form-item>
            <a-form-item label="网关" field="gateway">
              <a-input v-model="formData.gateway" />
            </a-form-item>
            <a-form-item label="端口" field="port">
              <a-select v-model="formData.port" :options="portOptions" />
            </a-form-item>
            <a-form-item label="VLAN" field="VLAN">
              <a-input v-model="formData.VLAN" />
            </a-form-item>
            <a-form-item label="下行带宽" field="down_bw">
              <a-input v-model="formData.down_bw" />
            </a-form-item>
            <a-form-item label="上行带宽" field="up_bw">
              <a-input v-model="formData.up_bw" />
            </a-form-item>
          </a-form>
        </a-modal>
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane key="2" v-if="hasPermission('samePortMultiAddr')">
        <template #title>同端口多地址</template>
        <a-card class="general-card" :title="$t('同端口多地址')">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery2 }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item field="number" :label="$t('组ID')">
                      <a-input
                        v-model="searchQuery2"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        @click="filterData2"
                        style="margin-left: 10px"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <a-col :span="12">
              <a-space>
                <!-- 新建按钮 -->
                <a-button
                  type="primary"
                  id="samePortMultiAddrAdd"
                  :disabled="!hasPermission('samePortMultiAddrAdd')"
                  @click="showModal2"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  {{ $t('searchTable.operation.create') }}
                </a-button>
              </a-space>
            </a-col>
            <!-- 右侧按钮 -->
            <a-col
              :span="12"
              style="display: flex; align-items: center; justify-content: end"
            >
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh2">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>

          <a-table
            :columns="columns1"
            :data="filteredData2"
            column-resizable
            :pagination="false"
            style="margin-top: 20px"
          >
            <template #exportName="{ rowIndex }">
              <span>{{ filteredData2[rowIndex].exportName }}</span>
            </template>
            <template #GroupID="{ rowIndex }">
              <span>{{ filteredData2[rowIndex].GroupID }}</span>
            </template>
            <template #ip="{ rowIndex }">
              <span>{{ filteredData2[rowIndex].ip }}</span>
            </template>
            <template #option="{ rowIndex }">
              <a-button
                id="samePortMultiAddrDelete"
                type="primary"
                :disabled="!hasPermission('samePortMultiAddrDelete')"
                @click="deleteRow2(rowIndex)"
              >
                <template #icon>
                  <IconEye />
                </template>
                <template #default>删除</template>
              </a-button>
            </template>
          </a-table>
        </a-card>

        <a-modal
          v-model:visible="isModalVisible2"
          title="新建"
          width="60%"
          draggable
          @ok="handleOk2"
          @cancel="handleCancel2"
        >
          <a-form :model="formData2" :rules="rules" ref="formRef2">
            <a-form-item label="出口名" field="exportName">
              <a-select
                v-model="formData2.exportName"
                :options="exitNameOptions"
                @change="handleExitNameChange"
              />
            </a-form-item>
            <a-form-item v-if="false" label="组ID" field="GroupID">
              <a-select
                v-model="formData2.GroupID"
                :options="[{ label: selectedGroupID, value: selectedGroupID }]"
                disabled
              />
            </a-form-item>
            <a-form-item label="固定出口端口地址" field="ip">
              <a-input v-model="formData2.ip" />
            </a-form-item>
          </a-form>
        </a-modal>
      </a-tab-pane>
      <!-- 标签页3 -->
      <a-tab-pane key="3" v-if="hasPermission('dialAuthExit')">
        <template #title>拨号认证出口</template>
        <a-card class="general-card" :title="$t('拨号认证出口')">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="{ searchQuery3 }"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left"
              >
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item field="number" :label="$t('组ID')">
                      <a-input
                        v-model="searchQuery3"
                        :placeholder="$t('searchTable.form.number.placeholder')"
                      />
                      <a-button
                        type="primary"
                        @click="filterData3"
                        style="margin-left: 10px"
                      >
                        <template #icon>
                          <icon-search />
                        </template>
                        {{ $t('searchTable.form.search') }}
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right"> </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <a-col :span="12">
              <a-space>
                <!-- 新建按钮 -->
                <a-button
                  id="dialAuthExitAdd"
                  type="primary"
                  :disabled="!hasPermission('dialAuthExitAdd')"
                  @click="showModal3"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  {{ $t('searchTable.operation.create') }}
                </a-button>
              </a-space>
            </a-col>
            <!-- 右侧按钮 -->
            <a-col
              :span="12"
              style="display: flex; align-items: center; justify-content: end"
            >
              <a-tooltip :content="$t('searchTable.actions.refresh')">
                <div class="action-icon" @click="handleRefresh3">
                  <icon-refresh size="18" />
                </div>
              </a-tooltip>
            </a-col>
          </a-row>

          <!-- 列表 -->
          <a-table
            :columns="columns2"
            :data="filteredData3"
            :pagination="false"
            style="margin-top: 20px"
          >
            <template #name="{ rowIndex }">
              <span>{{ filteredData3[rowIndex].name }}</span>
            </template>

            <template #GroupID="{ rowIndex }">
              <span>{{ filteredData3[rowIndex].GroupID }}</span>
            </template>

            <template #account="{ rowIndex }">
              <span>{{ filteredData3[rowIndex].account }}</span>
            </template>

            <template #password="{ rowIndex }">
              <span>{{ filteredData3[rowIndex].password }}</span>
            </template>

            <template #port="{ rowIndex }">
              <span>{{ filteredData3[rowIndex].port }}</span>
            </template>

            <template #OUTVLAN="{ rowIndex }">
              <span>{{ filteredData3[rowIndex].OUTVLAN }}</span>
            </template>
            <template #INVLAN="{ rowIndex }">
              <span>{{ filteredData3[rowIndex].INVLAN }}</span>
            </template>

            <template #down_bw="{ rowIndex }">
              <span>{{ filteredData3[rowIndex].down_bw }}</span>
            </template>

            <template #up_bw="{ rowIndex }">
              <span>{{ filteredData3[rowIndex].up_bw }}</span>
            </template>

            <template #option="{ rowIndex }">
              <a-button
                type="primary"
                id="dialAuthExitDelete"
                :disabled="!hasPermission('dialAuthExitDelete')"
                @click="deleteRow3(rowIndex)"
              >
                <template #icon>
                  <IconEye />
                </template>
                <template #default>删除</template>
              </a-button>
            </template>
          </a-table>
        </a-card>
        <a-modal
          v-model:visible="isModalVisible3"
          title="新建"
          draggable
          @ok="handleOk3"
          @cancel="handleCancel3"
        >
          <a-col :span="24">
            <a-alert type="info" banner closable>
              <template #icon> </template>
              <template #title
                >一个端口只能配置1个上行地址。不能同接入端口冲突。</template
              >
            </a-alert>
          </a-col>
          <br />
          <a-form :model="formData3" :rules="rules" ref="formRef3">
            <a-form-item label="出口名字" field="name">
              <a-input v-model="formData3.name" />
            </a-form-item>
            <a-form-item label="组ID" field="GroupID">
              <a-select v-model="formData3.GroupID" :options="optionsGroup" />
            </a-form-item>
            <a-form-item label="账号名" field="account">
              <a-input v-model="formData3.account" />
            </a-form-item>
            <a-form-item label="密码" field="password">
              <a-input v-model="formData3.password" />
            </a-form-item>
            <a-form-item label="端口" field="port">
              <a-select v-model="formData3.port" :options="portOptions" />
            </a-form-item>
            <a-form-item label="外层VLAN" field="OUTVLAN">
              <a-input v-model="formData3.OUTVLAN" />
            </a-form-item>
            <a-form-item label="内层VLAN" field="INVLAN">
              <a-input v-model="formData3.INVLAN" />
            </a-form-item>
            <a-form-item label="下行带宽" field="down_bw">
              <a-input v-model="formData3.down_bw" />
            </a-form-item>
            <a-form-item label="上行带宽" field="up_bw">
              <a-input v-model="formData3.up_bw" />
            </a-form-item>
          </a-form>
        </a-modal>
      </a-tab-pane>
      <!-- 标签页4 -->
      <a-tab-pane key="4" v-if="hasPermission('optionSelect')">
        <template #title>功能选择</template>
        <a-card class="general-card" title="功能选择">
          <div
            :style="{
              boxSizing: 'border-box',
              width: '100%',
              padding: '20px',
              backgroundColor: 'var(--color-fill-2)',
            }"
          >
            <a-row :gutter="20" :style="{ marginBottom: '20px' }">
              <a-col :span="8">
                <a-card
                  title="配置arp"
                  :bordered="false"
                  :style="{ width: '100%', height: '270px' }"
                >
                  <a-alert type="info" banner closable>
                    <template #icon> </template>
                    <template #title>
                      <span style="font-size: 15px"
                        >注意：配置后需要重新启动虚机才能生效</span
                      >
                    </template>
                    <template #extra> </template>
                  </a-alert>
                  <br />
                  <div class="session">
                    <div class="form-item">
                      <span>arp检测网关开关：</span>
                      <a-tooltip
                        content="每秒查询一次"
                        position="tr"
                        background-color="#3491FA"
                      >
                        <a-switch
                          v-model="arpSwitchValue"
                          @change="handleArpSwitchChange"
                        />
                        <span class="status-text">{{
                          arpSwitchValue ? '开启' : '关闭'
                        }}</span>
                      </a-tooltip>
                    </div>
                    <br />
                    <div class="form-item">
                      <span>arp检测超时(秒)： </span>
                      <a-input
                        v-model="arpConfig.timeout"
                        placeholder="0"
                        class="input-field"
                      />
                      <span class="hint"></span>
                    </div>
                    <br />
                    <div class="button">
                      <a-button
                        id="functionSwitchSubmit"
                        :disabled="!hasPermission('functionSwitchSubmit')"
                        type="primary"
                        @click="saveArpConfig"
                      >
                        <template #icon>
                          <icon-check />
                        </template>
                        <template #default>提交</template>
                      </a-button>

                      <a-button
                        type="secondary"
                        style="margin-left: 10px"
                        @click="resetArpConfig"
                      >
                        <template #icon>
                          <icon-refresh />
                        </template>
                        <template #default>重置</template>
                      </a-button>
                    </div>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card
                  title="配置会话出口"
                  :bordered="false"
                  :style="{ width: '100%', height: '270px' }"
                >
                  <a-alert type="info" banner closable>
                    <template #icon> </template>
                    <template #title>
                      <span style="font-size: 15px">可实时配置</span>
                    </template>
                    <template #extra> </template>
                  </a-alert>
                  <br />
                  <div class="session">
                    <div class="form-item">
                      <span>会话选择出口开关：</span>
                      <a-tooltip
                        content="用户会话选择出口方法，根据出口剩余流量大小，选择出口"
                        position="tr"
                        background-color="#3491FA"
                      >
                        <a-switch
                          v-model="sessionSwitchValue"
                          @change="handleSessionSwitchChange"
                        />
                        <span class="status-text">{{
                          sessionSwitchValue ? '开启' : '关闭'
                        }}</span>
                      </a-tooltip>
                    </div>
                    <br />
                    <div class="form-item">
                      <span>会话选择出口模式：</span>
                      <a-tooltip
                        content="配置不同组出口时，需要上传组地址配置文件"
                        position="tr"
                        background-color="#3491FA"
                      >
                        <a-select
                          v-model="sessionConfig.mode"
                          :options="['固定出口', '不同组出口']"
                          :style="{ width: '135px' }"
                          placeholder="固定出口"
                      /></a-tooltip>
                    </div>
                    <br />

                    <div class="button">
                      <a-button
                        id="accessPortSubmit"
                        :disabled="!hasPermission('accessPortSubmit')"
                        type="primary"
                        @click="saveSessionConfig"
                      >
                        <template #icon>
                          <icon-check />
                        </template>
                        <template #default>提交</template>
                      </a-button>

                      <a-button
                        type="secondary"
                        style="margin-left: 10px"
                        @click="resetSessionConfig"
                      >
                        <template #icon>
                          <icon-refresh />
                        </template>
                        <template #default>重置</template>
                      </a-button>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed,
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import {
    handleNotification1,
    handleNotification2,
  } from '../../../utils/info';

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      
      const currentPage = ref(1);
      const pageSize = ref(10);

      const paginatedData = computed(() => {
        const start = (currentPage.value - 1) * pageSize.value;
        return filteredData.value.slice(start, start + pageSize.value);
      });


      // 定义options的类型为OptionValue，明确其结构
      const options = {
        0: [],
        1: [],
        2: [],
        3: [],
        // 3: ['Guangzhou', 'Shenzhen', 'Shantou'],
      };
      // 验证IP地址格式
      const validateIpAddress = (ip: string): boolean => {
        const ipRegex =
          /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
      };

      // 验证子网掩码
      const validateSubnetMask = (mask: string): boolean => {
        // 检查是否符合IP地址格式
        if (!validateIpAddress(mask)) {
          return false;
        }

        // 检查是否是有效的子网掩码
        const parts = mask.split('.');
        const binary = parts
          .map((part) => parseInt(part, 10).toString(2).padStart(8, '0'))
          .join('');

        // 有效的子网掩码应该是连续的1后跟连续的0
        return /^1*0*$/.test(binary);
      };
      const options2 = {
        1: [],
        2: [],
        3: [],
        4: [],
        // 3: ['Guangzhou', 'Shenzhen', 'Shantou'],
      };
      const optionsGroup = ref([
        { label: '0', value: '0' },
        { label: '1', value: '1' },
        { label: '2', value: '2' },
        { label: '3', value: '3' },
      ]);
      // 添加端口选项数据
      const portOptions = ref([]);
      // 添加出口名字选项和组ID选项
      const exitNameOptions = ref([]);
      const exitGroupOptions = ref([]);
      // 存储选中的出口名字对应的组ID
      const selectedGroupID = ref('');

      // 定义columns数组中元素的类型
      const columns = [
        {
          title: '出口名字',
          dataIndex: 'name',
          slotName: 'name',
        },
        {
          title: '组ID',
          dataIndex: 'GroupID',
          slotName: 'GroupID',
        },
        {
          title: 'IP地址',
          dataIndex: 'IPaddress',
          slotName: 'IPaddress',
        },
        {
          title: '子网掩码',
          dataIndex: 'mask',
          slotName: 'mask',
        },
        {
          title: '网关',
          dataIndex: 'gateway',
          slotName: 'gateway',
        },
        {
          title: '端口',
          dataIndex: 'port',
          slotName: 'port',
        },
        {
          title: 'VLAN',
          dataIndex: 'VLAN',
          slotName: 'VLAN',
        },
        {
          title: '下行带宽',
          dataIndex: 'down_bw',
          slotName: 'down_bw',
        },
        {
          title: '上行带宽',
          dataIndex: 'up_bw',
          slotName: 'up_bw',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];
      const columns1 = [
        {
          title: '出口名字',
          dataIndex: 'exportName',
        },
        {
          title: '组ID',
          dataIndex: 'GroupID',
        },
        {
          title: '固定出口端口地址',
          dataIndex: 'ip',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];
      const columns2 = [
        {
          title: '出口名字',
          dataIndex: 'name',
          slotName: 'name',
        },
        {
          title: '组ID',
          dataIndex: 'GroupID',
          slotName: 'GroupID',
        },
        {
          title: '账号名',
          dataIndex: 'account',
          slotName: 'account',
        },
        {
          title: '密码',
          dataIndex: 'password',
          slotName: 'password',
        },

        {
          title: '端口',
          dataIndex: 'port',
          slotName: 'port',
        },
        {
          title: '外层VLAN',
          dataIndex: 'OUTVLAN',
          slotName: 'OUTVLAN',
        },
        {
          title: '内层VLAN',
          dataIndex: 'INVLAN',
          slotName: 'INVLAN',
        },
        {
          title: '下行带宽',
          dataIndex: 'down_bw',
          slotName: 'down_bw',
        },
        {
          title: '上行带宽',
          dataIndex: 'up_bw',
          slotName: 'up_bw',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];

      // 使用ref定义数据，方便后续更新
      const data = ref([
        {
          name: '',
          GroupID: '',
          IPaddress: '',
          mask: '',
          gateway: '',
          port: '',
          VLAN: '',
          down_bw: '',
          up_bw: '',
          ip: '',
        },
      ]);

      const data2 = ref([
        {
          exportName: '',
          GroupID: '',
          ip: '',
        },
      ]);

      const data3 = ref([
        {
          name: '',
          GroupID: '',
          account: '',
          password: '',
          port: '',
          OUTVLAN: '',
          INVLAN: '',
          down_bw: '',
          up_bw: '',
        },
      ]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);
      const isModalVisible2 = ref(false);
      const isModalVisible3 = ref(false);

      const formData = reactive({
        name: '',
        GroupID: '',
        IPaddress: '',
        mask: '',
        gateway: '',
        port: '',
        VLAN: '',
        down_bw: '',
        up_bw: '',
        ip: '',
      });

      const formData2 = reactive({
        exportName: '',
        GroupID: '',
        ip: '',
      });

      const formData3 = reactive({
        name: '',
        GroupID: '',
        account: '',
        password: '',
        port: '',
        OUTVLAN: '',
        INVLAN: '',
        down_bw: '',
        up_bw: '',
      });

      const rules = {
        exportName: [{ required: true, message: '出口名字不能为空' }],
        name: [{ required: true, message: '出口名字不能为空' }],
        GroupID: [{ required: true, message: '组ID不能为空' }],
        IPaddress: [
          { required: true, message: 'IP地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateIpAddress(value)) {
                callback('IP地址格式不正确，应为：***********');
              } else {
                callback();
              }
            },
          },
        ],
        mask: [
          { required: true, message: '子网掩码不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateSubnetMask(value)) {
                callback('子网掩码格式不正确，应为：*************');
              } else {
                callback();
              }
            },
          },
        ],
        gateway: [
          { required: true, message: '网关不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateIpAddress(value)) {
                callback('IP地址格式不正确，应为：***********');
              } else {
                callback();
              }
            },
          },
        ],
        port: [{ required: true, message: '端口不能为空' }],
        VLAN: [{ required: true, message: 'VLAN不能为空' }],
        down_bw: [{ required: true, message: '下行带宽不能为空' }],
        up_bw: [{ required: true, message: '上行带宽不能为空' }],
        account: [{ required: true, message: '账号名不能为空' }],
        password: [{ required: true, message: '密码不能为空' }],
        OUTVLAN: [{ required: true, message: '外层VLAN不能为空' }],
        INVLAN: [{ required: true, message: '内层VLAN不能为空' }],
        ip: [
          { required: true, message: '固定出口端口地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateIpAddress(value)) {
                callback('IP地址格式不正确，应为：***********');
              } else {
                callback();
              }
            },
          },
        ],
      };

      const formRef = ref();
      const formRef2 = ref();
      const formRef3 = ref();

      const showModal = () => {
        if (!hasPermission('fixExitAdd')) {
          Message.error('您没有权限');
          return;
        }
        isModalVisible.value = true;
      };
      const showModal2 = () => {
        if (!hasPermission('samePortMultiAddrAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 显示弹窗前重置表单数据
        formData2.exportName = '';
        formData2.GroupID = '';
        formData2.ip = '';
        selectedGroupID.value = '';
        isModalVisible2.value = true;
      };
      const showModal3 = () => {
        if (!hasPermission('dialAuthExitAdd')) {
          Message.error('您没有权限');
          return;
        }
        isModalVisible3.value = true;
      };
      // const filteredData = ref([...data.value]);
      // const filteredData2 = ref([...data2.value]);
      // const filteredData3 = ref([...data3.value]);
      const filteredData = ref([]);
      const filteredData2 = ref([]);
      const filteredData3 = ref([]);
      // 更新出口名字和组ID选项
      const updateExitOptions = () => {
        // 提取唯一的出口名字选项
        const nameSet = new Set(
          data.value.map((item) => item.name).filter((name) => name)
        );
        exitNameOptions.value = Array.from(nameSet).map((name) => ({
          label: name,
          value: name,
        }));

        console.log('更新出口名字选项:', exitNameOptions.value);
      };

      // 获取第一个标签页数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'multi_output' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 转换接口返回的数据字段名称为组件使用的字段名称
            data.value =
              response.data.data.map((item: any) => ({
                name: item.out_name || '',
                GroupID: item.group_id || '',
                IPaddress: item.ip || '',
                mask: item.mask || '',
                gateway: item.gateway || '',
                port: item.port || '',
                VLAN: item.vlan || '',
                down_bw: item.down_bw || '',
                up_bw: item.up_bw || '',
              })) || [];
            filteredData.value = [...data.value];
            console.log('固定地址出口数据更新:', data.value);

            // 更新出口名字和组ID的选项
            updateExitOptions();
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取固定地址出口数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 获取第二个标签页数据
      const fetchData2 = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'multi_output_sec' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 转换接口返回的数据字段名称为组件使用的字段名称
            data2.value =
              response.data.data.map((item: any) => ({
                exportName: item.out_name || '',
                GroupID: item.group_id || '',
                ip: item.ip || '',
              })) || [];
            filteredData2.value = [...data2.value];
            console.log('同端口多地址拨号认证出口数据更新:', data2.value);
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取同端口多地址拨号认证出口数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 获取第三个标签页数据
      const fetchData3 = async () => {
        try {
          const params = new URLSearchParams();
          params.append('act', 'multi_output_oeclient');

          const response = await axios.post('/lua/get_cfg.lua', params, {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          });

          if (response.data.code === 200) {
            console.log('接收到拨号认证出口原始数据:', response.data);
            // 转换接口返回的数据字段名称为组件使用的字段名称
            data3.value =
              response.data.data.map((item: any) => ({
                name: item.out_name || '',
                GroupID: item.group_id || '',
                account: item.dial_name || '',
                password: item.pwd || '',
                port: item.port || '',
                OUTVLAN: item.pvlan || '',
                INVLAN: item.cvlan || '',
                down_bw: item.down_bw || '',
                up_bw: item.up_bw || '',
              })) || [];
            filteredData3.value = [...data3.value];
            console.log('拨号认证出口数据更新:', data3.value);
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取拨号认证出口数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 第一个标签页的添加处理
      const handleOk = async (done) => {
        if (!hasPermission('fixExitAdd')) {
          Message.error('您没有权限');
          return;
        }
        formRef.value.validate((errors) => {
          if (errors) {
            // 表单验证失败
            Message.error('表单验证失败，请检查输入');
            done(false); // 阻止模态框关闭
            return;
          }

          axios
            .post(
              '/lua/set_cfg.lua',
              new URLSearchParams({
                act: 'output',
                type_act: 'multi_output_add',
                ip: formData.IPaddress,
                out_name: formData.name,
                group_id: formData.GroupID,
                mask: formData.mask,
                gateway: formData.gateway,
                port: formData.port,
                vlan: formData.VLAN,
                down_bw: formData.down_bw,
                up_bw: formData.up_bw,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )
            .then((response) => {
              if (response.data.code === 200) {
                fetchData();
                Message.success(response.data.data.result);
                isModalVisible.value = false;
                // 清空表单数据
                formData.name = '';
                formData.GroupID = '0';
                formData.IPaddress = '';
                formData.mask = '';
                formData.gateway = '';
                formData.port = '1';
                formData.VLAN = '';
                formData.down_bw = '';
                formData.up_bw = '';
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });
        return false;
      };

      // 第二个标签页的添加处理
      const handleOk2 = async (done) => {
        if (!hasPermission('samePortMultiAddrAdd')) {
          Message.error('您没有权限');
          return;
        }
        formRef2.value.validate((errors) => {
          if (errors) {
            // 表单验证失败
            Message.error('表单验证失败，请检查输入');
            done(false); // 阻止模态框关闭
            return;
          }

          axios
            .post(
              '/lua/set_cfg.lua',
              new URLSearchParams({
                act: 'output',
                type_act: 'multi_output_sec_add',
                out_name: formData2.exportName,
                group_id: formData2.GroupID,
                ip: formData2.ip,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )

            .then((response) => {
              if (response.data.code === 200) {
                fetchData2();
                Message.success(response.data.data.result);
                isModalVisible2.value = false;
                // 清空表单数据
                formData2.exportName = '';
                formData2.GroupID = '';
                formData2.ip = '';
                selectedGroupID.value = '';
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });
        return false;
      };

      // 第三个标签页的添加处理
      const handleOk3 = async (done) => {
        if (!hasPermission('dialAuthExitAdd')) {
          Message.error('您没有权限');
          return;
        }
        formRef3.value.validate((errors) => {
          if (errors) {
            // 表单验证失败
            Message.error('表单验证失败，请检查输入');
            done(false); // 阻止模态框关闭
            return;
          }
          axios
            .post(
              '/lua/set_cfg.lua',
              new URLSearchParams({
                act: 'output',
                type_act: 'multi_output_oeclient_add',
                out_name: formData3.name,
                group_id: formData3.GroupID,
                dial_name: formData3.account,
                pwd: formData3.password,
                port: formData3.port,
                pvlan: formData3.OUTVLAN,
                cvlan: formData3.INVLAN,
                down_bw: formData3.down_bw,
                up_bw: formData3.up_bw,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )

            .then((response) => {
              if (response.data.code === 200) {
                fetchData3();
                Message.success(response.data.data.result);
                isModalVisible3.value = false;
                // 清空表单数据
                formData3.name = '';
                formData3.GroupID = '0';
                formData3.account = '';
                formData3.password = '';
                formData3.port = '1';
                formData3.OUTVLAN = '';
                formData3.INVLAN = '';
                formData3.down_bw = '';
                formData3.up_bw = '';
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });
        return false;
      };

      const handleCancel = () => {
        isModalVisible.value = false;
      };
      const handleCancel2 = () => {
        isModalVisible2.value = false;
      };
      const handleCancel3 = () => {
        isModalVisible3.value = false;
      };

      const activeTabKey = ref('1');

      // 检查标签页权限并找到第一个可用的标签页
      const findFirstAvailableTab = () => {
        const tabPermissions = [
          { key: '1', permission: 'fixExit' },
          { key: '2', permission: 'samePortMultiAddr' },
          { key: '3', permission: 'dialAuthExit' },
          { key: '4', permission: 'optionSelect' },
        ];

        const availableTab = tabPermissions.find((tab) =>
          hasPermission(tab.permission)
        );

        if (availableTab) {
          return availableTab.key;
        }

        // 如果没有任何标签有权限，可能需要显示一个提示或者空白页面
        Message.error('您没有权限');
        return null;
      };

      // 修改刷新函数，为不同标签页提供专用刷新
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 添加第二个标签页的刷新函数
      const handleRefresh2 = () => {
        isRefreshing.value = true;
        fetchData2().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 添加第三个标签页的刷新函数
      const handleRefresh3 = () => {
        isRefreshing.value = true;
        fetchData3().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 第一个标签页的删除处理
      const deleteRow = async (rowIndex: number) => {
        try {
          if (!hasPermission('fixExitDelete')) {
            Message.error('您没有权限');
            return;
          }
          const item = filteredData.value[rowIndex];

          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'output',
              type_act: 'multi_output_del',
              out_name: item.name,
              group_id: item.GroupID,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData();
            Message.success('删除成功');
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('删除固定地址出口失败:', error);
          Message.error('删除失败');
        }
      };

      // 第二个标签页的删除处理
      const deleteRow2 = async (rowIndex: number) => {
        try {
          if (!hasPermission('samePortMultiAddrDelete')) {
            Message.error('您没有权限');
            return;
          }
          const item = filteredData2.value[rowIndex];

          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'output',
              type_act: 'multi_output_sec_del',
              out_name: item.exportName,
              group_id: item.GroupID,
              ip: item.ip,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData2();
            Message.success('删除成功');
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('删除同端口多地址拨号认证出口失败:', error);
          Message.error('删除失败');
        }
      };

      // 第三个标签页的删除处理
      const deleteRow3 = async (rowIndex: number) => {
        try {
          if (!hasPermission('dialAuthExitDelete')) {
            Message.error('您没有权限');
            return;
          }
          const item = filteredData3.value[rowIndex];

          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'output',
              type_act: 'multi_output_oeclient_del',
              out_name: item.name,
              group_id: item.GroupID,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData3();
            Message.success('删除成功');
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('删除拨号认证出口失败:', error);
          Message.error('删除失败');
        }
      };

      const isComponentVisible = ref(true);

      const searchQuery = ref('');
      const searchQuery2 = ref('');
      const searchQuery3 = ref('');

      const filterData = () => {
        if (searchQuery.value.trim() === '') {
          filteredData.value = data.value; // 如果没有输入搜索条件，直接返回原始数据
          Message.error('请填写组ID');
          return;
        }
        filteredData.value = data.value.filter((item) =>
          item.GroupID.includes(searchQuery.value)
        );
        if (filteredData.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      const filterData2 = () => {
        console.log('搜索条件:', searchQuery2.value); // 调试信息
        if (searchQuery2.value.trim() === '') {
          filteredData2.value = data2.value; // 如果没有输入搜索条件，直接返回原始数据
          Message.error('请填写组ID');
          return;
        }

        filteredData2.value = data2.value.filter((item) =>
          item.GroupID.includes(searchQuery2.value)
        );
        console.log('过滤后的数据:', filteredData2.value); // 调试信息
        if (filteredData2.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      const filterData3 = () => {
        console.log('搜索条件:', searchQuery3.value); // 调试信息
        if (searchQuery3.value.trim() === '') {
          filteredData3.value = data3.value; // 如果没有输入搜索条件，直接返回原始数据
          Message.error('请填写组ID');
          return;
        }

        filteredData3.value = data3.value.filter((item) =>
          item.GroupID.includes(searchQuery3.value)
        );
        console.log('过滤后的数据:', filteredData3.value); // 调试信息
        if (filteredData3.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      // ARP配置
      const arpConfig = reactive({
        enabled: '关闭',
        timeout: '0',
      });

      // 会话配置
      const sessionConfig = reactive({
        enabled: '关闭',
        mode: '固定出口',
      });

      // 开关状态值
      const arpSwitchValue = ref(false);
      const sessionSwitchValue = ref(false);

      // 处理ARP开关变化
      const handleArpSwitchChange = (value: boolean) => {
        arpConfig.enabled = value ? '开启' : '关闭';
      };

      // 处理会话开关变化
      const handleSessionSwitchChange = (value: boolean) => {
        sessionConfig.enabled = value ? '开启' : '关闭';
      };

      // 保存ARP配置
      const saveArpConfig = async () => {
        try {
          if (!hasPermission('functionSwitchSubmit')) {
            Message.error('您没有权限');
            return;
          }
          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'multi_output_sw',
              type_act: 'arp',
              chk_active: arpConfig.enabled === '开启' ? 'enable' : 'disable',
              chk_active_timeout: arpConfig.timeout,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || 'ARP配置保存成功');
          } else {
            Message.error(response.data.err || 'ARP配置保存失败');
          }
        } catch (error) {
          console.error('保存ARP配置错误:', error);
          Message.error('保存ARP配置请求失败');
        }
      };

      // 重置ARP配置
      const resetArpConfig = () => {
        arpConfig.enabled = '关闭';
        arpConfig.timeout = '0';
        // 同步更新开关状态
        arpSwitchValue.value = false;
      };

      // 保存会话配置
      const saveSessionConfig = async () => {
        try {
          if (!hasPermission('sessionExitSubmit')) {
            Message.error('您没有权限');
            return;
          }
          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'multi_output_sw',
              type_act: 'choose',
              choose_by_rest_bw:
                sessionConfig.enabled === '开启' ? 'enable' : 'disable',
              choose_mode:
                sessionConfig.mode === '固定出口' ? 'fix-out' : 'access-group',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '会话配置保存成功');
          } else {
            Message.error(response.data.err || '会话配置保存失败');
          }
        } catch (error) {
          console.error('保存会话配置错误:', error);
          Message.error('保存会话配置请求失败');
        }
      };

      // 重置会话配置
      const resetSessionConfig = () => {
        sessionConfig.enabled = '关闭';
        sessionConfig.mode = '固定出口';
        // 同步更新开关状态
        sessionSwitchValue.value = false;
      };

      // 获取ARP配置
      const getArpConfig = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({
              act: 'multi_output_swt',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            console.log('获取ARP配置成功:', response.data);
            arpConfig.enabled =
              response.data.data.chk_active === 'enable' ? '开启' : '关闭';
            arpConfig.timeout = response.data.data.chk_active_timeout || '0';
            // 更新开关值
            arpSwitchValue.value = arpConfig.enabled === '开启';
          } else {
            console.error('获取ARP配置失败:', response.data.err);
            Message.error(response.data.err || '获取ARP配置失败');
          }
        } catch (error) {
          console.error('获取ARP配置错误:', error);
          Message.error('获取ARP配置请求失败');
        }
      };

      // 获取端口列表数据
      const fetchPortData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'get_out_port' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 将接口返回的数据转换为选择框需要的格式
            portOptions.value = response.data.data.port.map((port: number) => ({
              label: port.toString(),
              value: port.toString(),
            }));
            console.log('获取端口数据成功:', portOptions.value);
          } else {
            console.error('获取端口数据失败:', response.data.err);
            Message.error(response.data.err || '获取端口数据失败');
          }
        } catch (error) {
          console.error('获取端口数据请求失败:', error);
          Message.error('获取端口数据失败');
        }
      };
      // 处理出口名字变化
      const handleExitNameChange = (value: string) => {
        // 根据选择的出口名字找到对应的组ID
        const selectedExit = data.value.find((item) => item.name === value);
        if (selectedExit) {
          selectedGroupID.value = selectedExit.GroupID;
          formData2.GroupID = selectedGroupID.value;
          console.log('出口名字变化，对应组ID:', selectedGroupID.value);
        }
      };
      // 添加标签页切换处理函数
      const handleTabChange = (key: string) => {
        // 检查用户是否有权限访问目标标签页
        let hasAccess = false;

        if (key === '1') {
          hasAccess = hasPermission('fixExit');
        } else if (key === '2') {
          hasAccess = hasPermission('samePortMultiAddr');
        } else if (key === '3') {
          hasAccess = hasPermission('dialAuthExit');
        } else if (key === '4') {
          hasAccess = hasPermission('optionSelect'); // 第4个标签页不需要权限校验
        }

        if (!hasAccess) {
          Message.error('您没有权限');

          // 尝试找到下一个有权限的标签页
          const newKey = findFirstAvailableTab();
          if (newKey && newKey !== activeTabKey.value) {
            activeTabKey.value = newKey;
          }
        } else {
          // 有权限访问，更新当前活动标签
          activeTabKey.value = key;

          // 加载对应标签页的数据
          if (key === '1') {
            fetchData();
          } else if (key === '2') {
            // 确保先加载第一个标签页的数据，获取选项
            fetchData().then(() => {
              fetchData2();
            });
          } else if (key === '3') {
            fetchData3();
          } else if (key === '4') {
            getArpConfig();
          }
        }
      };
      // 组件加载时获取数据和初始化标签页
      onMounted(() => {
        // 获取端口数据
        fetchPortData();

        // 查找第一个有权限的标签页
        const firstAvailableTab = findFirstAvailableTab();
        console.log(firstAvailableTab)
        if (firstAvailableTab) {
          activeTabKey.value = firstAvailableTab;
          // 加载对应标签页的数据
          if (firstAvailableTab === '1') {
            fetchData();
          } else if (firstAvailableTab === '2') {
            // 确保先加载第一个标签页的数据，获取选项
            fetchData().then(() => {
              fetchData2();
            });
          } else if (firstAvailableTab === '3') {
            fetchData3();
          } else if (firstAvailableTab === '4') {
            getArpConfig();
          }
        }
      });

      onBeforeUnmount(() => {});

      return {
        optionsGroup,
        options,
        options2,
        portOptions,
        exitNameOptions,
        exitGroupOptions,
        selectedGroupID,
        handleExitNameChange,
        columns,
        columns1,
        columns2,
        data,
        data2,
        data3,
        handleNotification1,
        handleNotification2,
        deleteRow,
        deleteRow2,
        deleteRow3,
        isModalVisible,
        isModalVisible2,
        isModalVisible3,
        formData,
        formData2,
        formData3,
        rules,
        formRef,
        formRef2,
        formRef3,
        showModal,
        showModal2,
        showModal3,
        handleTabChange,
        activeTabKey,
        handleOk,
        handleOk2,
        handleOk3,
        handleRefresh,
        handleRefresh2,
        handleRefresh3,
        handleCancel,
        handleCancel2,
        handleCancel3,
        isComponentVisible,
        isRefreshing,
        searchQuery,
        searchQuery2,
        searchQuery3,
        filterData,
        filterData2,
        filterData3,
        filteredData,
        filteredData2,
        filteredData3,
        arpConfig,
        sessionConfig,
        arpSwitchValue,
        sessionSwitchValue,
        handleArpSwitchChange,
        handleSessionSwitchChange,
        saveArpConfig,
        resetArpConfig,
        saveSessionConfig,
        resetSessionConfig,
        getArpConfig,
        hasPermission,
        currentPage,
        pageSize,
        paginatedData
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .input-field {
    width: 135px;
    margin-left: 5px;
  }

  .hint {
    flex: 1;
    text-align: left;
    color: gray;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }

  .status-text {
    margin-left: 10px;
    font-size: 14px;
  }

  .session {
    text-align: left;
  }

  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .button {
    text-align: center;
  }

  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
</style>
