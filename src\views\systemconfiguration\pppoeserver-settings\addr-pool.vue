<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="24">
        <a-space>
          <!-- 新建按钮 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            添加
          </a-button>

          <a-button @click="handleDelete">
            <template #icon>
              <icon-delete />
            </template>
            删除
          </a-button>
          <!-- 刷新按钮 -->
          <a-button @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            激活
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <!-- 添加表格组件 -->
    <a-table
      :columns="columns"
      :data="renderData"
      :row-selection="rowSelection"
      row-key="_uniqueKey"
      :loading="isRefreshing"
      :pagination="{ showTotal: true }"
    />
  </a-card>

  <a-modal
    v-model:visible="isModalVisible"
    :width="700"
    title="添加地址池"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-form :model="formData" :rules="rules" ref="formRef">
      <a-form-item label="ID" field="ID">
        <a-select
          v-model="formData.id"
          style="width: 200px"
          placeholder="请选择ID"
        />
      </a-form-item>
      <a-form-item label="地址段x" field="addr_x">
        <a-select
          v-model="formData.addr_x"
          style="width: 200px"
          placeholder="请选择地址段x"
        />
      </a-form-item>
      <a-form-item label="IP地址" field="ip">
        <a-input
          v-model="formData.ip"
          style="width: 200px"
          placeholder="请输入IP地址"
        />
      </a-form-item>
      <a-form-item label="子网掩码" field="mask">
        <a-input
          v-model="formData.mask"
          style="width: 200px"
          placeholder="请输入子网掩码"
        />
      </a-form-item>
      <a-form-item label="主DNS" field="main_dns">
        <a-input
          v-model="formData.main_dns"
          style="width: 200px"
          placeholder="请输入主DNS"
        />
      </a-form-item>
      <a-form-item label="备DNS" field="sec_dns">
        <a-input
          v-model="formData.sec_dns"
          style="width: 200px"
          placeholder="请输入备DNS"
        /> </a-form-item
      ><a-form-item label="上行速率(kbits/s)" field="uprate">
        <a-input
          v-model="formData.uprate"
          style="width: 200px"
          placeholder="请输入上行速率(kbits/s)"
        /> </a-form-item
      ><a-form-item label="下行速率(kbits/s)" field="downrate">
        <a-input
          v-model="formData.downrate"
          style="width: 200px"
          placeholder="请输入下行速率(kbits/s)"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    watch,
    computed,
  } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidVlanId } from '@/utils/validate';
  import { isValidIPv4, isValidMask } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: 'ID',
          dataIndex: 'id',
          slotName: 'id',
          align: 'center',
        },
        {
          title: '地址段x',
          dataIndex: 'addr_x',
          slotName: 'addr_x',
          align: 'center',
        },
        {
          title: 'IP地址',
          dataIndex: 'ip',
          slotName: 'ip',
          align: 'center',
        },
        {
          title: '子网掩码',
          dataIndex: 'mask',
          slotName: 'mask',
          align: 'center',
        },
        {
          title: '主DNS',
          dataIndex: 'main_dns',
          slotName: 'main_dns',
          align: 'center',
        },
        {
          title: '备DNS',
          dataIndex: 'sec_dns',
          slotName: 'sec_dns',
          align: 'center',
        },
        {
          title: '上行速率(kbits/s)',
          dataIndex: 'uprate',
          slotName: 'uprate',
          align: 'center',
        },
        {
          title: '下行速率(kbits/s)	',
          dataIndex: 'downrate',
          slotName: 'downrate',
          align: 'center',
        },
      ];

      const data = ref<
        {
          id: string;
          addr_x: string;
          ip: string;
          mask: string;
          main_dns: string;
          sec_dns: string;
          uprate: string;
          downrate: string;
        }[]
      >([]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);

      // 表格选择
      const selectedRowKeys = ref<string[]>([]);
      const rowSelection = reactive({
        type: 'checkbox' as const,
        showCheckedAll: true,
        onChange: (rowKeys: string[]) => {
          selectedRowKeys.value = rowKeys;
        },
      });

      // 表单数据
      const formData = reactive({
        id: '',
        addr_x: '',
        ip: '',
        mask: '',
        main_dns: '',
        sec_dns: '',
        uprate: '',
        downrate: '',
      });

      // 表单规则
      const rules = {
        id: [{ required: true, message: '接入点名不能为空' }],
        ip: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        main_dns: [
          { required: true, message: 'DNS地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('DNS地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        sec_dns: [
          { required: true, message: 'DNS地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('DNS地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        mask: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidMask(value)) {
                callback('子网掩码格式不正确，应为：*************');
              } else {
                callback();
              }
            },
          },
        ],
        addr_x: [{ required: true, message: '端口不能为空' }],
      };

      const formRef = ref();

      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_pppoe_server.lua',
            new URLSearchParams({ act: 'oesrv_pool' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            data.value = response.data.data;
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 激活配置
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 删除选中项
      const handleDelete = () => {
        if (selectedRowKeys.value.length === 0) {
          Message.warning('请选择要删除的项');
          return;
        }

        // 实际应调用删除API
        // 这里仅做前端删除
        data.value = data.value.filter(
          (item) => !selectedRowKeys.value.includes(item.id)
        );
        selectedRowKeys.value = [];
        Message.success('删除成功');
      };

      // 显示模态框
      const showModal = () => {
        // 重置表单
        formData.id = '';
        formData.addr_x = '';
        formData.ip = '';
        formData.mask = '';
        formData.main_dns = '';
        formData.sec_dns = '';
        formData.uprate = '';
        formData.downrate = '';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleBeforeOk = (done) => {
        formRef.value.validate().then((errors) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 添加到列表
          data.value.push({
            id: formData.id,
            addr_x: formData.addr_x,
            ip: formData.ip,
            mask: formData.mask,
            main_dns: formData.main_dns,
            sec_dns: formData.sec_dns,
            uprate: formData.uprate,
            downrate: formData.downrate,
          });

          Message.success('添加成功');
          done(true); // 允许模态框关闭
        });
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      // 为每行数据添加唯一标识符
      const renderData = computed(() => {
        return data.value.map((item, index) => ({
          ...item,
          _uniqueKey: `${item.id}_${item.addr_x}_${index}`, // 添加唯一标识符
        }));
      });

      return {
        columns,
        data,
        handleRefresh,
        isRefreshing,
        formData,
        rules,
        formRef,
        isModalVisible,
        showModal,
        handleBeforeOk,
        handleCancel,
        handleDelete,
        rowSelection,
        selectedRowKeys,
        hasPermission,
        renderData,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
