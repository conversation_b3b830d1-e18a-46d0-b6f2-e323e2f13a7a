import { Message } from '@arco-design/web-vue';

/**
 * 全局消息配置工具
 * 提供统一的消息显示方法，带有预设的显示时间
 */
let DEFAULT_DURATION = 5000; // 默认显示5秒

/**
 * 成功消息
 * @param content 消息内容
 * @param duration 显示时长（毫秒）
 */
export function showSuccess(content: string, duration = DEFAULT_DURATION) {
  return Message.success({
    content,
    duration,
  });
}

/**
 * 错误消息
 * @param content 消息内容
 * @param duration 显示时长（毫秒）
 */
export function showError(content: string, duration = DEFAULT_DURATION) {
  return Message.error({
    content,
    duration,
  });
}

/**
 * 警告消息
 * @param content 消息内容
 * @param duration 显示时长（毫秒）
 */
export function showWarning(content: string, duration = DEFAULT_DURATION) {
  return Message.warning({
    content,
    duration,
  });
}

/**
 * 信息消息
 * @param content 消息内容
 * @param duration 显示时长（毫秒）
 */
export function showInfo(content: string, duration = DEFAULT_DURATION) {
  return Message.info({
    content,
    duration,
  });
}

/**
 * 加载消息
 * @param content 消息内容
 * @param duration 显示时长（毫秒）
 */
export function showLoading(content: string, duration = DEFAULT_DURATION) {
  return Message.loading({
    content,
    duration,
  });
}

/**
 * 更新全局默认显示时间
 * @param duration 新的默认显示时间（毫秒）
 */
export function updateDefaultDuration(duration: number) {
  if (duration > 0) {
    // 更新全局变量
    DEFAULT_DURATION = duration;
    // 保存到本地存储，以便在整个应用中保持一致
    localStorage.setItem('message_default_duration', duration.toString());
    return true;
  }
  return false;
}

/**
 * 获取当前默认显示时间
 */
export function getDefaultDuration(): number {
  return DEFAULT_DURATION;
}

// 初始化：从本地存储加载设置
const savedDuration = localStorage.getItem('message_default_duration');
if (savedDuration) {
  // 如果有保存的设置，使用保存的值
  DEFAULT_DURATION = parseInt(savedDuration, 10);
}

// 导出默认对象
export default {
  showSuccess,
  showError,
  showWarning,
  showInfo,
  showLoading,
  updateDefaultDuration,
  getDefaultDuration,
};
