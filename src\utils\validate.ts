/**
 * 验证 IPv4 地址格式
 * @param {string} ip - 要验证的 IP 地址
 * @returns {boolean} - 是否合法
 */
export function isValidIPv4(ip) {
  const ipv4Regex =
    /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipv4Regex.test(ip);
}

/**
 * 验证 IPv6 地址格式（简化版）
 * @param {string} ip - 要验证的 IP 地址
 * @returns {boolean} - 是否合法
 */
export function isValidIPv6(ip) {
  const ipv6Regex =
    /^([0-9a-fA-F]{1,4}:){7}([0-9a-fA-F]{1,4})|(([0-9a-fA-F]{1,4}:){1,7}:)|((:[0-9a-fA-F]{1,4}){1,7})$/;
  return ipv6Regex.test(ip);
}

/**
 * 验证 IP 地址（自动判断 IPv4 或 IPv6）
 * @param {string} ip - 要验证的 IP 地址
 * @returns {boolean} - 是否合法
 */
export function isValidIP(ip) {
  return isValidIPv4(ip) || isValidIPv6(ip);
}

/**
 * 验证 子网掩码 格式
 * @param {string} mask - 要验证的 子网掩码
 * @returns {boolean} - 是否合法
 */
export function isValidMask(ip) {
  const ipv4Regex =
    /^(255|254|252|248|240|224|192|128|0)\.(255|254|252|248|240|224|192|128|0)\.(255|254|252|248|240|224|192|128|0)\.(255|254|252|248|240|224|192|128|0)$/;
  return ipv4Regex.test(ip);
}

/**
 * 验证虚拟mac ID（值范围：1~255）
 * @param {number|string} value - 要验证的虚拟mac ID
 * @returns {boolean} - 是否合法
 */
export function isValidVirtualMacId(value) {
  const numValue = Number(value);
  return !Number.isNaN(numValue) && numValue >= 1 && numValue <= 255;
}

/**
 * 验证VLAN ID（值范围：0~4095）
 * @param {number|string} value - 要验证的VLAN ID
 * @returns {boolean} - 是否合法
 */
export function isValidVlanId(value) {
  const numValue = Number(value);
  return !Number.isNaN(numValue) && numValue >= 0 && numValue <= 4095;
}

/**
 * 验证MAC地址头格式
 * @param {string} mac - 要验证的MAC地址头，格式如：00-07-74-XX
 * @returns {boolean} - 是否合法
 */
export function isValidMacHead(mac) {
  if (!mac) return true; // 允许为空，使用缺省值
  // 格式：00-07-74-XX
  const macRegex = /^([0-9A-Fa-f]{2}-){3}[0-9A-Fa-f]{2}$/;
  return macRegex.test(mac);
}
