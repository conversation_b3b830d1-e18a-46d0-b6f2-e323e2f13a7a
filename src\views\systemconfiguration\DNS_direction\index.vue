<!-- DNS定向 -->
<template>
  <div v-if="isComponentVisible" class="container">
    <div v-if="isRefreshing" class="overlay">
      <div class="loader"></div>
    </div>
    <Breadcrumb :items="['menu.system-configuration', 'menu.DNS_direction']" />
    <a-card class="general-card" :title="$t('menu.DNS_direction')">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="{ searchQuery }"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="number" :label="$t('groupID')">
                  <a-input
                    v-model="searchQuery"
                    :placeholder="$t('searchTable.form.number.placeholder')"
                  />
                  <a-button
                    type="primary"
                    @click="filterData"
                    style="margin-left: 10px"
                  >
                    <template #icon>
                      <icon-search />
                    </template>
                    {{ $t('searchTable.form.search') }}
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-col :flex="'86px'" style="text-align: right"> </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <!-- 新建按钮 -->
            <a-button
              type="primary"
              :disabled="!hasPermission('dnsDirectionAdd')"
              @click="showModal"
            >
              <template #icon>
                <icon-plus />
              </template>
              {{ $t('searchTable.operation.create') }}
            </a-button>
            <a-upload action="/">
              <template #upload-button>
                <!-- <a-button>
                  {{ $t('searchTable.operation.import') }}
                </a-button> -->
              </template>
            </a-upload>
          </a-space>
        </a-col>
        <!-- 右侧按钮 -->
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <!-- <a-button>
            <template #icon>
              <icon-download />
            </template>
            {{ $t('searchTable.operation.download') }}
          </a-button> -->
          <a-tooltip :content="$t('searchTable.actions.refresh')">
            <div class="action-icon" @click="handleRefresh">
              <icon-refresh size="18" />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <a-table
        :columns="columns8"
        :data="filteredData"
        style="margin-top: 20px"
        :pagination="false"
      >
        <template #group_id="{ record }">
          <span>{{ record.group_id }}</span>
        </template>

        <template #mode="{ record }">
          <span>{{ record.mode }}</span>
        </template>

        <template #orig_dns="{ record }">
          <span>{{ record.orig_dns }}</span>
        </template>

        <template #new_dns="{ record }">
          <span>{{ record.new_dns }}</span>
        </template>

        <template #option="{ record }">
          <a-button
            id="dnsDirectionDelete"
            :disabled="!hasPermission('dnsDirectionDelete')"
            type="primary"
            @click="deleteRow(record)"
          >
            <template #icon>
              <IconEye />
            </template>
            <template #default>删除</template>
          </a-button>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:visible="isModalVisible"
      title="新建DNS定向"
      draggable
      :mask-closable="false"
      :unmount-on-close="false"
      @before-ok="handleOk"
      @cancel="handleCancel"
    >
      <a-col :span="24">
        <a-alert type="info" banner closable>
          <template #icon> </template>
          <template #title>
            说明：配置不同出口组的DNS定向, 如果原DNS配置0.0.0.0, 全部定向,
            否则必须匹配原DNS,才DNS定向。</template
          >
        </a-alert>
      </a-col>
      <br />
      <a-form :model="formData" :rules="rules" ref="formRef">
        <a-form-item :label="$t('groupID')" field="group_id">
          <a-select v-model="formData.group_id" :options="optionsGroup" />
        </a-form-item>
        <a-form-item :label="$t('mode')" field="mode">
          <a-select v-model="formData.mode" :options="optionsMode" />
        </a-form-item>
        <a-form-item :label="$t('origDNS')" field="orig_dns">
          <a-input v-model="formData.orig_dns" />
        </a-form-item>
        <a-form-item :label="$t('newDNS')" field="new_dns">
          <a-input v-model="formData.new_dns" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
  } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import {
    handleNotification1,
    handleNotification2,
  } from '../../../utils/info';

  // 定义数据接口
  export interface ExportInfo {
    exportName: string;
    group_id: string;
    portaddresses: string;
  }

  const { hasPermission } = usePermission();

  export default defineComponent({
    setup() {
      const { t } = useI18n();
      const userStore = useUserStore();

      // 验证IP地址格式
      const validateIpAddress = (ip: string): boolean => {
        // 如果是0.0.0.0，直接返回true（特殊处理）
        if (ip === '0.0.0.0') return true;

        const ipRegex =
          /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
      };

      // 定义options的类型为OptionValue，明确其结构
      const formData = reactive({
        group_id: '0', // 默认值设置为0
        mode: '', //
        orig_dns: '',
        new_dns: '',
      });
      // 定义columns数组中元素的类型
      const optionsGroup = ref([
        { label: '0', value: '0' },
        { label: '1', value: '1' },
        { label: '2', value: '2' },
        { label: '3', value: '3' },
      ]);

      // 选项列表，用于mode选择框
      const optionsMode = ref([
        { label: '主DNS', value: 'primary' }, // 主DNS
        { label: '备DNS', value: 'secondary' }, // 备DNS
      ]);

      const columns8 = [
        {
          title: '组ID',
          dataIndex: 'group_id',
          slotName: 'group_id',
        },
        {
          title: '主备',
          dataIndex: 'mode',
          slotName: 'mode',
        },
        {
          title: '原DNS',
          dataIndex: 'orig_dns',
          slotName: 'orig_dns',
        },
        {
          title: '新DNS',
          dataIndex: 'new_dns',
          slotName: 'new_dns',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];

      const data = ref<any[]>([]);
      const filteredData = ref<any[]>([]);

      const isModalVisible = ref(false);
      const isComponentVisible = ref(true);
      const isRefreshing = ref(false);
      const searchQuery = ref('');

      const formRef = ref();
      // 获取数据
      // fetchData函数
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'dns_rdr' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            data.value = response.data.data || [];
            filteredData.value = [...data.value];
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Fetch error:', error);
          Message.error('获取数据失败');
        }
      };
      // 删除行
      const deleteRow = async (record: { group_id: string; mode: string }) => {
        try {
          if (!hasPermission('dnsDirectionDelete')) {
            Message.error('您没有权限');
            return;
          }
          const {
            group_id: groupID, // 重命名为驼峰式
            mode,
          } = record;

          // 发送删除请求
          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'dns_rdr_del',
              group_id: groupID,
              mode,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 删除成功后重新获取数据
            await fetchData();
            Message.success('删除成功');
          } else {
            Message.error('删除失败');
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('删除失败');
        }
      };

      // 显示模态框
      const showModal = () => {
        if (!hasPermission('dnsDirectionAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 重置表单数据
        formData.group_id = '0';
        formData.mode = '';
        formData.orig_dns = '';
        formData.new_dns = '';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleOk = async (done) => {
        try {
          if (!hasPermission('dnsDirectionAdd')) {
            Message.error('您没有权限');
            return;
          }
          // 验证表单
          formRef.value.validate((errors) => {
            if (errors) {
              // 表单验证失败
              Message.error('表单验证失败，请检查输入');
              done(false); // 阻止模态框关闭
              return;
            }

            const {
              group_id: groupID, // 重命名为驼峰式
              mode,
              orig_dns: origDns,
              new_dns: newDns,
            } = formData;

            // 发送添加请求
            axios
              .post(
                '/lua/set_cfg.lua',
                new URLSearchParams({
                  act: 'dns_rdr_add',
                  group_id: groupID,
                  mode,
                  orig_dns: origDns,
                  new_dns: newDns,
                }),
                {
                  headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                  },
                }
              )
              .then((response) => {
                if (response.data.code === 200) {
                  fetchData();
                  Message.success('添加成功');
                  isModalVisible.value = false;
                  formData.group_id = '0'; // 重置表单
                  formData.mode = '';
                  formData.orig_dns = '';
                  formData.new_dns = '';
                  done(true); // 允许模态框关闭
                } else {
                  Message.error({
                    content: response.data.err,
                    duration: 5000,
                  });
                  done(false); // 阻止模态框关闭
                }
              })
              .catch((error) => {
                console.error('Error adding data:', error);
                Message.error('添加失败');
                done(false); // 阻止模态框关闭
              });
          });
          return false;
        } catch (error) {
          console.error('Error adding data:', error);
          Message.error('添加失败');
          done(false);
          return false;
        }
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      // 刷新数据
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 过滤数据
      const filterData = () => {
        const rawData = data.value;

        if (searchQuery.value.trim() === '') {
          filteredData.value = [...rawData];
          Message.error('请填写组ID');
          return;
        }

        filteredData.value = rawData.filter((item) =>
          item.group_id.includes(searchQuery.value)
        );

        if (filteredData.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      // 组件加载时获取数据
      onMounted(() => {
        fetchData();
      });

      const rules = {
        group_id: [{ required: true, message: t('groupIDRequired') }],
        mode: [{ required: true, message: t('modeRequired') }],
        orig_dns: [
          { required: true, message: t('origDNSRequired') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateIpAddress(value)) {
                callback('原DNS格式不正确，应为：***********');
              } else {
                callback();
              }
            },
          },
        ],
        new_dns: [
          { required: true, message: t('newDNSRequired') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateIpAddress(value)) {
                callback('新DNS格式不正确，应为：***********');
              } else {
                callback();
              }
            },
          },
        ],
      };

      return {
        optionsGroup,
        optionsMode,
        columns8,
        data,
        deleteRow,
        hasPermission,
        isModalVisible,
        formData,
        rules,
        formRef,
        showModal,
        handleOk,
        handleCancel,
        isComponentVisible,
        handleRefresh,
        isRefreshing,
        searchQuery,
        filterData,
        filteredData,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }

  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
  }
</style>
