<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="24">
        <a-space>
          <!-- 选择ID下拉框 -->
          <a-select
            v-model="selectedId"
            :style="{ width: '200px' }"
            placeholder="已配置的PPPOE server 地址池的ID："
            @change="handleIdChange"
          >
            <a-option v-for="item in idOptions" :key="item" :value="item">
              {{ item }}
            </a-option>
          </a-select>

          <!-- 按钮组 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            添加
          </a-button>

          <a-button @click="handleDelete">
            <template #icon>
              <icon-delete />
            </template>
            删除
          </a-button>
          <!-- 刷新按钮 -->
          <a-button @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            激活
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <!-- 显示当前选择的ID信息 -->
    <div v-if="selectedId" style="margin-bottom: 16px">
      <p
        >地址池ID为{{ selectedId }}的IP地址: {{ poolIpInfo.ip }}, 子网掩码:
        {{ poolIpInfo.mask }}</p
      >
      <p style="color: red">注意: 删除操作有2方法，选其一：</p>
      <p style="color: red">1、删除固定地址后重新启动生效</p>
      <p style="color: red"
        >2、删除固定地址后，删除地址池再添加激活地址池，再配置固定地址</p
      >
    </div>

    <!-- 添加表格组件 -->
    <a-table
      :columns="columns"
      :data="renderData"
      :row-selection="rowSelection"
      row-key="uniqueKey"
      :loading="isRefreshing"
      :pagination="{ showTotal: true }"
    />
  </a-card>

  <a-modal
    v-model:visible="isModalVisible"
    :width="700"
    title="添加固定地址"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-form :model="formData" :rules="rules" ref="formRef">
      <a-form-item label="ID-地址段x" field="id">
        <a-input
          v-model="formData.id"
          style="width: 200px"
          placeholder="ID-地址段x"
          disabled
        />
      </a-form-item>
      <a-form-item label="IP地址" field="ip">
        <a-input
          v-model="formData.ip"
          style="width: 200px"
          placeholder="请输入IP地址"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    watch,
    computed,
  } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';

  interface FixedAddrItem {
    id: string;
    ip: string;
    uniqueKey?: string; // 添加唯一标识符类型，去掉下划线前缀
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // ID选择相关
      const idOptions = ref<string[]>(['1-0', '1-1']); // 默认两个选项，根据实际情况可以从接口获取
      const selectedId = ref<string>('');
      const poolIpInfo = ref({ ip: '', mask: '' });

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: 'ID-地址段x',
          dataIndex: 'id',
          align: 'center',
        },
        {
          title: 'IP地址',
          dataIndex: 'ip',
          align: 'center',
        },
      ];

      const data = ref<FixedAddrItem[]>([]);

      const isRefreshing = ref(false);
      const isModalVisible = ref(false);

      // 表格选择
      const selectedRowKeys = ref<string[]>([]);
      const rowSelection = reactive({
        type: 'checkbox' as const,
        showCheckedAll: true,
        onChange: (rowKeys: string[]) => {
          selectedRowKeys.value = rowKeys;
        },
      });

      // 表单数据
      const formData = reactive({
        id: '',
        ip: '',
      });

      // 表单规则
      const rules = {
        id: [{ required: true, message: 'ID-地址段x不能为空' }],
        ip: [
          { required: true, message: 'IP地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
      };

      const formRef = ref();

      // 获取数据
      const fetchData = async () => {
        if (!selectedId.value) return;

        isRefreshing.value = true;
        try {
          const response = await axios.post(
            '/lua/get_cfg_pppoe_server.lua',
            new URLSearchParams({ act: 'oesrv_fix', id: selectedId.value }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 使用模拟数据，实际应该使用response.data.data
            if (selectedId.value === '1-0') {
              data.value = [
                { id: '1-0', ip: '**************' },
                { id: '1-0', ip: '**************' },
              ];
            } else if (selectedId.value === '1-1') {
              data.value = [{ id: '1-1', ip: '' }];
            } else {
              data.value = [];
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        } finally {
          isRefreshing.value = false;
        }
      };

      // 获取地址池信息
      const getPoolInfo = async (id: string) => {
        // 模拟数据，实际应该调用API获取
        if (id === '1-0') {
          poolIpInfo.value = { ip: '************', mask: '*************' };
        } else if (id === '1-1') {
          poolIpInfo.value = { ip: '************', mask: '*************' };
        } else {
          poolIpInfo.value = { ip: '', mask: '' };
        }
      };

      // 选择ID变化
      const handleIdChange = (value: string) => {
        selectedId.value = value;

        // 根据ID获取地址池信息
        getPoolInfo(value);

        // 获取固定地址列表
        fetchData();
      };

      // 激活配置
      const handleRefresh = () => {
        if (!selectedId.value) {
          Message.warning('请先选择ID');
          return;
        }
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 删除选中项
      const handleDelete = () => {
        if (!selectedId.value) {
          Message.warning('请先选择ID');
          return;
        }
        if (selectedRowKeys.value.length === 0) {
          Message.warning('请选择要删除的项');
          return;
        }

        // 实际应调用删除API
        // 这里仅做前端删除
        data.value = data.value.filter(
          (item) => !selectedRowKeys.value.includes(item.uniqueKey || '')
        );
        selectedRowKeys.value = [];
        Message.success('删除成功');
      };

      // 显示模态框
      const showModal = () => {
        if (!selectedId.value) {
          Message.warning('请先选择ID');
          return;
        }
        // 重置表单
        formData.id = selectedId.value;
        formData.ip = '';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleBeforeOk = (done: (closed: boolean) => void) => {
        formRef.value.validate().then((errors: any) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 添加到列表
          data.value.push({
            id: formData.id,
            ip: formData.ip,
          });

          Message.success('添加成功');
          done(true); // 允许模态框关闭
        });
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            // 如果已经选择了ID，则获取数据
            if (selectedId.value) {
              fetchData();
            }
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          // 初始化时默认选择第一个ID选项
          const [firstId] = idOptions.value;
          if (firstId) {
            selectedId.value = firstId;
            handleIdChange(selectedId.value);
          }
        }
      });

      // 为每行数据添加唯一标识符
      const renderData = computed(() => {
        return data.value.map((item, index) => ({
          ...item,
          uniqueKey: `${item.id}_${index}`, // 添加唯一标识符
        }));
      });

      return {
        columns,
        data,
        handleRefresh,
        isRefreshing,
        formData,
        rules,
        formRef,
        isModalVisible,
        showModal,
        handleBeforeOk,
        handleCancel,
        handleDelete,
        rowSelection,
        selectedRowKeys,
        hasPermission,
        renderData,
        idOptions,
        selectedId,
        handleIdChange,
        poolIpInfo,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
