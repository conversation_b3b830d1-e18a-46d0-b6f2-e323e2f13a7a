// import { DEFAULT_LAYOUT } from '../base';
// import { AppRouteRecordRaw } from '../types';

// const OPERATIONPERMISSIONS: AppRouteRecordRaw = {
//   path: '/operaterpermissions',
//   name: 'operaterpermissions',
//   component: DEFAULT_LAYOUT,
//   meta: {
//     locale: 'Operater_Permission',
//     icon: 'icon-menu',
//     permissions: ['operationPermissions'],
//     requiresAuth: true,
//     order: 7,
//   },
//   children: [
//     {
//       path: 'Role_Permission',
//       name: 'Role_Permission',
//       component: () =>
//         import('@/views/Operater_Permission/Role_Permission/index.vue'),
//       meta: {
//         locale: 'menu.Role_Permission',
//         icon: 'icon-user',
//         permissions: ['RolePermissions'],
//         requiresAuth: true,
//         roles: ['admin'],
//       },
//     },
//   ],
// };

// export default OPERATIONPERMISSIONS;
