import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const SYSTEMCONTROL: AppRouteRecordRaw = {
  path: '/systemcontrol',
  name: 'systemcontrol',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.system-control',
    icon: 'icon-link',
    permissions: ['systemControl'],
    requiresAuth: true,
    order: 2,
  },
  children: [
    {
      path: 'operation-control',
      name: 'operation-control',
      component: () => import('@/views/systemcontrol/operation-control/index.vue'),
      meta: {
        locale: 'menu.operation-control',
        icon: 'icon-menu',
        permissions: ['operationControl'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      path: 'network-diagnosis',
      name: 'network-diagnosis',
      component: () => import('@/views/systemcontrol/network-diagnosis/index.vue'),
      meta: {
        locale: 'menu.network-diagnosis',
        icon: 'icon-menu',
        permissions: ['NetworkDiagnosis'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      path: 'packet-capture-analysis',
      name: 'packet-capture-analysis',
      component: () => import('@/views/systemcontrol/packet-capture-analysis/index.vue'),
      meta: {
        locale: 'menu.packet-capture-analysis',
        icon: 'icon-menu',
        permissions: ['packetCaptureAnalysis'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },

    // {
    //   path: 'trigger-session',
    //   name: 'trigger-session',
    //   component: () =>
    //     import('@/views/systemcontrol/trigger-session/index.vue'),
    //   meta: {
    //     locale: 'menu.trigger-session',
    //     icon: 'icon-message',
    //     requiresAuth: true,
    //     roles: ['admin'],
    //   },
    // },

    // {
    //   path: 'trigger-readgroup',
    //   name: 'trigger-readgroup',
    //   component: () =>
    //     import('@/views/systemcontrol/trigger-readgroup/index.vue'),
    //   meta: {
    //     locale: 'menu.trigger-readgroup',
    //     icon: 'icon-user-group',
    //     requiresAuth: true,
    //     roles: ['admin'],
    //   },
    // },
  ],
};

export default SYSTEMCONTROL;
