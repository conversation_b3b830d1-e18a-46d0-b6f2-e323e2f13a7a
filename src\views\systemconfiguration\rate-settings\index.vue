<!-- 速率控制 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.rate-settings']" />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane
        key="1"
        title="基本配置"
        v-if="hasPermission('bindMultiplePort')"
      >
        <BasicConfiguration :active="activeTabKey === '1'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane
        key="2"
        title="时间配置"
        v-if="hasPermission('bindMultiplePort')"
      >
        <TimeConfiguration :active="activeTabKey === '2'" />
      </a-tab-pane>
      <!-- 标签页3 -->
      <a-tab-pane key="3" title="外层vlan" v-if="hasPermission('bindMultiplePort')">
        <OutVlan :active="activeTabKey === '3'" />
      </a-tab-pane>
      <!-- 标签页4 -->
      <a-tab-pane key="4" title="内层vlan" v-if="hasPermission('bindMultiplePort')">
        <InVlan :active="activeTabKey === '4'" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  import BasicConfiguration from './basic-configuration.vue';
  import TimeConfiguration from './time-configuration.vue';
  import InVlan from './in-vlan.vue';
  import OutVlan from './out-vlan.vue';

  export default defineComponent({
    components: {
      BasicConfiguration,
      TimeConfiguration,
      InVlan,
      OutVlan,
    },
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const activeTabKey = ref('1');

      const handleTabChange = (key: string) => {
        activeTabKey.value = key;
      };

      return {
        activeTabKey,
        handleTabChange,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
</style>
