<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <div class="port-selection-container">
      <span class="form-label">配置端口启动pppoe server接入：</span>
      <div class="port-checkbox-group">
        <div v-for="port in portList" :key="port" class="port-checkbox-item">
          <a-checkbox v-model="selectedPorts[port]">端口{{ port }}</a-checkbox>
        </div>
      </div>
      <div class="action-buttons">
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          @click="saveAction"
          :loading="isSubmitting"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>提交</template>
        </a-button>
      </div>
    </div>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const isSubmitting = ref(false);

      // 所有可用端口列表
      const portList = ['0', '1', '2', '3', '4'];

      // 选中的端口
      const selectedPorts = reactive({
        '0': false,
        '1': false,
        '2': false,
        '3': false,
        '4': false,
      });

      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_pppoe_server.lua',
            new URLSearchParams({ act: 'oesrv_access' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            // 重置所有端口状态
            Object.keys(selectedPorts).forEach((port) => {
              selectedPorts[port] = false;
            });

            // 设置选中的端口
            if (response.data.data && Array.isArray(response.data.data)) {
              response.data.data.forEach((item) => {
                if (item.port && portList.includes(item.port)) {
                  selectedPorts[item.port] = true;
                }
              });
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }

        isSubmitting.value = true;

        try {
          // 收集选中的端口
          const enabledPorts = Object.keys(selectedPorts).filter(
            (port) => selectedPorts[port]
          );

          const response = await axios.post(
            '/lua/set_cfg_pppoe_server.lua',
            new URLSearchParams({
              act: 'oesrv_access',
              ports: enabledPorts.join(','),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          console.error('提交配置失败:', error);
          Message.error('配置请求失败');
        } finally {
          isSubmitting.value = false;
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        portList,
        selectedPorts,
        saveAction,
        hasPermission,
        isSubmitting,
      };
    },
  });
</script>

<style scoped>
  .port-selection-container {
    padding: 10px 20px;
  }

  .port-checkbox-group {
    margin: 20px 0;
  }

  .port-checkbox-item {
    margin-bottom: 12px;
  }

  .action-buttons {
    margin-top: 30px;
  }
</style>
