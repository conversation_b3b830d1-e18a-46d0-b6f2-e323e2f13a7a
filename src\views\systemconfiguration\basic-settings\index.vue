<template>
  <div class="container">
    <Breadcrumb
      :items="['menu.system-configuration', 'menu.basic-settings']"
    />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane key="1" title="工作模式" v-if="hasPermission('workMode')">
        <work_mode :active="activeTabKey === '1'"/>
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane key="2" title="基本配置" v-if="hasPermission('BasicConfiguration')">
        <basic_configuration :active="activeTabKey === '2'" />
      </a-tab-pane>
      <!-- 标签页3 -->
      <a-tab-pane key="3" title="基本控制" v-if="hasPermission('BasicControl')">
        <basic_control :active="activeTabKey === '3'" />
      </a-tab-pane>
      <!-- 标签页4 -->
      <a-tab-pane key="4" title="带宽控制" v-if="hasPermission('bandwidthControl')">
        <bandwidth_control :active="activeTabKey === '4'" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    ref,
    onMounted,
    onBeforeUnmount,
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';


  
import work_mode from './work_mode.vue';
import basic_configuration from './basic_configuration.vue';
import basic_control from './basic_control.vue';
import bandwidth_control from './bandwidth_control.vue';

  export default defineComponent({    
    components: {
      work_mode,
      basic_configuration,
      basic_control,
      bandwidth_control
    },
    setup() {
      
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const activeTabKey = ref('1');
      
      const handleTabChange = (key: string) => {
        activeTabKey.value = key;
      };

      return {
        activeTabKey,
        handleTabChange,
        hasPermission
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .input-field {
    width: 135px;
    margin-left: 5px;
  }

  .hint {
    flex: 1;
    text-align: left;
    color: gray;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }

  .status-text {
    margin-left: 10px;
    font-size: 14px;
  }

  .session {
    text-align: left;
  }

  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .button {
    text-align: center;
  }

  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
</style>
