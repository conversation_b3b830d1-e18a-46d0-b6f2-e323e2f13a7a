// 定义数据接口
export interface StatusInfo {
  describe: string;
  data: string;
}

// 定义模拟的数据

export const getStatusInfo1 = (): StatusInfo[] => {
  return [
    { describe: 'ICMP NAT输出创建失败数目', data: '0' },
    { describe: 'ICMP NAT输入查找失败数目', data: '0' },
    { describe: 'ICMP NAT-FRAG创建失败数目', data: '0|0' },
    { describe: 'ICMP NAT-FRAG输入查找失败数目', data: '0' },
    { describe: 'ICMP NAT-FRAG输出查找失败数目', data: '0' },
  ];
};
// 定义模拟的数据

export const getStatusInfo2 = (): StatusInfo[] => {
  return [
    { describe: 'UDP NAT输出创建失败数目', data: '0' },
    { describe: 'UDP NAT输入查找失败数目', data: '0' },
    { describe: 'UDP NAT-FRAG创建失败数目', data: '0|0' },
    { describe: 'UDP NAT-FRAG输入查找失败数目', data: '0' },
    { describe: 'UDP NAT-FRAG输出查找失败数目', data: '0' },
    { describe: 'UDP RDR NAT创建失败数目', data: '0' },
  ];
};
// 定义模拟的数据

export const getStatusInfo3 = (): StatusInfo[] => {
  return [
    { describe: 'TCP NAT输出创建失败数目', data: '0' },
    { describe: 'TCP NAT输入查找失败数目', data: '0' },
    { describe: 'TCP NAT-FRAG创建失败数目', data: '0|0' },
    { describe: 'TCP NAT-FRAG输入查找失败数目', data: '0' },
    { describe: 'TCP NAT-FRAG输出查找失败数目', data: '0' },
    { describe: 'TCP RDR NAT创建失败数目', data: '0' },
  ];
};
// 定义模拟的数据

export const getStatusInfo4 = (): StatusInfo[] => {
  return [
    { describe: 'Other NAT输入查找失败数目', data: '0' },
    { describe: 'Other NAT输出创建失败数目', data: '0' },
  ];
};

// 定义模拟的数据

export const getStatusInfo5 = (): StatusInfo[] => {
  return [
    { describe: '入口自身MAC地址', data: 'B0:51:8E:09:95:9E' },
    { describe: '入口网关MAC地址', data: '00:00:00:00:00:00' },
    { describe: '入口网关MAC过期时间(秒)', data: '0' },
    { describe: '入口接收速率(Kbits/s)', data: '0' },
    { describe: '入口转送速率(Kbits/s)', data: '0' },
    { describe: '出口接收速率(Kbits/s)', data: '0' },
    { describe: '出口转送速率(Kbits/s)', data: '0' },
  ];
};
// 定义模拟的数据

export const getStatusInfo6 = (): StatusInfo[] => {
  return [
    { describe: 'IP 会话数目', data: '0' },
    { describe: 'NAT 会话数目', data: '0' },
    { describe: 'NAT-FRAG 会话数目', data: '0' },
    { describe: 'CPU 空载运行', data: '10529742' },
  ];
};
// 定义模拟的数据

export const getStatusInfo7 = (): StatusInfo[] => {
  return [
    { describe: 'NAT 会话每秒创建数目', data: '0' },
    { describe: 'NAT 会话每秒释放数目', data: '0' },
    { describe: 'NAT-FRAG 会话每秒创建数目', data: '0' },
    { describe: 'NAT-FRAG 会话每秒释放数目', data: '0' },
  ];
};
// 定义模拟的数据

export const getStatusInfo8 = (): StatusInfo[] => {
  return [
    { describe: '软件版本', data: '3.1 Rel Jun 3 2024' },
    { describe: '版本授权时间', data: '20250101' },
    { describe: '系统时间', data: '20241203 17:39:24' },
    { describe: '授权并发用户', data: '5000' },
    { describe: '运行时间', data: '0 Day 23:48:00' },
    { describe: '发送ARP 请求数', data: '10' },
    { describe: '发送ARP 回应数', data: '0' },
  ];
};
// 定义模拟的数据

export const getStatusInfo9 = (): StatusInfo[] => {
  return [
    { describe: 'Input 转发数据包数目', data: '0' },
    { describe: 'Output 转发数据包数目', data: '0' },
    { describe: 'Input 丢弃不支持ICMP数目', data: '0' },
    { describe: 'Output 丢弃不支持ICMP数目', data: '0' },
  ];
};
// 定义模拟的数据

export const getStatusInfo10 = (): StatusInfo[] => {
  return [
    { describe: 'IP会话连接溢出数目', data: '0' },
    { describe: '连接溢出最多IP地址', data: '0.0.0.0(0)' },
    { describe: 'IP会话溢出数目', data: '0' },
    { describe: 'NAT会话溢出数目', data: '0' },
    { describe: 'NAT-FRAG会话溢出数目', data: '0' },
    { describe: 'NAT-FRAG创建冲突数目', data: '0' },
  ];
};
