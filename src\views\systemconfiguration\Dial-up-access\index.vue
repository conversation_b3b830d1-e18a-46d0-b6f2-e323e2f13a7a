<!-- 拨号接入组件 -->
<template>
  <div class="container">
    <Breadcrumb
      :items="['menu.system-configuration', 'menu.Authorization_upgrade']"
    />

    <a-card title="拨号接入">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-col :span="8">
          <a-card title="拨号接入" :bordered="false" :style="{ width: '100%' }">
            <template #extra> </template>
            <div class="session">
              <div class="form-item">
                <span>拨号连接协商重试次数：</span>
                <a-tooltip
                  content="不填缺省为3"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.neg_trynum"
                    class="input-field"
                    :status="negTrynumError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="negTrynumError" class="error-message">{{
                  negTrynumError
                }}</div>
              </div>
              <div class="form-item">
                <span>拨号连接协商超时：</span>
                <a-tooltip
                  content="单位：毫秒，不填缺省为249"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.neg_timeout"
                    class="input-field"
                    :status="negTimeoutError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="negTimeoutError" class="error-message">{{
                  negTimeoutError
                }}</div>
              </div>
              <div class="form-item">
                <span>拨号连接认证重试次数：</span>
                <a-tooltip
                  content="不填缺省为1"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.auth_trynum"
                    class="input-field"
                    :status="authTrynumError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="authTrynumError" class="error-message">{{
                  authTrynumError
                }}</div>
              </div>
              <div class="form-item">
                <span>拨号连接认证超时：</span>
                <a-tooltip
                  content="单位：毫秒，不填缺省为3000"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.auth_timeout"
                    class="input-field"
                    :status="authTimeoutError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="authTimeoutError" class="error-message">{{
                  authTimeoutError
                }}</div>
              </div>
              <div class="form-item">
                <span>模拟用户MAC地址头：</span>
                <a-tooltip
                  content="格式：00-07-74-XX，缺省：00-07-74-80"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.vmac_head"
                    class="input-field"
                    :status="vmacHeadError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="vmacHeadError" class="error-message">{{
                  vmacHeadError
                }}</div>
              </div>
              <div class="form-item">
                <span>嗅探web服务器地址：</span>
                <a-tooltip
                  content="0.0.0.0 表示不进行嗅探"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.sniffer_addr"
                    class="input-field"
                    :status="snifferAddrError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="snifferAddrError" class="error-message">{{
                  snifferAddrError
                }}</div>
              </div>
              <br />
              <div class="form-item">
                <span>拨号连接端口支持：</span>
                <a-tooltip
                  content=" 此项修改后需要重启虚机"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-select
                    v-model="formData.out_vlan"
                    :options="[
                      { label: '无vlan', value: 'none' },
                      { label: '一层vlan', value: 'vlan' },
                      { label: '二层vlan', value: 'qing' },
                    ]"
                    :style="{ width: '135px' }"
                  />
                </a-tooltip>
              </div>
              <br />
              <div class="button">
                <a-button
                  id="dialAccessSubmit"
                  :disabled="!hasPermission('dialAccessSubmit')"
                  type="primary"
                  @click="handleNotification1"
                >
                  <template #icon>
                    <icon-check />
                  </template>
                  <template #default>提交</template>
                </a-button>

                <!-- <a-button
                  type="secondary"
                  style="margin-left: 10px"
                  @click="handleNotification2"
                >
                  <template #icon>
                    <icon-refresh />
                  </template>
                  <template #default>重置</template>
                </a-button> -->
              </div>
            </div>
          </a-card>
        </a-col>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import {
    handleNotification4,
    handleNotification1,
    handleNotification2,
    handleNotification3,
  } from '../../../utils/info';

  const { hasPermission } = usePermission();

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const formData = reactive({
        neg_trynum: '',
        neg_timeout: '',
        auth_trynum: '',
        auth_timeout: '',
        vmac_head: '',
        sniffer_addr: '',
        out_vlan: '',
      });

      // 添加错误状态
      const negTrynumError = ref('');
      const negTimeoutError = ref('');
      const authTrynumError = ref('');
      const authTimeoutError = ref('');
      const vmacHeadError = ref('');
      const snifferAddrError = ref('');

      // 验证IP地址格式
      const validateIpAddress = (ip: string): boolean => {
        if (ip === '0.0.0.0') return true; // 特殊情况，0.0.0.0 表示不进行嗅探
        const ipRegex =
          /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
      };

      // 验证MAC地址头格式
      const validateMacHead = (mac: string): boolean => {
        if (!mac) return true; // 允许为空，使用缺省值
        // 格式：00-07-74-XX
        const macRegex = /^([0-9A-Fa-f]{2}-){3}[0-9A-Fa-f]{2}$/;
        return macRegex.test(mac);
      };

      // 验证数字输入
      const validateNumberInput = (
        input: string,
        min: number,
        max: number
      ): boolean => {
        if (!input) return true; // 允许为空，使用缺省值
        const num = Number(input);
        return !Number.isNaN(num) && num >= min && num <= max;
      };

      onMounted(async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'oeclient_setting' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            formData.neg_trynum = response.data.data.neg_trynum;
            formData.neg_timeout = response.data.data.neg_timeout;
            formData.auth_trynum = response.data.data.auth_trynum;
            formData.auth_timeout = response.data.data.auth_timeout;
            formData.vmac_head = response.data.data.vmac_head;
            formData.sniffer_addr = response.data.data.sniffer_addr;
            formData.out_vlan = response.data.data.out_vlan;
          } else {
            console.error('Failed to fetch data:', (response as any).data);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      });

      // 校验所有字段
      const validateAllFields = (): boolean => {
        let isValid = true;

        // 验证拨号连接协商重试次数
        if (
          formData.neg_trynum &&
          !validateNumberInput(formData.neg_trynum, 0, 100)
        ) {
          negTrynumError.value = '请输入有效的数字';
          isValid = false;
        } else {
          negTrynumError.value = '';
        }

        // 验证拨号连接协商超时
        if (
          formData.neg_timeout &&
          !validateNumberInput(formData.neg_timeout, 0, 10000)
        ) {
          negTimeoutError.value = '请输入有效的毫秒数';
          isValid = false;
        } else {
          negTimeoutError.value = '';
        }

        // 验证拨号连接认证重试次数
        if (
          formData.auth_trynum &&
          !validateNumberInput(formData.auth_trynum, 0, 100)
        ) {
          authTrynumError.value = '请输入有效的数字';
          isValid = false;
        } else {
          authTrynumError.value = '';
        }

        // 验证拨号连接认证超时
        if (
          formData.auth_timeout &&
          !validateNumberInput(formData.auth_timeout, 0, 10000)
        ) {
          authTimeoutError.value = '请输入有效的毫秒数';
          isValid = false;
        } else {
          authTimeoutError.value = '';
        }

        // 验证模拟用户MAC地址头
        if (formData.vmac_head && !validateMacHead(formData.vmac_head)) {
          vmacHeadError.value = '格式不正确，请输入形如00-07-74-XX的格式';
          isValid = false;
        } else {
          vmacHeadError.value = '';
        }

        // 验证嗅探web服务器地址
        if (
          formData.sniffer_addr &&
          !validateIpAddress(formData.sniffer_addr)
        ) {
          snifferAddrError.value = 'IP地址格式不正确，请重新输入';
          isValid = false;
        } else {
          snifferAddrError.value = '';
        }

        return isValid;
      };

      const submitData = async () => {
        try {
          if (!hasPermission('dialAccessSubmit')) {
            Message.error({
              content: '您没有权限',
              duration: 5000,
            });
            return;
          }

          // 在提交前验证所有字段
          if (!validateAllFields()) {
            Message.error({
              content: '请修正表单中的错误再提交',
              duration: 5000,
            });
            return;
          }

          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'oeclient_setting',
              sniffer_addr: formData.sniffer_addr,
              vmac_head: formData.vmac_head,
              neg_trynum: formData.neg_trynum,
              auth_timeout: formData.auth_timeout,
              out_vlan: formData.out_vlan,
              neg_timeout: formData.neg_timeout,
              auth_trynum: formData.auth_trynum,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            Message.success(response.data.data.result);
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Error setting data:', error);
        }
      };

      return {
        handleNotification4,
        handleNotification1: submitData, // 更新按钮点击事件处理函数
        handleNotification2,
        formData,
        hasPermission,
        // 添加错误状态到返回值
        negTrynumError,
        negTimeoutError,
        authTrynumError,
        authTimeoutError,
        vmacHeadError,
        snifferAddrError,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .hint {
    flex: 1; /* 占据剩余空间 */
    text-align: left;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }

  /* 为上传按钮添加居中样式 */
  .upload {
    display: block;
    margin: 0 auto;
  }

  /* 为按钮添加居中样式 */
  .button {
    text-align: center;
  }

  .arco-input-wrapper {
    margin: 10px 0 !important;
  }

  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>
