<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="client_server_addr" class="uniform-form-item">
          <template #label>
            <span class="form-label">服务器地址：</span>
          </template>
          <a-tooltip
            content=" 格式：*******，不填或0.0.0.0则本页面的其它项也无效
            "
            position="tl"
            background-color="#3491FA"
          >
            <a-input v-model="formData.client_server_addr" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="client_server_service" class="uniform-form-item">
          <template #label>
            <span class="form-label"> 服务名：</span>
          </template>
          <a-tooltip
            content=" 不填缺省为internet
            "
            position="tl"
            background-color="#3491FA"
          >
            <a-input v-model="formData.client_server_service" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="client_server_chk_alive" class="uniform-form-item">
          <template #label>
            <span class="form-label"> 心跳包检查：</span>
          </template>
          <a-select
            :style="{ width: '100px' }"
            v-model="formData.client_server_chk_alive"
          >
            <a-option value="disable">否</a-option>
            <a-option value="enable">是</a-option>
          </a-select>
        </a-form-item>
      </a-col> </a-row
    ><a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="client_server_chk_zone" class="uniform-form-item">
          <template #label>
            <span class="form-label"> 认证域检查：</span>
          </template>
          <a-select
            :style="{ width: '100px' }"
            v-model="formData.client_server_chk_zone"
          >
            <a-option value="disable">否</a-option>
            <a-option value="enable">是</a-option>
          </a-select>
        </a-form-item>
      </a-col> </a-row
    ><a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="client_server_rm_zone" class="uniform-form-item">
          <template #label>
            <span class="form-label"> 删除认证域：</span>
          </template>
          <a-select
            :style="{ width: '100px' }"
            v-model="formData.client_server_rm_zone"
          >
            <a-option value="disable">否</a-option>
            <a-option value="enable">是</a-option>
          </a-select>
        </a-form-item>
      </a-col> </a-row
    ><a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item
          field="client_server_allow_nat_auth"
          class="uniform-form-item"
        >
          <template #label>
            <span class="form-label"> 允许NAT后认证：</span>
          </template>
          <a-select
            :style="{ width: '100px' }"
            v-model="formData.client_server_allow_nat_auth"
          >
            <a-option value="disable">否</a-option>
            <a-option value="enable">是</a-option>
          </a-select>
        </a-form-item>
      </a-col> </a-row
    ><a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item
          field="client_server_use_zone_srv"
          class="uniform-form-item"
        >
          <template #label>
            <span class="form-label"> 设置服务器名为认证域：</span>
          </template>
          <a-select
            :style="{ width: '100px' }"
            v-model="formData.client_server_use_zone_srv"
          >
            <a-option value="disable">否</a-option>
            <a-option value="enable">是</a-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <a-button
      :disabled="!hasPermission('evrrpSubmit')"
      type="primary"
      style="margin-top: 2%"
      @click="saveAction"
    >
      <template #icon>
        <icon-check />
      </template>
      <template #default>提交</template>
    </a-button>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      const formData = reactive({
        work: '',
        client_server_addr: '',
        client_server_chk_zone: '',
        client_server_chk_alive: '',
        client_server_service: '',
        client_server_rm_zone: '',
        client_server_allow_nat_auth: '',
        client_server_use_zone_srv: '',
      });

      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_other.lua',
            new URLSearchParams({ act: 'client_server' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );
          if (response.data.code === 200) {
            formData.client_server_addr =
              response.data.data.client_server_addr || '';
            formData.client_server_chk_zone =
              response.data.data.client_server_chk_zone || '';
            formData.client_server_service =
              response.data.data.client_server_service || '';
            formData.client_server_allow_nat_auth =
              response.data.data.client_server_allow_nat_auth || '';
            formData.client_server_rm_zone =
              response.data.data.client_server_rm_zone || '';
            formData.client_server_chk_alive =
              response.data.data.client_server_chk_alive || '';
            formData.client_server_use_zone_srv =
              response.data.data.client_server_use_zone_srv || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        try {
          if (!hasPermission('evrrpSubmit')) {
            Message.error('您没有权限');
            return;
          }

          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'mod',
              local_ip: String(formData.local_ip),
              peer_ip: String(formData.peer_ip),
              client_server_allow_nat_auth: String(
                formData.client_server_allow_nat_auth
              ),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        saveAction,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .form-row {
    margin-bottom: 16px;
  }

  .uniform-form-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .form-label {
    display: inline-block;
    margin-bottom: 2px;
    white-space: normal;
    word-break: break-word;
    width: 100%;
  }

  .uniform-form-item >>> .arco-form-item-wrapper {
    width: 100%;
  }

  /* 中屏幕及以上时改为水平布局 */
  @media (min-width: 768px) {
    .uniform-form-item {
      flex-direction: row;
      align-items: center;
    }

    .form-label {
      width: 160px;
      text-align: right;
      padding-right: 1px;
      margin-bottom: 0;
    }
  }

  /* 大屏幕时调整标签宽度 */
  @media (min-width: 1200px) {
    .form-label {
      width: 180px;
    }
  }

  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>
