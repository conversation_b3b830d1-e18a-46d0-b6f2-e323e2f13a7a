<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-space>
          <!-- 新建按钮 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            添加时间段
          </a-button>
        </a-space>
      </a-col>
      <!-- 右侧按钮 -->
      <a-col
        :span="12"
        style="display: flex; align-items: center; justify-content: end"
      >
        <!-- 刷新按钮 -->
        <a-tooltip content="刷新">
          <div class="action-icon" @click="handleRefresh">
            <icon-refresh size="18" />
          </div>
        </a-tooltip>
      </a-col>
    </a-row>

    <!-- 列表 -->
    <a-table
      :columns="columns"
      :data="timeRanges"
      style="margin-top: 20px"
      :pagination="false"
    >
      <template #operation="{ index }">
        <a-button status="danger" @click="deleteRow(index)">
          <template #icon>
            <icon-delete />
          </template>
          <template #default>删除</template>
        </a-button>
      </template>
    </a-table>
  </a-card>

  <a-modal
    v-model:visible="isModalVisible"
    title="添加时间段"
    :width="700"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-form :model="formData" :rules="rules" ref="formRef">
      <a-form-item label="开始时间(hh:mm)" field="start_time">
        <a-time-picker
          v-model="formData.start_time"
          format="HH:mm"
          style="width: 200px"
        />
      </a-form-item>
      <a-form-item label="结束时间(hh:mm)" field="end_time">
        <a-time-picker
          v-model="formData.end_time"
          format="HH:mm"
          style="width: 200px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns = [
        {
          title: '开始时间(hh:mm)',
          dataIndex: 'start_time',
        },
        {
          title: '结束时间(hh:mm)',
          dataIndex: 'end_time',
        },
        {
          title: '操作',
          dataIndex: 'operation',
          slotName: 'operation',
        },
      ];

      // 时间段数据
      const timeRanges = ref<{ start_time: string; end_time: string }[]>([]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);

      // 表单数据
      const formData = reactive({
        start_time: '',
        end_time: '',
      });

      // 表单规则
      const rules = {
        start_time: [{ required: true, message: '开始时间不能为空' }],
        end_time: [
          { required: true, message: '结束时间不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!formData.start_time || !formData.end_time) {
                callback();
                return;
              }

              const startTimeParts = formData.start_time.split(':').map(Number);
              const endTimeParts = formData.end_time.split(':').map(Number);

              if (
                startTimeParts[0] > endTimeParts[0] ||
                (startTimeParts[0] === endTimeParts[0] &&
                  startTimeParts[1] >= endTimeParts[1])
              ) {
                callback('结束时间必须大于开始时间');
              } else {
                callback();
              }
            },
          },
        ],
      };

      const formRef = ref();

      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_spec_bw_ctrl.lua',
            new URLSearchParams({ act: 'chk_time' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          console.log('接口返回数据:', response.data);

          if (response.data.code === 200) {
            // 如果后端返回了时间段数据，可以在这里设置
            if (response.data.data && Array.isArray(response.data.data)) {
              timeRanges.value = response.data.data;
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 刷新数据
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 显示模态框
      const showModal = () => {
        formData.start_time = '06:30';
        formData.end_time = '11:00';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleBeforeOk = (done) => {
        // 应用rules中的验证逻辑，但不使用validate方法

        // 验证开始时间是否为空
        if (!formData.start_time) {
          Message.error('开始时间不能为空');
          done(false);
          return;
        }

        // 验证结束时间是否为空
        if (!formData.end_time) {
          Message.error('结束时间不能为空');
          done(false);
          return;
        }

        // 验证结束时间是否大于开始时间
        const startTimeParts = formData.start_time.split(':').map(Number);
        const endTimeParts = formData.end_time.split(':').map(Number);

        if (
          startTimeParts[0] > endTimeParts[0] ||
          (startTimeParts[0] === endTimeParts[0] &&
            startTimeParts[1] >= endTimeParts[1])
        ) {
          Message.error('结束时间必须大于开始时间');
          done(false);
          return;
        }

        // 验证通过，添加到列表
        timeRanges.value.push({
          start_time: formData.start_time,
          end_time: formData.end_time,
        });

        Message.success('添加成功');
        done(true); // 允许模态框关闭
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      // 删除行
      const deleteRow = (index: number) => {
        timeRanges.value.splice(index, 1);
        Message.success('删除成功');
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        timeRanges,
        handleRefresh,
        isRefreshing,
        formData,
        rules,
        formRef,
        isModalVisible,
        showModal,
        handleBeforeOk,
        handleCancel,
        deleteRow,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
