<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="24">
        <a-space>
          <!-- 新建按钮 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>

          <a-button @click="handleDelete">
            <template #icon>
              <icon-delete />
            </template>
            删除
          </a-button>

          <a-button @click="handleImport">
            <template #icon>
              <icon-import />
            </template>
            导入
          </a-button>

          <a-button @click="handleExportResult">
            <template #icon>
              <icon-export />
            </template>
            导出结果
          </a-button>

          <a-button @click="handleExportAll">
            <template #icon>
              <icon-export />
            </template>
            导出全部热点
          </a-button>

          <!-- 刷新按钮 -->
          <a-button @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            激活
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <!-- 列表 -->
    <a-table
      row-key="access"
      :columns="columns"
      :data="data"
      style="margin-top: 20px"
      :pagination="false"
      :row-selection="rowSelection"
    >
      <template #isuse="{ record }">
        <a-radio-group v-model="record.isuse">
          <a-radio value="1">是</a-radio>
          <a-radio value="0">否</a-radio>
        </a-radio-group>
      </template>
    </a-table>
  </a-card>

  <a-modal
    v-model:visible="isModalVisible"
    title="添加热点"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
    :width="700"
  >
    <a-form :model="formData" :rules="rules" ref="formRef">
      <a-form-item label="接入点名" field="access">
        <a-input
          v-model="formData.access"
          style="width: 200px"
          placeholder="请输入接入点名"
        />
      </a-form-item>
      <a-form-item label="端口" field="port">
        <a-input
          v-model="formData.port"
          style="width: 200px"
          placeholder="请输入端口号"
        />
      </a-form-item>
      <a-form-item label="外层vlan" field="pvlan">
        <a-input
          v-model="formData.pvlan"
          style="width: 200px"
          placeholder="请输入外层vlan"
        />
      </a-form-item>
      <a-form-item label="内层vlan" field="cvlan">
        <a-input
          v-model="formData.cvlan"
          style="width: 200px"
          placeholder="请输入内层vlan"
        />
      </a-form-item>
      <a-form-item label="应用" field="isuse">
        <a-radio-group v-model="formData.isuse">
          <a-radio value="1">是</a-radio>
          <a-radio value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    watch,
    computed,
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidVlanId } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns = [
        {
          title: '接入点名',
          dataIndex: 'access',
        },
        {
          title: '端口',
          dataIndex: 'port',
        },
        {
          title: '外层vlan',
          dataIndex: 'pvlan',
        },
        {
          title: '内层vlan',
          dataIndex: 'cvlan',
        },
        {
          title: '应用',
          dataIndex: 'isuse',
          slotName: 'isuse',
        },
      ];

      const data = ref<
        {
          access: string;
          port: string;
          pvlan: string;
          cvlan: string;
          isuse: string;
        }[]
      >([]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);

      // 表格选择
      const selectedRowKeys = ref<string[]>([]);
      const rowSelection = reactive({
        type: 'checkbox' as const,
        showCheckedAll: true,
        onChange: (rowKeys: string[]) => {
          selectedRowKeys.value = rowKeys;
        },
      });

      // 表单数据
      const formData = reactive({
        access: '', // 接入点名
        port: '', // 端口
        pvlan: '', // 外层vlan
        cvlan: '', // 内层vlan
        isuse: '', // 是否应用
      });

      // 表单规则
      const rules = {
        access: [{ required: true, message: '接入点名不能为空' }],
        port: [{ required: true, message: '端口不能为空' }],
        pvlan: [
          { required: true, message: '外层vlan不能为空' },
          {
            validator: (value: number, callback: (error?: string) => void) => {
              if (!isValidVlanId(value)) {
                callback('vlan的范围：0~4095');
              } else {
                callback();
              }
            },
          },
        ],
        cvlan: [
          { required: true, message: '内层vlan不能为空' },
          {
            validator: (value: number, callback: (error?: string) => void) => {
              if (!isValidVlanId(value)) {
                callback('vlan的范围：0~4095');
              } else {
                callback();
              }
            },
          },
        ],
      };

      const formRef = ref();

      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/ap_postion.lua',
            new URLSearchParams({ act: 'get' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              data.value = response.data.data;
            } else {
              // 如果API没有返回数据，使用测试数据
              data.value = [
                {
                  access: 't_1',
                  port: '22334',
                  pvlan: '12',
                  cvlan: '56',
                  isuse: '1',
                },
                {
                  access: '1',
                  port: '1',
                  pvlan: '1',
                  cvlan: '12',
                  isuse: '0',
                },
              ];
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
            // 使用测试数据
            data.value = [
              {
                access: 't_1',
                port: '22334',
                pvlan: '12',
                cvlan: '56',
                isuse: '1',
              },
              {
                access: '1',
                port: '1',
                pvlan: '1',
                cvlan: '12',
                isuse: '0',
              },
            ];
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
          // 使用测试数据
          data.value = [
            {
              access: 't_1',
              port: '22334',
              pvlan: '12',
              cvlan: '56',
              isuse: '1',
            },
            {
              access: '1',
              port: '1',
              pvlan: '1',
              cvlan: '12',
              isuse: '0',
            },
          ];
        }
      };

      // 激活配置
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 删除选中项
      const handleDelete = () => {
        if (selectedRowKeys.value.length === 0) {
          Message.warning('请选择要删除的项');
          return;
        }

        // 实际应调用删除API
        // 这里仅做前端删除
        data.value = data.value.filter(
          (item) => !selectedRowKeys.value.includes(item.access)
        );
        selectedRowKeys.value = [];
        Message.success('删除成功');
      };

      // 导入
      const handleImport = () => {
        Message.info('导入功能暂未实现');
      };

      // 导出结果
      const handleExportResult = () => {
        Message.info('导出结果功能暂未实现');
      };

      // 导出全部热点
      const handleExportAll = () => {
        Message.info('导出全部热点功能暂未实现');
      };

      // 显示模态框
      const showModal = () => {
        // 重置表单
        formData.access = '';
        formData.port = '';
        formData.pvlan = '';
        formData.cvlan = '';
        formData.isuse = '1';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleBeforeOk = (done) => {
        formRef.value.validate().then((errors) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 添加到列表
          data.value.push({
            access: formData.access,
            port: formData.port,
            pvlan: formData.pvlan,
            cvlan: formData.cvlan,
            isuse: formData.isuse,
          });

          Message.success('添加成功');
          done(true); // 允许模态框关闭
        });
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        handleRefresh,
        isRefreshing,
        formData,
        rules,
        formRef,
        isModalVisible,
        showModal,
        handleBeforeOk,
        handleCancel,
        handleDelete,
        handleImport,
        handleExportResult,
        handleExportAll,
        rowSelection,
        selectedRowKeys,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
