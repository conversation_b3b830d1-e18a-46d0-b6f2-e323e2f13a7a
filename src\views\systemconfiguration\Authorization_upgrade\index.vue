<!-- 升级管理组件 -->
<template>
  <div class="container">
    <Breadcrumb
      :items="['menu.system-configuration', 'menu.Authorization_upgrade']"
    />

    <a-card title="升级管理">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-row :gutter="20" :style="{ marginBottom: '20px' }">
          <a-col :span="8">
            <a-card
              title="软件升级"
              :bordered="false"
              :style="{ width: '100%' }"
            >
              <a-alert type="info" banner closable>
                <template #icon> </template>
                <template #title>
                  <span style="font-size: 15px">
                    注意：升级后，需要重启设备。!!</span
                  >
                </template>
                <template #extra> </template>
              </a-alert>
              <br />
              <div class="session">
                <div class="form-item">
                  <span>请导入升级文件：</span>
                  <a-tooltip
                    content="注意：导入文件只支持(*.bin)"
                    position="tr"
                    background-color="#3491FA"
                  >
                    <a-input
                      class="input-field"
                      readonly
                      :style="{ width: '100%' }"
                      :model-value="upgradeFileName"
                    />
                  </a-tooltip>
                </div>
                <br />               
                <a-upload
                  :action="'/lua/upgrade_software.lua'"
                  class="upload"
                  :auto-upload="false"
                  :limit="1"
                  :show-file-list="false"
                  :file-list="upgradeFileList"
                  accept=".bin"
                  ref="upgradeRef"
                  @change="onUpgradeFileChange"
                  @before-upload="beforeUpgradeUpload"
                  @success="onUpgradeSuccess"
                  @error="onUploadError"
                >
                  <template #upload-button>
                    <a-space>
                      <a-button :disabled="!hasPermission('upgradeSoftware')">选择文件</a-button>
                      <a-button type="primary" @click="submitUpgrade" :disabled="!(upgradeFileList?.length)">
                        <template #icon>
                          <icon-upload />
                        </template>
                        上传
                      </a-button>
                    </a-space>
                  </template>
                </a-upload>
              </div>
            </a-card>
          </a-col>

          <a-col :span="8">
            <a-card
              title="授权升级"
              :bordered="false"
              :style="{ width: '100%' }"
            >
              <a-alert type="info" banner closable>
                <template #icon> </template>
                <template #title>
                  <span style="font-size: 15px">
                    注意：升级后，需要重启设备。!!</span
                  >
                </template>
                <template #extra> </template>
              </a-alert>
              <br />
              <div class="session">
                <div class="form-item">
                  <span>请导入license文件：</span>
                  <a-tooltip
                    content="	注意：导入的文件名不能有后缀"
                    position="tr"
                    background-color="#3491FA"
                  >
                    <a-input
                      class="input-field"
                      readonly
                      :style="{ width: '100%' }"
                      :model-value="licenseFileName"

                    />
                  </a-tooltip>
                </div>
                <br />
                <a-upload
                  :action="'/lua/upgrade_license.lua'"
                  class="upload"
                  :auto-upload="false"
                  :limit="1"
                  :file-list="licenseFileList"
                  :show-file-list="false"
                  ref="licenseRef"
                  @change="onLicenseFileChange"
                  @before-upload="beforeLicenseUpload"
                  @success="onLicenseSuccess"
                  @error="onUploadError"
                >
                  <template #upload-button>
                    <a-space>
                      <a-button :disabled="!hasPermission('upgradeLicense')">选择文件</a-button>
                      <a-button type="primary" @click="submitLicense" :disabled="!(licenseFileList?.length)">
                        <template #icon>
                          <icon-upload />
                        </template>
                        上传
                      </a-button>
                    </a-space>
                  </template>
                </a-upload>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import usePermission from '@/hooks/permission';

  interface ApiResponse {
    code: number;
    result?: string;
    err?: string;
  }

  const { hasPermission } = usePermission();

  export default defineComponent({
    setup() {
      const upgradeFileName = ref('');
      const licenseFileName = ref('');
      const upgradeFileList = ref<any[]>([]);
      const licenseFileList = ref<any[]>([]);
      
      const licenseRef = ref();
      const submitLicense = (e) => {
        if (!hasPermission('upgradeLicense')) {
          Message.error({
            content: '您没有权限',
            duration: 5000,
          });
          return false;
        }
        e.stopPropagation();
        licenseRef.value.submit();
      };
      
      const upgradeRef = ref();
      const submitUpgrade = (e) => {
        if (!hasPermission('upgradeSoftware')) {
          Message.error({
            content: '您没有权限',
            duration: 5000,
          });
          return false;
        } 
        e.stopPropagation();
        upgradeRef.value.submit();
      };



      const beforeUpgradeUpload = async (file: File) => {
        if (!hasPermission('upgradeSubmit')) {
          Message.error({
            content: '您没有权限',
            duration: 5000,
          });
          return false;
        }
        return true;
      };

      const beforeLicenseUpload = async (file: File) => {
        if (!hasPermission('upgradeSubmit')) {
          Message.error({
            content: '您没有权限',
            duration: 5000,
          });
          return false;
        }
        return true;
      };

      const onUpgradeFileChange = (fileList) => {
        upgradeFileList.value = [],
        upgradeFileName.value = '';
        
        if (fileList && fileList.length > 0 && fileList[0].status === "init") {
          const file = fileList[0].file;
          upgradeFileName.value = file.name;
          upgradeFileList.value = fileList;
        }
      };

      const onLicenseFileChange = (fileList) => {
        licenseFileName.value = '';
        licenseFileList.value = [];
        
        if (fileList && fileList.length > 0 && fileList[0].status === "init") {
          const file = fileList[0].file;
          licenseFileName.value = file.name;
          licenseFileList.value = fileList;
        }
      };

      const onUpgradeSuccess = (response: any) => {
        if (response.data.code === 200) {
          Message.success(response.data.success);
          upgradeFileList.value = [];
          upgradeFileName.value = '';
        } else {
          Message.error(response.data.err);
        }
      };

      const onLicenseSuccess = (response: any) => {
        if (response.data.code === 200) {
          Message.success(response.data.success);
          licenseFileList.value = [];
          licenseFileName.value = '';
        } else {
          Message.error(response.data.err);
        }
      };

      const onUploadError = () => {
        upgradeFileList.value = [];
        upgradeFileName.value = '';
        licenseFileList.value = [];
        licenseFileName.value = '';
        
        Message.error('上传失败，请稍后重试');
      };

      return {
        upgradeFileName,
        licenseFileName,
        upgradeFileList,
        licenseFileList,
        onUpgradeFileChange,
        onLicenseFileChange,
        beforeUpgradeUpload,
        beforeLicenseUpload,
        onUpgradeSuccess,
        onLicenseSuccess,
        onUploadError,
        hasPermission,
        licenseRef,
        submitLicense,
        submitUpgrade,
        upgradeRef
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .hint {
    flex: 1; /* 占据剩余空间 */
    text-align: left;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }

  /* 为上传按钮添加居中样式 */
  .upload {
    display: block;
    margin: 0 auto;
  }

  /* 为按钮添加居中样式 */
  .button {
    text-align: center;
  }
  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .arco-input-wrapper {
    margin: 10px 0 !important;
  }
</style>
