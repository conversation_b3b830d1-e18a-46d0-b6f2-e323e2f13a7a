<template>

  <a-card class="general-card">

    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-space align="baseline">
          <span class="form-label">工作模式：</span>
          <a-select :style="{ width: '200px' }" v-model="formData.mode">
            <a-option value="route">路由</a-option>
            <a-option value="bridge">桥接</a-option>
          </a-select>
        </a-space>
      </a-col>
    </a-row>

    <div v-show="formData.mode === 'bridge'">
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space align="baseline">
            <span class="form-label">出入口设置：</span>
            <a-space>
              <a-select v-model="formData.output" :style="{ width: '200px' }" :options="optionsGroup" />
              <a-select v-model="formData.input" :style="{ width: '200px' }" :options="optionsGroup" />
            </a-space>
          </a-space>
        </a-col>
      </a-row>

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space align="baseline">
            <span class="form-label">认证端口：</span>
            <a-select v-model="formData.auth" :style="{ width: '200px' }" :options="optionsGroup" />
          </a-space>
        </a-col>
      </a-row>
    </div>


    <a-table :columns="columns" :data="cfgData" column-resizable :pagination="false" style="margin-top: 20px"
      v-show="formData.mode === 'route'">
      <template #port="{ rowIndex }">
        <span>{{ cfgData[rowIndex].port }}</span>
      </template>

      <template #auth="{ rowIndex }">
        <a-select :style="{ width: '100px' }" v-model="cfgData[rowIndex].auth">
          <a-option value="enable">是</a-option>
          <a-option value="disable">否</a-option>
        </a-select>
      </template>
      <template #vlan="{ rowIndex }">
        <a-select :style="{ width: '100px' }" v-model="cfgData[rowIndex].vlan" @change="handleVlanChange(rowIndex)">
          <a-option value="enable">是</a-option>
          <a-option value="disable">否</a-option>
        </a-select>
      </template>
      <template #qinq="{ rowIndex }">
        <a-select :style="{ width: '100px' }" v-model="cfgData[rowIndex].qinq"
          :disabled="cfgData[rowIndex].vlan == 'disable'">
          <a-option value="enable">是</a-option>
          <a-option value="disable">否</a-option>
        </a-select>
      </template>
      <template #vlan_split="{ rowIndex }">
        <a-select :style="{ width: '100px' }" v-model="cfgData[rowIndex].vlan_split">
          <a-option value="enable">是</a-option>
          <a-option value="disable">否</a-option>
        </a-select>
      </template>
      <template #onevlan="{ rowIndex }">
        <a-input v-model="cfgData[rowIndex].onevlan" />
      </template>
    </a-table>

    <a-button id="workModeSubmit" :disabled="!hasPermission('workModeSubmit')" type="primary" style="margin-top: 2%"
      @click="saveConfig">
      <template #icon>
        <icon-check />
      </template>
      <template #default>提交</template>
    </a-button>
  </a-card>

</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed,
    watch
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  export default defineComponent({
    props: {
      active: Boolean
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      const formData = reactive({
        mode: 'route',
        output: '0',
        input: '0',
        auth: '0',
      });
            
      const columns = [
        {
          title: '端口',
          dataIndex: 'port',
          slotName: 'port',
        },
        {
          title: '认证端口',
          dataIndex: 'auth',
          slotName: 'auth',
        },
        {
          title: '支持vlan',
          dataIndex: 'vlan',
          slotName: 'vlan',
        },
        {
          title: '支持qinq',
          dataIndex: 'qinq',
          slotName: 'qinq',
        },
        {
          title: 'vlan剥离',
          dataIndex: 'vlan_split',
          slotName: 'vlan_split',
        },
        {
          title: '上层vlan',
          dataIndex: 'onevlan',
          slotName: 'onevlan',
        }
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];   
       
      interface SelectOption {
        label: string;
        value: string;
      }
      const optionsGroup = ref<SelectOption[]>([]);        
      const cfgData = ref([]);
      const portData = ref([]);
      
      const getPorts = async () => {
        try {
          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({ act: 'get_vm_port' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            
            const formattedData: SelectOption[] = [];    
            const tmpportData: string[] = [];    

            response.data.data.port.forEach((item: string) => {
              formattedData.push({ 
                label: String(item), 
                value: String(item),  
              });   
              
              tmpportData.push(item);
            });
              
            portData.value = tmpportData;
            optionsGroup.value = formattedData;
            
          } else {
            Message.error(response.data.result || '获取端口失败');
          }
        } catch (error) {
          Message.error('获取端口错误');
        }
      };

      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_basic_setting.lua',
            new URLSearchParams({ act: 'work_mode' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          // console.log(response);
          
          if (response.data.code === 200) {
            formData.mode = response.data.data.mode;
            if(response.data.data.mode == "bridge"){
              formData.output = response.data.data.output;
              formData.input = response.data.data.input;
              formData.auth = response.data.data.bridge_auth;              
            }else{
              const formattedData: Record<string, any>[] = [];  
              let data: Record<string, any> = [];

              for(let i=0; i<portData.value.length; i++){
                
                let auth = 'disable';
                let vlan = 'disable';
                let qinq = 'disable';
                let vlan_split = 'disable';
                let onevlan = '';
                
                response.data.data.auth.forEach((item) => {
                  if(item.auth_value == portData.value[i]){
                    auth = "enable";
                  }                
                });
                response.data.data.vlan.forEach((item) => {
                  if(item.vlan_port == portData.value[i]){
                    vlan = item.vlan_value;
                  }                
                });
                response.data.data.qinq.forEach((item) => {
                  if(item.qinq_port == portData.value[i]){
                    qinq = item.qinq_value;
                  }                
                });
                if (Array.isArray(response?.data?.data?.vlan_split)) {
                  response.data.data.vlan_split.forEach((item) => {
                    if(item.split_vlan_port == portData.value[i]){
                      vlan_split = item.split_vlan_value;
                    }                
                  }); 
                }
                if (Array.isArray(response?.data?.data?.onevlan)) {
                  response.data.data.onevlan.forEach((item) => {
                    if(item.add_one_vlan_port == portData.value[i]){
                      onevlan = item.add_one_vlan_value;
                    }                
                  });  
                }
                
                data = {
                  port: portData.value[i],
                  auth: auth,
                  vlan: vlan,
                  qinq: qinq,
                  vlan_split: vlan_split,
                  onevlan: onevlan,
                } ;      
                
                formattedData.push(data);        
              }
              
              cfgData.value = formattedData;  
            }
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveConfig = async () => {
        try {
          if (!hasPermission('workModeSubmit')) {
            Message.error('您没有权限');
            return;
          }
          let auth = [];
          let vlan = [];
          let qinq = [];
          let split_vlan = [];
          let add_one_vlan = [];
          
          if(formData.mode == "route"){
            for(let i=0; i<cfgData.value.length; i++){
              auth.push(cfgData.value[i].auth);
              vlan.push(cfgData.value[i].vlan);
              qinq.push(cfgData.value[i].qinq);
              split_vlan.push(cfgData.value[i].vlan_split);
              add_one_vlan.push(cfgData.value[i].onevlan);
            }             
          }
          
          const response = await axios.post(
            '/lua/set_cfg_basic_setting.lua',
            new URLSearchParams({
              act: 'work_mode',
              mode: formData.mode,
              auth: JSON.stringify(auth),
              vlan: JSON.stringify(vlan),
              qinq: JSON.stringify(qinq),
              split_vlan: JSON.stringify(split_vlan),
              add_one_vlan: JSON.stringify(add_one_vlan),
              out_port: formData.output,
              in_port: formData.input,
              bridge_auth: formData.auth
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };
      
      const handleVlanChange = (rowIndex) => {
        if (cfgData.value[rowIndex].vlan === 'disable') {
          cfgData.value[rowIndex].qinq = 'disable';
        }
      };

      watch(() => props.active, (newVal) => {
        if (newVal) {
          getPorts();
          fetchData();
        }
      });
      
      onMounted(() => {
        if (props.active) {
          getPorts();
          fetchData();
        }        
      });

      return {
        columns,
        cfgData,
        portData,
        formData,
        optionsGroup,
        saveConfig,
        hasPermission,
        handleVlanChange,
      };
    },
  });
</script>

<style scoped>

  .general-card {
    width: 100%;
  }
  .form-label {
    display: inline-block;
    width: 90px; /* 调整这个值 */
    text-align: right;
    margin-right: 8px;
  }
</style>