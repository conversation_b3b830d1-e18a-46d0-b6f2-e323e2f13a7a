import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const SYSTEMFIGIRATION: AppRouteRecordRaw = {
  path: '/systemconfiguration',
  name: 'systemconfiguration',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.system-configuration',
    permissions: ['systemConfig'],
    icon: 'icon-settings',
    requiresAuth: true,
    order: 3,
  },
  children: [
    {
      // 基本设置
      path: 'basic-settings',
      name: 'basic-settings',
      component: () =>
        import('@/views/systemconfiguration/basic-settings/index.vue'),
      meta: {
        locale: 'menu.basic-settings',
        icon: 'icon-export',
        permissions: ['BasicSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 端口设置
      path: 'port-settings',
      name: 'port-settings',
      component: () =>
        import('@/views/systemconfiguration/port-settings/index.vue'),
      meta: {
        locale: 'menu.port-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 路由设置
      path: 'route-settings',
      name: 'route-settings',
      component: () =>
        import('@/views/systemconfiguration/route-settings/index.vue'),
      meta: {
        locale: 'menu.route-settings',
        icon: 'icon-export',
        permissions: ['RouteSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },

    {
      // DNS设置
      path: 'dns-settings',
      name: 'dns-settings',
      component: () =>
        import('@/views/systemconfiguration/port-settings/index.vue'),
      meta: {
        locale: 'menu.dns-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 地址控制
      path: 'address-settings',
      name: 'address-settings',
      component: () =>
        import('@/views/systemconfiguration/port-settings/index.vue'),
      meta: {
        locale: 'menu.address-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // web定向
      path: 'web-settings',
      name: 'web-settings',
      component: () =>
        import('@/views/systemconfiguration/port-settings/index.vue'),
      meta: {
        locale: 'menu.web-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      //   web转pppoe
      path: 'web-to-pppoe-settings',
      name: 'web-to-pppoe-settings',
      component: () =>
        import('@/views/systemconfiguration/port-settings/index.vue'),
      meta: {
        locale: 'menu.web-to-pppoe-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      //  pppoe server
      path: 'pppoeserver-settings',
      name: 'pppoeserver-settings',
      component: () =>
        import('@/views/systemconfiguration/pppoeserver-settings/index.vue'),
      meta: {
        locale: 'menu.pppoeserver-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // dhcp服务器
      path: 'dhcp-settings',
      name: 'dhcp-settings',
      component: () =>
        import('@/views/systemconfiguration/dhcp-settings/index.vue'),
      meta: {
        locale: 'menu.dhcp-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // IPV6配置
      path: 'ipv6-settings',
      name: 'ipv6-settings',
      component: () =>
        import('@/views/systemconfiguration/ipv6-settings/index.vue'),
      meta: {
        locale: 'menu.ipv6-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 用户MAC地址
      path: 'usermac-settings',
      name: 'usermac-settings',
      component: () =>
        import('@/views/systemconfiguration/usermac-settings/index.vue'),
      meta: {
        locale: 'menu.usermac-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 速率控制
      path: 'rate-settings',
      name: 'rate-settings',
      component: () =>
        import('@/views/systemconfiguration/rate-settings/index.vue'),
      meta: {
        locale: 'menu.rate-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // qinq绑mac
      path: 'qinq-settings',
      name: 'qinq-settings',
      component: () =>
        import('@/views/systemconfiguration/qinq-settings/index.vue'),
      meta: {
        locale: 'menu.qinq-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 5G网络接入
      path: '5g-settings',
      name: '5g-settings',
      component: () =>
        import('@/views/systemconfiguration/5g-settings/index.vue'),
      meta: {
        locale: 'menu.5g-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 特殊设置
      path: 'special-settings',
      name: 'special-settings',
      component: () =>
        import('@/views/systemconfiguration/special-settings/index.vue'),
      meta: {
        locale: 'menu.special-settings',
        icon: 'icon-export',
        permissions: ['PortSettings'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
  ],
};

export default SYSTEMFIGIRATION;
