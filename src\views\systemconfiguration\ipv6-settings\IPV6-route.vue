<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- IPv6协议栈开关配置 -->
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 4 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="only_forward" class="uniform-form-item">
        <template #label>
          <span class="form-label"
            >Do not authenticate forwarding IPV6 data:
          </span>
        </template>
        <a-select :style="{ width: '100px' }" v-model="formData.only_forward">
          <a-option value="disable">No</a-option>
          <a-option value="enable">Yes</a-option>
        </a-select>
      </a-form-item>

      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>Submit</template>
        </a-button>
        <a-button style="margin-left: 10px" @click="resetAction">
          <template #icon>
            <icon-refresh />
          </template>
          <template #default>Reset</template>
        </a-button>
      </a-form-item>
    </a-form>

    <a-divider />

    <!-- 表格标题和新建按钮 -->
    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <h4 style="margin: 0; color: #1d2129">IPv6 Route</h4>
      </a-col>
      <a-col :span="12" style="text-align: right">
        <a-button type="primary" @click="showAddModal">
          <template #icon>
            <icon-plus />
          </template>
          Add
        </a-button>
      </a-col>
    </a-row>

    <!-- IPv6路由配置表格 -->
    <a-table
      row-key="index"
      :columns="routeColumns"
      :data="routeTableData"
      :pagination="false"
      :bordered="true"
      :loading="isRouteLoading"
    >
      <template #ip="{ record }">
        <span>{{ record.ip || '-' }}</span>
      </template>

      <template #gw="{ record }">
        <span>{{ record.gw || '-' }}</span>
      </template>

      <template #desc="{ record }">
        <span>{{ record.desc || '-' }}</span>
      </template>

      <template #operate="{ record, rowIndex }">
        <a-space>
          <a-button type="primary" size="small" @click="editRecord(record)">
            <template #icon>
              <icon-edit />
            </template>
            Edit
          </a-button>
          <a-button
            type="primary"
            status="danger"
            size="small"
            @click="deleteRecord(record, rowIndex)"
          >
            <template #icon>
              <icon-delete />
            </template>
            Delete
          </a-button>
        </a-space>
      </template>
    </a-table>
  </a-card>

  <!-- 新建IPv6路由模态框 -->
  <a-modal
    v-model:visible="isAddModalVisible"
    title="Add IPv6 Route"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleAddConfirm"
    @cancel="handleAddCancel"
    :width="600"
  >
    <a-form :model="addFormData" :rules="addRules" ref="addFormRef">
      <a-form-item label="IPv6 Address" field="ip">
        <a-input
          v-model="addFormData.ip"
          placeholder="请输入IPv6地址"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="Gateway" field="gw">
        <a-input
          v-model="addFormData.gw"
          placeholder="请输入网关"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="Description" field="desc">
        <a-input
          v-model="addFormData.desc"
          placeholder="请输入描述"
          style="width: 300px"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 编辑IPv6路由模态框 -->
  <a-modal
    v-model:visible="isEditModalVisible"
    title="Edit IPv6 Route"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleEditConfirm"
    @cancel="handleEditCancel"
    :width="600"
  >
    <a-form :model="editFormData" :rules="editRules" ref="editFormRef">
      <a-form-item label="IPv6 Address" field="ip">
        <a-input
          v-model="editFormData.ip"
          placeholder="请输入IPv6地址"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="Gateway" field="gw">
        <a-input
          v-model="editFormData.gw"
          placeholder="请输入网关"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="Description" field="desc">
        <a-input
          v-model="editFormData.desc"
          placeholder="请输入描述"
          style="width: 300px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface RouteIPv6Item {
    index: number;
    ip: string;
    gw: string;
    desc: string;
    isEditing?: boolean;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      // IPv6路由配置表单数据
      const formData = reactive({
        only_forward: 'disable',
      });

      // 表单验证规则
      const rules = {};

      // IPv6路由表格列定义
      const routeColumns = [
        {
          title: 'IPv6 Address',
          dataIndex: 'ip',
          slotName: 'ip',
          align: 'center' as const,
          width: 300,
        },
        {
          title: 'Gateway',
          dataIndex: 'gw',
          slotName: 'gw',
          align: 'center' as const,
          width: 200,
        },
        {
          title: 'Description',
          dataIndex: 'desc',
          slotName: 'desc',
          align: 'center' as const,
          width: 200,
        },
        {
          title: 'Operate',
          dataIndex: 'operate',
          slotName: 'operate',
          align: 'center' as const,
          width: 200,
        },
      ];

      const routeTableData = ref<RouteIPv6Item[]>([]);
      const isRouteLoading = ref(false);
      const isAddModalVisible = ref(false);
      const isEditModalVisible = ref(false);
      const currentEditRecord = ref<RouteIPv6Item | null>(null);

      // 新建表单数据
      const addFormData = reactive({
        ip: '',
        gw: '',
        desc: '',
      });

      // 编辑表单数据
      const editFormData = reactive({
        ip: '',
        gw: '',
        desc: '',
      });

      // 新建表单规则
      const addRules = {
        ip: [{ required: true, message: 'IPv6地址不能为空' }],
        gw: [{ required: false, message: '网关可以为空' }],
        desc: [{ required: false, message: '描述可以为空' }],
      };

      // 编辑表单规则
      const editRules = {
        ip: [{ required: true, message: 'IPv6地址不能为空' }],
        gw: [{ required: false, message: '网关可以为空' }],
        desc: [{ required: false, message: '描述可以为空' }],
      };

      const addFormRef = ref();
      const editFormRef = ref();

      // 获取IPv6路由配置数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_ipv6_setting.lua',
            new URLSearchParams({ act: 'route' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            const data = response.data.data;
            formData.only_forward = data.only_forward || 'disable';

            // 处理地址数组
            if (data.addr && Array.isArray(data.addr)) {
              routeTableData.value = data.addr.map(
                (item: any, index: number) => ({
                  index: index + 1,
                  ip: item.ip || '',
                  gw: item.gw || '',
                  desc: item.desc || '',
                  isEditing: false,
                })
              );
            } else {
              routeTableData.value = [];
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 保存IPv6路由配置
      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_ipv6_settings.lua',
            new URLSearchParams({
              act: 'route',
              act_type: 'mod',
              only_forward: formData.only_forward,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      // 重置表单
      const resetAction = () => {
        formData.only_forward = 'disable';
        Message.success('重置成功');
      };

      // 显示新建模态框
      const showAddModal = () => {
        addFormData.ip = '';
        addFormData.gw = '';
        addFormData.desc = '';
        isAddModalVisible.value = true;
      };

      // 处理新建确认
      const handleAddConfirm = async (done: any) => {
        try {
          const errors = await addFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          // 添加到表格数据
          const newRecord: RouteIPv6Item = {
            index: routeTableData.value.length + 1,
            ip: addFormData.ip,
            gw: addFormData.gw,
            desc: addFormData.desc,
            isEditing: false,
          };
          routeTableData.value.push(newRecord);

          Message.success('添加成功');
          done(true);
        } catch (error) {
          console.error('添加失败:', error);
          Message.error('添加请求失败');
          done(false);
        }
      };

      // 处理新建取消
      const handleAddCancel = () => {
        isAddModalVisible.value = false;
      };

      // 编辑记录
      const editRecord = (record: RouteIPv6Item) => {
        currentEditRecord.value = record;
        editFormData.ip = record.ip;
        editFormData.gw = record.gw;
        editFormData.desc = record.desc;
        isEditModalVisible.value = true;
      };

      // 处理编辑确认
      const handleEditConfirm = async (done: any) => {
        try {
          const errors = await editFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          if (currentEditRecord.value) {
            currentEditRecord.value.ip = editFormData.ip;
            currentEditRecord.value.gw = editFormData.gw;
            currentEditRecord.value.desc = editFormData.desc;
          }

          Message.success('编辑成功');
          done(true);
        } catch (error) {
          console.error('编辑失败:', error);
          Message.error('编辑请求失败');
          done(false);
        }
      };

      // 处理编辑取消
      const handleEditCancel = () => {
        isEditModalVisible.value = false;
        currentEditRecord.value = null;
      };

      // 删除记录
      const deleteRecord = (record: RouteIPv6Item, rowIndex: number) => {
        routeTableData.value.splice(rowIndex, 1);
        // 重新设置索引
        routeTableData.value.forEach((item, index) => {
          item.index = index + 1;
        });
        Message.success('删除成功');
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        rules,
        routeColumns,
        routeTableData,
        isRouteLoading,
        isAddModalVisible,
        isEditModalVisible,
        addFormData,
        editFormData,
        addRules,
        editRules,
        addFormRef,
        editFormRef,
        saveAction,
        resetAction,
        showAddModal,
        handleAddConfirm,
        handleAddCancel,
        editRecord,
        handleEditConfirm,
        handleEditCancel,
        deleteRecord,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }

  .uniform-form-item {
    margin-bottom: 16px;
  }

  .form-label {
    font-weight: 500;
    color: #1d2129;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
