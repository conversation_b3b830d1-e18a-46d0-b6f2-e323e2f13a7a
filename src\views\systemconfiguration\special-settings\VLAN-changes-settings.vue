<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 3 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="rp_vlan_chg_sw" class="uniform-form-item">
        <template #label>
          <span class="form-label">VLAN变更开关</span>
        </template>
        <a-select :style="{ width: '100px' }" v-model="formData.rp_vlan_chg_sw">
          <a-option value="disable">关闭</a-option>
          <a-option value="enable">开启</a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        :rules="rules.srv_addr"
        field="srv_addr"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">服务地址</span>
        </template>
        <a-input
          v-model="formData.srv_addr"
          :style="{ width: '200px' }"
          placeholder="请输入服务地址"
        />
      </a-form-item>
      <a-form-item
        :rules="rules.srv_port"
        field="srv_port"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">服务端口</span>
        </template>
        <a-input
          v-model="formData.srv_port"
          :style="{ width: '200px' }"
          placeholder="请输入服务端口"
        />
      </a-form-item>
      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          style="margin-top: 2%"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>提交</template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      const formData = reactive({
        rp_vlan_chg_sw: '',
        srv_addr: '',
        srv_port: '',
      });
      const rules = {
        srv_addr: [
          { required: true, message: '服务地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        srv_port: [
          { required: true, message: '服务端口不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              const port = parseInt(value);
              if (!value || isNaN(port) || port < 1 || port > 65535) {
                callback('端口范围应为：1-65535');
              } else {
                callback();
              }
            },
          },
        ],
      };

      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_other.lua',
            new URLSearchParams({ act: 'rp_vlan_chg' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.rp_vlan_chg_sw = response.data.data.rp_vlan_chg_sw || '';
            formData.srv_addr = response.data.data.srv_addr || '';
            formData.srv_port = response.data.data.srv_port || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          // Arco Design表单验证失败时会返回errors对象
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_other.lua',
            new URLSearchParams({
              act: 'rp_vlan_chg',
              act_type: 'mod',
              rp_vlan_chg_sw: String(formData.rp_vlan_chg_sw),
              srv_addr: String(formData.srv_addr),
              srv_port: String(formData.srv_port),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        saveAction,
        hasPermission,
        formRef,
        rules,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
