<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 3 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="work" class="uniform-form-item">
        <template #label>
          <span class="form-label">功能开关</span>
        </template>
        <a-select :style="{ width: '100px' }" v-model="formData.work">
          <a-option value="disable">关闭</a-option>
          <a-option value="enable">开启</a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        :rules="rules.chk_interval"
        field="chk_interval"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">连续溢出时间</span>
        </template>
        <a-tooltip
          content="单位：秒，缺省：60"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            :style="{ width: '200px' }"
            v-model="formData.chk_interval"
            placeholder=""
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item
        :rules="rules.chk_over_bw"
        field="chk_over_bw"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label"> 溢出带宽门限：</span>
        </template>
        <a-tooltip
          content="单位：kbit/s，缺省：5120"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            :style="{ width: '200px' }"
            v-model="formData.chk_over_bw"
            placeholder=""
          />
        </a-tooltip>
      </a-form-item>

      <a-form-item
        :rules="rules.ctrl_length"
        field="ctrl_length"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label"> 溢出控制时长：</span>
        </template>
        <a-tooltip
          content="单位：秒，缺省：1800"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            :style="{ width: '200px' }"
            v-model="formData.ctrl_length"
            placeholder=""
          />
        </a-tooltip>
      </a-form-item>

      <a-form-item
        :rules="rules.ctrl_bw"
        field="ctrl_bw"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label"> 溢出控制带宽：</span>
        </template>
        <a-tooltip
          content="单位：kbit/s，缺省：5120"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            :style="{ width: '200px' }"
            v-model="formData.ctrl_bw"
            placeholder=""
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          style="margin-top: 2%"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>提交</template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      const formData = reactive({
        work: '',
        chk_interval: '',
        chk_over_bw: '',
        ctrl_bw: '',
        ctrl_length: '',
      });
      const rules = {
        chk_interval: [{ required: true, message: '不能为空' }],
        chk_over_bw: [{ required: true, message: '不能为空' }],
        ctrl_bw: [{ required: true, message: '不能为空' }],
        ctrl_length: [{ required: true, message: '不能为空' }],
      };
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_spec_bw_ctrl.lua',
            new URLSearchParams({ act: 'chk_base' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.work = response.data.data.work || '';
            formData.chk_interval = response.data.data.chk_interval || '';
            formData.chk_over_bw = response.data.data.chk_over_bw || '';
            formData.ctrl_bw = response.data.data.ctrl_bw || '';
            formData.ctrl_length = response.data.data.ctrl_length || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          // Arco Design表单验证失败时会返回errors对象
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'mod',
              chk_interval: String(formData.chk_interval),
              chk_over_bw: String(formData.chk_over_bw),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        saveAction,
        hasPermission,
        rules,
        formRef,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
