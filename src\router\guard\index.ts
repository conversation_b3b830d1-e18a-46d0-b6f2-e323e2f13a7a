import type { Router } from 'vue-router';
import { setRouteEmitter } from '@/utils/route-listener';
import { loadAndSetAppTitle } from '@/utils/title';
import setupUserLoginInfoGuard from './userLoginInfo';
import setupPermissionGuard from './permission';

function setupPageGuard(router: Router) {
  router.beforeEach(async (to) => {
    // emit route change
    setRouteEmitter(to);

    // 在每次路由变化时确保标题正确设置
    loadAndSetAppTitle();
  });
}

export default function createRouteGuard(router: Router) {
  setupPageGuard(router);
  setupUserLoginInfoGuard(router);
  setupPermissionGuard(router);
}
