<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-tabs>
      <a-tab-pane key="1" title="基础配置" v-if="hasPermission('evrrpBasic')">
        <a-row :gutter="[16, 16]" class="form-row">
          <a-col :xs="24" :sm="12" :md="12" :lg="8">
            <a-form-item field="mode" class="uniform-form-item">
              <template #label>
                <span class="form-label">端口备份模式</span>
              </template>
              <a-select :style="{ width: '100px' }" v-model="formData.mode">
                <a-option value="isolate">独立</a-option>
                <a-option value="interaction">相互作用</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]" class="form-row">
          <a-col :xs="24" :sm="12" :md="12" :lg="8">
            <a-form-item field="alive_interval" class="uniform-form-item">
              <template #label>
                <span class="form-label">master发送通知时间间隔</span>
              </template>
              <a-tooltip
                content="单位：秒，不填缺省为1"
                position="tl"
                background-color="#3491FA"
              >
                <a-input-number
                  v-model="formData.alive_interval"
                  placeholder=""
                />
              </a-tooltip>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]" class="form-row">
          <a-col :xs="24" :sm="12" :md="12" :lg="8">
            <a-form-item field="idle_timeout" class="uniform-form-item">
              <template #label>
                <span class="form-label"> 判断master断开超时间隔</span>
              </template>
              <a-tooltip
                content="单位：秒，不填缺省为16"
                position="tl"
                background-color="#3491FA"
              >
                <a-input-number
                  v-model="formData.idle_timeout"
                  placeholder=""
                />
              </a-tooltip>
            </a-form-item>
          </a-col>
        </a-row>

        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          style="margin-top: 2%"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>提交</template>
        </a-button>
      </a-tab-pane>

      <a-tab-pane key="2" title="物理端口" v-if="hasPermission('evrrpPhyPort')">
        <a-space>
          <!-- 新建按钮 -->
          <a-button
            id="evrrpPhyPortAdd"
            :disabled="!hasPermission('evrrpPhyPortAdd')"
            type="primary"
            @click="showModal"
            style="margin-right: 2px"
          >
            <template #icon>
              <icon-plus />
            </template>
            {{ $t('searchTable.operation.add') }}
          </a-button>
        </a-space>

        <a-table
          :columns="columnsPhy"
          :data="paginatedDataPhy"
          style="margin-top: 20px"
          :pagination="false"
          :rules="rulesPhy"
        >
          <template #vid="{ record }">
            <a-input v-model="record.vid" :style="{ width: '100px' }" />
          </template>
          <template #port="{ record }">
            <a-select
              v-model="record.port"
              :style="{ width: '100px' }"
              :options="optionsGroup"
            />
          </template>
          <template #vlan="{ record }">
            <a-input v-model="record.vlan" :style="{ width: '100px' }" />
          </template>
          <template #stat="{ record }">
            <a-select v-model="record.stat" :style="{ width: '100px' }">
              <a-option value="master">master</a-option>
              <a-option value="slave">slave</a-option>
            </a-select>
          </template>
          <template #option="{ record }">
            <a-button
              id="evrrpPhyPortDelete"
              :disabled="!hasPermission('evrrpPhyPortDelete')"
              status="danger"
              @click="deletePhy(record)"
            >
              <template #icon>
                <IconMinus />
              </template>
              <template #default>删除</template>
            </a-button>
            <a-button
              id="evrrpPhyPortMod"
              :disabled="!hasPermission('evrrpPhyPortMod')"
              type="primary"
              @click="editPhy(record)"
              style="margin-left: 2%"
            >
              <template #icon>
                <IconEdit />
              </template>
              <template #default>编辑</template>
            </a-button>
          </template>
        </a-table>
        <a-pagination
          v-model:current="currentPagePhy"
          v-model:page-size="pageSizePhy"
          :total="filteredDataPhy.length"
          show-total
          show-size-changer
          show-jumper
          show-page-size
          style="margin-top: 20px"
        />
      </a-tab-pane>

      <a-tab-pane key="3" title="网络端口" v-if="hasPermission('evrrpNetPort')">
        <a-space>
          <!-- 新建按钮 -->
          <a-button
            id="evrrpNetPortAdd"
            :disabled="!hasPermission('evrrpNetPortAdd')"
            type="primary"
            @click="showModalNet"
            style="margin-right: 2px"
          >
            <template #icon>
              <icon-plus />
            </template>
            {{ $t('searchTable.operation.add') }}
          </a-button>
        </a-space>

        <a-table
          :columns="columnsNet"
          :data="paginatedDataNet"
          style="margin-top: 20px"
          :pagination="false"
          :rules="rulesNet"
        >
          <template #vid="{ record }">
            <a-input v-model="record.vid" :style="{ width: '100px' }" />
          </template>
          <template #port="{ record }">
            <a-select
              v-model="record.port"
              :style="{ width: '100px' }"
              :options="optionsGroup"
            />
          </template>
          <template #vlan="{ record }">
            <a-input v-model="record.vlan" :style="{ width: '100px' }" />
          </template>
          <template #addr="{ record }">
            <a-input v-model="record.addr" />
          </template>
          <template #stat="{ record }">
            <a-select v-model="record.stat" :style="{ width: '100px' }">
              <a-option value="master">master</a-option>
              <a-option value="slave">slave</a-option>
            </a-select>
          </template>
          <template #option="{ record }">
            <a-button
              id="evrrpNetPortDelete"
              :disabled="!hasPermission('evrrpNetPortDelete')"
              status="danger"
              @click="deleteNet(record)"
            >
              <template #icon>
                <IconMinus />
              </template>
              <template #default>删除</template>
            </a-button>
            <a-button
              id="evrrpNetPortMod"
              :disabled="!hasPermission('evrrpNetPortMod')"
              type="primary"
              @click="editNet(record)"
              style="margin-left: 2%"
            >
              <template #icon>
                <IconEdit />
              </template>
              <template #default>编辑</template>
            </a-button>
          </template>
        </a-table>
        <a-pagination
          v-model:current="currentPageNet"
          v-model:page-size="pageSizeNet"
          :total="filteredDataNet.length"
          show-total
          show-size-changer
          show-jumper
          show-page-size
          style="margin-top: 20px"
        />
      </a-tab-pane>
    </a-tabs>
  </a-card>

  <a-modal
    v-model:visible="isModalVisible"
    title="添加物理端口"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOkPhy"
    @cancel="handleCancel"
    :width="700"
  >
    <a-form :model="formDataPhy" :rules="rulesPhy" ref="formRefPhy">
      <a-form-item label="虚拟mac ID" field="vid">
        <a-input v-model="formDataPhy.vid" />
      </a-form-item>
      <a-form-item label="端口" field="port">
        <a-select v-model="formDataPhy.port" :options="optionsGroup" />
      </a-form-item>
      <a-form-item label="交换协议包vlan" field="vlan">
        <a-input-number v-model="formDataPhy.vlan" />
      </a-form-item>
      <a-form-item label="主备" field="stat">
        <a-select v-model="formDataPhy.stat">
          <a-option value="master">master</a-option>
          <a-option value="slave">slave</a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
  <a-modal
    v-model:visible="isModalVisibleNet"
    title="添加网络端口"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOkNet"
    @cancel="handleCancelNet"
    :width="700"
  >
    <a-form :model="formDataNet" :rules="rulesNet" ref="formRefNet">
      <a-form-item label="虚拟mac ID" field="vid">
        <a-input v-model="formDataNet.vid" />
      </a-form-item>
      <a-form-item label="端口" field="port">
        <a-select v-model="formDataNet.port" :options="optionsGroup" />
      </a-form-item>
      <a-form-item label="端口vlan" field="vlan">
        <a-input-number v-model="formDataNet.vlan" />
      </a-form-item>
      <a-form-item label="网络地址" field="addr">
        <a-input v-model="formDataNet.addr" />
      </a-form-item>
      <a-form-item label="主备" field="stat">
        <a-select v-model="formDataNet.stat">
          <a-option value="master">master</a-option>
          <a-option value="slave">slave</a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed,
    watch,
  } from 'vue';
  import { Message, TableRowSelection } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      const formRefPhy = ref();
      const formRefNet = ref();

      const formData = reactive({
        mode: 'isolation',
        alive_interval: 1,
        idle_timeout: 16,
      });

      const rulesPhy = {
        vid: [
          { required: true, message: '虚拟mac ID不能为空' },
          {
            validator: (value: number, callback: (error?: string) => void) => {
              if (Number(value) < 1 || Number(value) > 255) {
                callback('虚拟mac ID值范围：1~255');
              } else {
                callback();
              }
            },
          },
        ],
        port: [{ required: true, message: '端口不能为空' }],
        vlan: [
          { required: true, message: '交换协议包vlan不能为空' },
          {
            validator: (value: number, callback: (error?: string) => void) => {
              if (Number(value) < 0 || Number(value) > 4095) {
                callback('vlan的范围：0~4095');
              } else {
                callback();
              }
            },
          },
        ],
        stat: [{ required: true, message: '主备不能为空' }],
      };
      const rulesNet = {
        vid: [
          { required: true, message: '虚拟mac ID不能为空' },
          {
            validator: (value: number, callback: (error?: string) => void) => {
              if (Number(value) < 1 || Number(value) > 255) {
                callback('虚拟mac ID值范围：1~255');
              } else {
                callback();
              }
            },
          },
        ],
        port: [{ required: true, message: '端口不能为空' }],
        vlan: [
          { required: true, message: '端口vlan不能为空' },
          {
            validator: (value: number, callback: (error?: string) => void) => {
              if (Number(value) < 0 || Number(value) > 4095) {
                callback('vlan的范围：0~4095');
              } else {
                callback();
              }
            },
          },
        ],
        addr: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: number, callback: (error?: string) => void) => {
              if (!isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        stat: [{ required: true, message: '主备不能为空' }],
      };

      const formDataPhy = reactive({
        vid: '',
        port: '',
        vlan: null,
        stat: '',
      });
      const formDataNet = reactive({
        vid: '',
        port: '',
        vlan: null,
        addr: '',
        stat: '',
      });

      const filteredDataPhy = ref<any[]>([]);
      const currentPagePhy = ref(1);
      const pageSizePhy = ref(10);
      const paginatedDataPhy = computed(() => {
        const start = (currentPagePhy.value - 1) * pageSizePhy.value;
        return filteredDataPhy.value.slice(start, start + pageSizePhy.value);
      });

      const filteredDataNet = ref<any[]>([]);
      const currentPageNet = ref(1);
      const pageSizeNet = ref(10);
      const paginatedDataNet = computed(() => {
        const start = (currentPageNet.value - 1) * pageSizeNet.value;
        return filteredDataNet.value.slice(start, start + pageSizeNet.value);
      });

      const columnsPhy = [
        {
          title: '虚拟mac ID',
          dataIndex: 'vid',
          slotName: 'vid',
        },
        {
          title: '端口',
          dataIndex: 'port',
          slotName: 'port',
        },
        {
          title: '交换协议包vlan',
          dataIndex: 'vlan',
          slotName: 'vlan',
        },
        {
          title: '主备',
          dataIndex: 'stat',
          slotName: 'stat',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];

      const columnsNet = [
        {
          title: '虚拟mac ID',
          dataIndex: 'vid',
          slotName: 'vid',
        },
        {
          title: '端口',
          dataIndex: 'port',
          slotName: 'port',
        },
        {
          title: '端口vlan',
          dataIndex: 'vlan',
          slotName: 'vlan',
        },
        {
          title: '网络地址',
          dataIndex: 'addr',
          slotName: 'addr',
        },
        {
          title: '主备',
          dataIndex: 'stat',
          slotName: 'stat',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];

      const isModalVisible = ref(false);
      const isModalVisibleNet = ref(false);

      const data = ref<any[]>([]);
      const data1 = ref<any[]>([]);
      const dataPhy = ref([]);
      const dataNet = ref<any[]>([]);

      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_port.lua',
            new URLSearchParams({ act: 'evrrp' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          // console.log(response);

          if (response.data.code === 200) {
            formData.mode = response.data.data.mode || 'isolation';
            formData.alive_interval =
              Number(response.data.data.alive_interval) || 1;
            formData.idle_timeout =
              Number(response.data.data.idle_timeout) || 16;

            data.value = Array.isArray(response.data.data.phy_port)
              ? response.data.data.phy_port
              : [];
            filteredDataPhy.value = [...data.value];
            dataPhy.value = JSON.parse(JSON.stringify(filteredDataPhy.value));

            data1.value = Array.isArray(response.data.data.net_port)
              ? response.data.data.net_port
              : [];
            filteredDataNet.value = [...data1.value];
            dataNet.value = JSON.parse(JSON.stringify(filteredDataNet.value));
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };
      const showModal = () => {
        if (!hasPermission('evrrpPhyPortAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 清空表单
        formDataPhy.vid = '';
        formDataPhy.port = '';
        formDataPhy.vlan = '';
        formDataPhy.stat = '';
        isModalVisible.value = true;
      };

      const showModalNet = () => {
        if (!hasPermission('evrrpNetPortAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 清空表单
        formDataNet.vid = '';
        formDataNet.port = '';
        formDataNet.vlan = '';
        formDataNet.addr = '';
        formDataNet.stat = '';
        isModalVisibleNet.value = true;
      };

      // 处理确认
      const handleBeforeOkPhy = async (done) => {
        if (!hasPermission('evrrpPhyPortAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 只通过表单验证规则验证
        formRefPhy.value.validate().then((errors) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 表单验证通过后，发送添加请求
          const { vid, port, vlan, stat } = formDataPhy;

          axios
            .post(
              '/lua/set_cfg_port.lua',
              new URLSearchParams({
                act: 'evrrp',
                act_type: 'addphy',
                vid,
                port,
                vlan,
                stat,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )
            .then((response) => {
              if (response.data.code === 200) {
                fetchData();
                Message.success(response.data.result);
                // 成功后清空表单
                formDataPhy.vid = '';
                formDataPhy.port = '';
                formDataPhy.vlan = '';
                formDataPhy.stat = '';
                done(true); // 允许模态框关闭
                isModalVisible.value = false;
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });
      };
      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };
      const deletePhy = async (record: {
        vid: string;
        port: string;
        vlan: string;
        stat: string;
      }) => {
        try {
          if (!hasPermission('evrrpPhyPortDelete')) {
            Message.error('您没有权限');
            return;
          }

          const { vid, port, vlan, stat } = record;
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'delphy',
              vid,
              port,
              vlan,
              stat,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData();
            Message.success(response.data.result);
          } else {
            Message.error(response.data.err);
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('删除失败');
        }
      };
      const editPhy = async (record) => {
        try {
          if (!hasPermission('evrrpPhyPortMod')) {
            Message.error('您没有权限');
            return;
          }

          const originalData = dataPhy.value.find(
            (item) => item.key === record.key
          );
          const { vid, port, vlan, stat } = record;

          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'modphy',
              vid,
              port,
              vlan,
              stat,
              old_vid: originalData.vid,
              old_port: originalData.port,
              old_vlan: originalData.vlan,
              old_stat: originalData.stat,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData();
            Message.success(response.data.result);
          } else {
            Message.error(response.data.err);
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('编辑失败');
        }
      };

      // 处理确认
      const handleBeforeOkNet = async (done) => {
        if (!hasPermission('evrrpNetPortAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 只通过表单验证规则验证
        formRefNet.value.validate().then((errors) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 表单验证通过后，发送添加请求
          const { vid, port, vlan, addr, stat } = formDataNet;

          axios
            .post(
              '/lua/set_cfg_port.lua',
              new URLSearchParams({
                act: 'evrrp',
                act_type: 'addnet',
                vid,
                port,
                vlan,
                addr,
                stat,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )
            .then((response) => {
              if (response.data.code === 200) {
                fetchData();
                Message.success(response.data.result);
                // 成功后清空表单
                formDataNet.vid = '';
                formDataNet.port = '';
                formDataNet.vlan = '';
                formDataNet.addr = '';
                formDataNet.stat = '';
                done(true); // 允许模态框关闭
                isModalVisibleNet.value = false;
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });
      };
      // 处理取消
      const handleCancelNet = () => {
        isModalVisibleNet.value = false;
      };

      const deleteNet = async (record: {
        vid: string;
        port: string;
        vlan: string;
        addr: string;
        stat: string;
      }) => {
        try {
          if (!hasPermission('evrrpNetPortDelete')) {
            Message.error('您没有权限');
            return;
          }

          const { vid, port, vlan, addr, stat } = record;
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'delnet',
              vid,
              port,
              vlan,
              addr,
              stat,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData();
            Message.success(response.data.result);
          } else {
            Message.error(response.data.err);
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('删除失败');
        }
      };

      const editNet = async (record) => {
        try {
          if (!hasPermission('evrrpNetPortMod')) {
            Message.error('您没有权限');
            return;
          }

          const originalData = dataNet.value.find(
            (item) => item.key === record.key
          );
          const { vid, port, vlan, addr, stat } = record;

          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'modnet',
              vid,
              port,
              vlan,
              addr,
              stat,
              old_vid: originalData.vid,
              old_port: originalData.port,
              old_vlan: originalData.vlan,
              old_addr: originalData.addr,
              old_stat: originalData.stat,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            await fetchData();
            Message.success(response.data.result);
          } else {
            Message.error(response.data.err);
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('编辑失败');
        }
      };

      interface SelectOption {
        label: string;
        value: string;
      }
      const optionsGroup = ref<SelectOption[]>([]);
      const getPorts = async () => {
        try {
          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({ act: 'get_vm_port' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            const formattedData: SelectOption[] = [];

            response.data.data.port.forEach((item: string) => {
              formattedData.push({
                label: String(item),
                value: String(item),
              });
            });

            optionsGroup.value = formattedData;
          } else {
            Message.error(response.data.result || '获取端口失败');
          }
        } catch (error) {
          Message.error('获取端口错误');
        }
      };

      const saveAction = async () => {
        try {
          if (!hasPermission('evrrpSubmit')) {
            Message.error('您没有权限');
            return;
          }

          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'mod',
              mode: formData.mode,
              alive_interval: String(formData.alive_interval),
              idle_timeout: String(formData.idle_timeout),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            getPorts();
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          getPorts();
          fetchData();
        }
      });

      return {
        formData,
        formDataPhy,
        formDataNet,
        rulesPhy,
        rulesNet,
        formRefPhy,
        formRefNet,
        deletePhy,
        deleteNet,
        editPhy,
        editNet,
        saveAction,
        hasPermission,
        columnsPhy,
        paginatedDataPhy,
        currentPagePhy,
        pageSizePhy,
        filteredDataPhy,
        columnsNet,
        paginatedDataNet,
        currentPageNet,
        pageSizeNet,
        filteredDataNet,
        showModal,
        showModalNet,
        isModalVisible,
        isModalVisibleNet,
        handleBeforeOkPhy,
        handleCancel,
        handleBeforeOkNet,
        handleCancelNet,
        optionsGroup,
      };
    },
  });
</script>

<style scoped>
  .form-row {
    margin-bottom: 16px;
  }

  .uniform-form-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .form-label {
    display: inline-block;
    margin-bottom: 2px;
    white-space: normal;
    word-break: break-word;
    width: 100%;
  }

  .uniform-form-item >>> .arco-form-item-wrapper {
    width: 100%;
  }

  /* 中屏幕及以上时改为水平布局 */
  @media (min-width: 768px) {
    .uniform-form-item {
      flex-direction: row;
      align-items: center;
    }

    .form-label {
      width: 160px;
      text-align: right;
      padding-right: 1px;
      margin-bottom: 0;
    }
  }

  /* 大屏幕时调整标签宽度 */
  @media (min-width: 1200px) {
    .form-label {
      width: 180px;
    }
  }

  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>
