<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.route-settings']" />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane key="1" title="出口路由" v-if="hasPermission('exitRoute')">
        <exit_route :active="activeTabKey === '1'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane
        key="2"
        title="多出口路由"
        v-if="hasPermission('multipleRoute')"
      >
        <multiple_route :active="activeTabKey === '2'" />
      </a-tab-pane>
      <!-- 标签页3 -->
      <a-tab-pane key="3" title="角色路由表" v-if="hasPermission('roleRoute')">
        <role_route :active="activeTabKey === '3'" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  import exit_route from './exit_route.vue';
  import multiple_route from './multiple_route.vue';
  import role_route from './role_route.vue';

  export default defineComponent({
    components: {
      exit_route,
      multiple_route,
      role_route,
    },
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const activeTabKey = ref('1');

      const handleTabChange = (key: string) => {
        activeTabKey.value = key;
      };

      return {
        activeTabKey,
        handleTabChange,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
</style>
