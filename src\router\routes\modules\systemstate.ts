import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const SYSTEMSTATE: AppRouteRecordRaw = {
  path: '/systemstate',
  name: 'systemstate',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.systemstate',
    permissions: ['systemStatus'],
    requiresAuth: true,
    icon: 'icon-dashboard',
    order: 0,
  },
  children: [
    {
      // 端口信息
      path: 'port',
      name: 'Port',
      component: () => import('@/views/systemstate/port/index.vue'),
      meta: {
        locale: 'menu.port',
        requiresAuth: true,
        icon: 'icon-share-alt',
        permissions: ['portInfo'],
        roles: ['*'],
      },
    },
    {
      // 会话查询
      path: 'menu.session-query',
      name: 'menu.session-query',
      component: () => import('@/views/systemstate/session-query/index.vue'),
      meta: {
        locale: 'menu.session-query',
        icon: 'icon-message',
        permissions: ['sessionQuery'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 历史状态
      path: 'menu.chart',
      name: 'menu.chart ',
      component: () => import('@/views/systemstate/chart/index.vue'),
      meta: {
        locale: 'menu.chart',
        icon: 'icon-bar-chart',
        requiresAuth: true,
        permissions: ['chartInfo'],
        roles: ['admin'],
      },
    },
    {
      // 会话列表
      path: 'menu.session-list',
      name: 'menu.session-list',
      component: () => import('@/views/systemstate/session-list/index.vue'),
      meta: {
        locale: 'menu.session-list',
        icon: 'icon-phone',
        permissions: ['sessionList'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 地址池信息
      path: 'menu.dhcp-pool-info',
      name: 'menu.dhcp-pool-info',
      component: () => import('@/views/systemstate/dhcp-pool-info/index.vue'),
      meta: {
        locale: 'menu.dhcp-pool-info',
        icon: 'icon-nav',
        permissions: ['dhcpPoolInfo'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // evrrp端口状态
      path: 'menu.evrrp-port-status',
      name: 'menu.evrrp-port-status',
      component: () =>
        import('@/views/systemstate/evrrp-port-status/index.vue'),
      meta: {
        locale: 'menu.evrrp-port-status',
        icon: 'icon-share-alt',
        permissions: ['evrrpPortStatus'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 接口信息
      path: 'menu.radClient-status',
      name: 'menu.radClient-status',
      component: () => import('@/views/systemstate/radClient-status/index.vue'),
      meta: {
        locale: 'menu.radClient-status',
        icon: 'icon-drive-file',
        permissions: ['radClientStatus'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 历史接口
      path: 'menu.radClient-chart',
      name: 'menu.radClient-chart',
      component: () => import('@/views/systemstate/radClient-chart/index.vue'),
      meta: {
        locale: 'menu.radClient-chart',
        icon: 'icon-bar-chart',
        permissions: ['radClientChartInfo'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 日志信息
      path: 'menu.vm-log',
      name: 'menu.vm-log',
      component: () => import('@/views/systemstate/vm-log/index.vue'),
      meta: {
        locale: 'menu.vm-log',
        icon: 'icon-file',
        permissions: ['vmLog'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 在线用户
      path: 'menu.online-user',
      name: 'menu.online-user',
      component: () => import('@/views/systemstate/online-user/index.vue'),
      meta: {
        locale: 'menu.online-user',
        icon: 'icon-user',
        permissions: ['onlineUsers'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      // 拨号会话
      path: 'menu.oeclient-session',
      name: 'menu.oeclient-session',
      component: () => import('@/views/systemstate/oeclient-session/index.vue'),
      meta: {
        locale: 'menu.oeclient-session',
        icon: 'icon-phone',
        permissions: ['oeclientSession'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
  ],
};

export default SYSTEMSTATE;
