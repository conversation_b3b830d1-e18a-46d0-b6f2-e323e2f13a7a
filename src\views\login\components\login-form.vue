<template>
  <div class="login-form-wrapper">
    <div class="login-form-title">{{ $t('login.form.title') }} </div>
    <div class="login-form-error-msg">{{ errorMessage }}</div>
    <a-form
      ref="loginForm"
      :model="userInfo"
      class="login-form"
      layout="vertical"
      @submit="handleSubmit"
    >
      <a-form-item
        field="userid"
        :rules="[{ required: true, message: $t('login.form.userid.errMsg') }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input
          v-model="userInfo.userid"
          :placeholder="$t('login.form.userid.placeholder')"
        >
          <template #prefix>
            <icon-user />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item
        field="password"
        :rules="[{ required: true, message: $t('login.form.password.errMsg') }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input-password
          v-model="userInfo.password"
          :placeholder="$t('login.form.password.placeholder')"
          allow-clear
        >
          <template #prefix>
            <icon-lock />
          </template>
        </a-input-password>
      </a-form-item>
      <a-space :size="16" direction="vertical">
        <div class="login-form-password-actions">
          <a-checkbox
            checked="rememberPassword"
            :model-value="loginConfig.rememberPassword"
            @change="setRememberPassword as any"
          >
            {{ $t('login.form.rememberPassword') }}
          </a-checkbox>
        </div>
        <a-button
          id="login.submit"
          type="primary"
          html-type="submit"
          long
          :loading="loading"
        >
          {{ $t('login.form.login') }}
        </a-button>
        <!-- <a-button type="text" long class="login-form-register-btn">
          {{ $t('login.form.register') }}
        </a-button> -->
      </a-space>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import { useI18n } from 'vue-i18n';
  import { useStorage } from '@vueuse/core';
  import { useUserStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import type { LoginData } from '@/api/user';

  const router = useRouter();
  const { t } = useI18n();
  const errorMessage = ref('');
  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();

  const loginConfig = useStorage('login-config', {
    rememberPassword: true,
    userid: '', // 演示默认值
    password: '', // demo default value
  });
  const userInfo = reactive({
    userid: loginConfig.value.userid,
    password: loginConfig.value.password,
  });

  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    if (loading.value) return;
    if (!errors) {
      setLoading(true);
      try {
        // console.log('开始登录流程 ');
        // console.log('登录表单数据:', values);

        // 登录 - login方法会自动设置userId并初始化用户信息
        const loginResult = await userStore.login(values as LoginData);
        // console.log('登录API返回结果:', loginResult);
        // console.log('登录成功后的userId:', localStorage.getItem('userId'));

        // 登录成功后获取用户权限
        // console.log('开始获取用户权限...');
        await userStore.fetchUserPermissions(true);
        // console.log('用户权限获取完成，当前权限:', userStore.permissions);

        // 显示成功消息
        Message.success(t('login.form.login.success'));

        // 保存登录配置
        const { rememberPassword } = loginConfig.value;
        const { userid, password } = values;
        // 实际生产环境需要进行加密存储。
        loginConfig.value.userid = rememberPassword ? userid : '';
        loginConfig.value.password = rememberPassword ? password : '';

        // 登录成功后跳转
        const { redirect, ...othersQuery } = router.currentRoute.value.query;
        // console.log('即将跳转到:', redirect || 'Workplace');
        router.push({
          name: (redirect as string) || 'Workplace',
          query: {
            ...othersQuery,
          },
        });
        // console.log('========== 登录流程完成 ==========');
      } catch (err) {
        // console.error('登录或权限获取失败:', err);
        errorMessage.value = (err as Error).message;
      } finally {
        setLoading(false);
      }
    }
  };
  const setRememberPassword = (value: boolean) => {
    loginConfig.value.rememberPassword = value;
  };
</script>

<style lang="less" scoped>
  .login-form {
    &-wrapper {
      width: 320px;
    }

    &-title {
      color: var(--color-text-1);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }

    &-sub-title {
      color: var(--color-text-3);
      font-size: 16px;
      line-height: 24px;
    }

    &-error-msg {
      height: 32px;
      color: rgb(var(--red-6));
      line-height: 32px;
    }

    &-password-actions {
      display: flex;
      justify-content: space-between;
    }

    &-register-btn {
      color: var(--color-text-3) !important;
    }
  }
</style>
