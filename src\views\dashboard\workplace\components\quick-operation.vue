<template>
  <a-card
    class="general-card"
    :title="$t('workplace.quick.operation')"
    :header-style="{ paddingBottom: '0' }"
    :body-style="{ padding: '24px 20px 0 20px' }"
  >
    <template #extra>
      <!-- <a-link>{{ $t('workplace.quickOperation.setup') }}</a-link> -->
    </template>
    <a-row :gutter="8">
      <a-col
        v-for="link in quickLinks"
        :key="link.text"
        :span="8"
        class="wrapper"
        @click="navigateTo(link.route)"
        style="cursor: pointer"
      >
        <div class="icon">
          <component :is="link.icon" />
        </div>
        <a-typography-paragraph class="text">
          {{ $t(link.text) }}
        </a-typography-paragraph>
      </a-col>
    </a-row>
    <a-divider class="split-line" style="margin: 0" />
  </a-card>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import usePermission from '@/hooks/permission';

  const router = useRouter();
  const { accessRouter } = usePermission();

  const quickLinks = [
    { text: 'menu.port', icon: 'icon-file', route: '/systemstate/port' },
    {
      text: 'menu.system-control',
      icon: 'icon-link',
      route: '/systemcontrol/system-control',
    },
    {
      text: 'menu.access-port',
      icon: 'icon-share-alt',
      route: '/systemconfiguration/access-port',
    },
    {
      text: 'menu.basic_settings',
      icon: 'icon-share-alt',
      route: '/Virtual_Machine_Settings/network_port',
    },
    {
      text: 'menu.Network settings',
      icon: 'icon-settings',
      route: '/Host_Setting/Network_settings',
    },
    {
      text: 'menu.Operator Configuration',
      icon: 'icon-user',
      route: '/Operator_management/Operator_Configuration',
    },
  ];

  function updateRecentLinks(route: string) {
    const recentLinks = JSON.parse(localStorage.getItem('recentLinks') || '[]');
    if (!recentLinks.includes(route)) {
      recentLinks.unshift(route);
      if (recentLinks.length > 3) {
        recentLinks.pop();
      }
      localStorage.setItem('recentLinks', JSON.stringify(recentLinks));
    }
  }

  function navigateTo(route: string) {
    // 获取目标路由的元信息
    const targetRoute = router.resolve(route);

    // 检查权限
    if (!accessRouter(targetRoute)) {
      Message.error('您没有权限访问该页面');
      return;
    }

    router.push(route);
    updateRecentLinks(route);
  }
</script>

<style scoped lang="less"></style>
