<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 3 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item
        :rules="rules.local_ip"
        field="local_ip"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">禁止option 60：</span>
        </template>
        <a-tooltip
          content="禁止option 60 所匹配vendor-id的数据包"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.local_ip"
            :style="{ width: '200px' }"
            placeholder=""
        /></a-tooltip>
      </a-form-item>
      <a-form-item
        :rules="rules.peer_ip"
        field="peer_ip"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label"> 处理option 60：</span>
        </template>
        <a-tooltip
          content="仅仅处理option 60 所匹配vendor-id的数据包"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.peer_ip"
            :style="{ width: '200px' }"
            placeholder=""
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          style="margin-top: 2%"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>提交</template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      const formData = reactive({
        work: '',
        local_ip: '',
        peer_ip: '',
        key_num: '',
      });
      const rules = {
        local_ip: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        peer_ip: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
      };

      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_5g_net_gw.lua',
            new URLSearchParams({ act: 'gre_tun_5g' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.work = response.data.data.work || '';
            formData.local_ip = response.data.data.local_ip || '';
            formData.peer_ip = response.data.data.peer_ip || '';
            formData.key_num = response.data.data.key_num || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          // Arco Design表单验证失败时会返回errors对象
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'mod',
              local_ip: String(formData.local_ip),
              peer_ip: String(formData.peer_ip),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        saveAction,
        hasPermission,
        formRef,
        rules,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
