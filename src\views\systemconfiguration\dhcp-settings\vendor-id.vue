<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 3 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item
        :rules="rules.local_ip"
        field="local_ip"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">{{ $t('Prohibit option') }}</span>
        </template>
        <a-tooltip
          :content="$t('prohibit_option_tooltip')"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.local_ip"
            :style="{ width: '200px' }"
            placeholder=""
        /></a-tooltip>
      </a-form-item>
      <a-form-item
        :rules="rules.peer_ip"
        field="peer_ip"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">{{ $t('process_option_60') }}</span>
        </template>
        <a-tooltip
          :content="$t('process_option_tooltip')"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.peer_ip"
            :style="{ width: '200px' }"
            placeholder=""
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          style="margin-top: 2%"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>{{ $t('submit') }}</template>
        </a-button>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          style="margin-top: 2%; margin-left: 2%"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>{{ $t('cancel_configuration') }}</template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const { t } = useI18n();
      const formRef = ref(null);

      const formData = reactive({
        work: '',
        local_ip: '',
        peer_ip: '',
        key_num: '',
      });
      const rules = {
        local_ip: [
          { required: true, message: t('network_address_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('ip_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        peer_ip: [
          { required: true, message: t('network_address_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('ip_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
      };

      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'vendor_id' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.work = response.data.data.work || '';
            formData.local_ip = response.data.data.local_ip || '';
            formData.peer_ip = response.data.data.peer_ip || '';
            formData.key_num = response.data.data.key_num || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error(t('get_data_failed'));
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error(t('no_permission'));
          return;
        }
        try {
          const errors = await formRef.value.validate();
          // Arco Design表单验证失败时会返回errors对象
          if (errors) {
            Message.error(t('form_validation_failed'));
            return;
          }
        } catch (validationError) {
          Message.error(t('form_validation_error'));
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'vendor_id',
              act_type: 'mod',
              local_ip: String(formData.local_ip),
              peer_ip: String(formData.peer_ip),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || t('configuration_success'));
          } else {
            Message.error(response.data.err || t('configuration_failed'));
          }
        } catch (error) {
          Message.error(t('configuration_request_failed'));
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        saveAction,
        hasPermission,
        formRef,
        rules,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
