import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const OPERATORMANAGEMENT: AppRouteRecordRaw = {
  path: '/Host_Setting',
  name: 'Host_Setting',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.Host Setting',
    permissions: ['hostSettings'],
    icon: 'icon-desktop',
    requiresAuth: true,
    order: 5,
  },
  children: [
    {
      path: 'Network_settings',
      name: 'Network_settings',
      component: () =>
        import('@/views/Host_Setting/Network_settings/index.vue'),
      meta: {
        locale: 'menu.Network settings',
        icon: 'icon-settings',
        permissions: ['networkSetting'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
  ],
};

export default OPERATORMANAGEMENT;
