<!-- 出口状态组件 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.export-status']" />
    <a-card title="出口状态">
      <div class="export">
        <div class="export2">
          <a-table
            :columns="columns"
            :data="exportdata"
            column-resizable
            :pagination="false"
          />
        </div>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import axios from 'axios';

  interface ExportData {
    key: string;
    exportName: string;
    groupId: number;
    status: string;
    boundIPCount: number;
    boundMappingCount: number;
    downlinkBytes: number;
    uplinkBytes: number;
    realtimeDownlinkBandwidth: number;
    totalDownlinkBandwidth: number;
    realtimeUplinkBandwidth: number;
    totalUplinkBandwidth: number;
  }

  const exportdata = ref<ExportData[]>([]);

  const columns = [
    {
      title: '出口名',
      dataIndex: 'exportName',
    },
    {
      title: '组ID',
      dataIndex: 'groupId',
    },
    {
      title: '状态',
      dataIndex: 'status',
    },
    {
      title: '绑IP会话数目',
      dataIndex: 'boundIPCount',
    },
    {
      title: '绑映射配置数目',
      dataIndex: 'boundMappingCount',
    },
    {
      title: '下行字节数据',
      dataIndex: 'downlinkBytes',
    },
    {
      title: '上行字节数据',
      dataIndex: 'uplinkBytes',
    },
    {
      title: '实时下行带宽',
      dataIndex: 'realtimeDownlinkBandwidth',
    },
    {
      title: '下行总带宽',
      dataIndex: 'totalDownlinkBandwidth',
    },
    {
      title: '实时上行带宽',
      dataIndex: 'realtimeUplinkBandwidth',
    },
    {
      title: '上行总带宽',
      dataIndex: 'totalUplinkBandwidth',
    },
  ];

  onMounted(async () => {
    try {
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({ act: 'mulit_output' }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      console.log('Full response:', response);
      console.log('Response data:', response.data);

      // 使用 response.status 检查 HTTP 状态码
      if (response.data.code === 200) {
        const formattedData = [];
        response.data.data.forEach((item) => {
          const currentExportData: ExportData = {
            key: item.grp_id,
            exportName: item.out_name,
            status: item.stat,
            groupId: item.grp_id,
            boundIPCount: item.ip_num,
            boundMappingCount: item.map_num,
            downlinkBytes: item.downbyte,
            uplinkBytes: item.upbyte,
            realtimeDownlinkBandwidth: item.downrate,
            totalDownlinkBandwidth: item.downbandwidth,
            realtimeUplinkBandwidth: item.uprate,
            totalUplinkBandwidth: item.upbandwidth,
          };
          formattedData.push(currentExportData);
        });

        exportdata.value = formattedData;
      } else {
        console.error(
          'Error fetching data:',
          response.data ? response.data.msg : 'No data received'
        );
      }
    } catch (error) {
      console.error('Error fetching export status info:', error);
    }
  });
</script>

<style scoped>
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }
  .export,
  .export2 {
    background-color: #fff;
    padding: 5px;
  }

  .export {
    background-color: #fff;
    margin-bottom: 20px;
  }
</style>
