<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- IPv6协议栈开关配置 -->
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 4 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="work" class="uniform-form-item">
        <template #label>
          <span class="form-label">DNS Allowlist:</span>
        </template>
        <a-select :style="{ width: '100px' }" v-model="formData.work">
          <a-option value="disable">Disable</a-option>
          <a-option value="enable">Enable</a-option>
        </a-select>
      </a-form-item>

      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>Submit</template>
        </a-button>
        <a-button style="margin-left: 10px" @click="resetAction">
          <template #icon>
            <icon-refresh />
          </template>
          <template #default>Reset</template>
        </a-button>
      </a-form-item>
    </a-form>

    <a-divider />

    <!-- 表格标题和新建按钮 -->
    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <h4 style="margin: 0; color: #1d2129">Domain Allowlist</h4>
      </a-col>
      <a-col :span="12" style="text-align: right">
        <a-button type="primary" @click="showAddModal">
          <template #icon>
            <icon-plus />
          </template>
          Add
        </a-button>
      </a-col>
    </a-row>

    <!-- 域名白名单配置表格 -->
    <a-table
      row-key="index"
      :columns="domainColumns"
      :data="domainTableData"
      :pagination="false"
      :bordered="true"
      :loading="isDomainLoading"
    >
      <template #name="{ record }">
        <span>{{ record.name || '-' }}</span>
      </template>

      <template #desc="{ record }">
        <span>{{ record.desc || '-' }}</span>
      </template>

      <template #operate="{ record, rowIndex }">
        <a-space>
          <a-button type="primary" size="small" @click="editRecord(record)">
            <template #icon>
              <icon-edit />
            </template>
            Edit
          </a-button>
          <a-button
            type="primary"
            status="danger"
            size="small"
            @click="deleteRecord(record, rowIndex)"
          >
            <template #icon>
              <icon-delete />
            </template>
            Delete
          </a-button>
        </a-space>
      </template>
    </a-table>
  </a-card>

  <!-- 新建域名模态框 -->
  <a-modal
    v-model:visible="isAddModalVisible"
    title="Add Domain"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleAddConfirm"
    @cancel="handleAddCancel"
    :width="600"
  >
    <a-form :model="addFormData" :rules="addRules" ref="addFormRef">
      <a-form-item label="Domain Name" field="name">
        <a-input
          v-model="addFormData.name"
          placeholder="请输入域名"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="Description" field="desc">
        <a-input
          v-model="addFormData.desc"
          placeholder="请输入描述"
          style="width: 300px"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 编辑域名模态框 -->
  <a-modal
    v-model:visible="isEditModalVisible"
    title="Edit Domain"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleEditConfirm"
    @cancel="handleEditCancel"
    :width="600"
  >
    <a-form :model="editFormData" :rules="editRules" ref="editFormRef">
      <a-form-item label="Domain Name" field="name">
        <a-input
          v-model="editFormData.name"
          placeholder="请输入域名"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="Description" field="desc">
        <a-input
          v-model="editFormData.desc"
          placeholder="请输入描述"
          style="width: 300px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface DomainItem {
    index: number;
    name: string;
    desc: string;
    isEditing?: boolean;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      // DNS白名单配置表单数据
      const formData = reactive({
        work: 'disable',
      });

      // 表单验证规则
      const rules = {};

      // 域名白名单表格列定义
      const domainColumns = [
        {
          title: 'Domain Name',
          dataIndex: 'name',
          slotName: 'name',
          align: 'center' as const,
          width: 400,
        },
        {
          title: 'Description',
          dataIndex: 'desc',
          slotName: 'desc',
          align: 'center' as const,
          width: 300,
        },
        {
          title: 'Operate',
          dataIndex: 'operate',
          slotName: 'operate',
          align: 'center' as const,
          width: 200,
        },
      ];

      const domainTableData = ref<DomainItem[]>([]);
      const isDomainLoading = ref(false);
      const isAddModalVisible = ref(false);
      const isEditModalVisible = ref(false);
      const currentEditRecord = ref<DomainItem | null>(null);

      // 新建表单数据
      const addFormData = reactive({
        name: '',
        desc: '',
      });

      // 编辑表单数据
      const editFormData = reactive({
        name: '',
        desc: '',
      });

      // 新建表单规则
      const addRules = {
        name: [{ required: true, message: '域名不能为空' }],
        desc: [{ required: false, message: '描述可以为空' }],
      };

      // 编辑表单规则
      const editRules = {
        name: [{ required: true, message: '域名不能为空' }],
        desc: [{ required: false, message: '描述可以为空' }],
      };

      const addFormRef = ref();
      const editFormRef = ref();

      // 获取DNS白名单配置数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_ipv6_setting.lua',
            new URLSearchParams({ act: 'dns' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            const data = response.data.data;
            formData.work = data.work || 'disable';

            // 处理域名数组
            if (data.domain && Array.isArray(data.domain)) {
              domainTableData.value = data.domain.map(
                (item: any, index: number) => ({
                  index: index + 1,
                  name: item.name || '',
                  desc: item.desc || '',
                  isEditing: false,
                })
              );
            } else {
              domainTableData.value = [];
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 保存DNS白名单配置
      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_ipv6_setting.lua',
            new URLSearchParams({
              act: 'dns',
              act_type: 'mod',
              work: formData.work,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      // 重置表单
      const resetAction = () => {
        formData.work = 'disable';
        Message.success('重置成功');
      };

      // 显示新建模态框
      const showAddModal = () => {
        addFormData.name = '';
        addFormData.desc = '';
        isAddModalVisible.value = true;
      };

      // 处理新建确认
      const handleAddConfirm = async (done: any) => {
        try {
          const errors = await addFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          // 添加到表格数据
          const newRecord: DomainItem = {
            index: domainTableData.value.length + 1,
            name: addFormData.name,
            desc: addFormData.desc,
            isEditing: false,
          };
          domainTableData.value.push(newRecord);

          Message.success('添加成功');
          done(true);
        } catch (error) {
          console.error('添加失败:', error);
          Message.error('添加请求失败');
          done(false);
        }
      };

      // 处理新建取消
      const handleAddCancel = () => {
        isAddModalVisible.value = false;
      };

      // 编辑记录
      const editRecord = (record: DomainItem) => {
        currentEditRecord.value = record;
        editFormData.name = record.name;
        editFormData.desc = record.desc;
        isEditModalVisible.value = true;
      };

      // 处理编辑确认
      const handleEditConfirm = async (done: any) => {
        try {
          const errors = await editFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          if (currentEditRecord.value) {
            currentEditRecord.value.name = editFormData.name;
            currentEditRecord.value.desc = editFormData.desc;
          }

          Message.success('编辑成功');
          done(true);
        } catch (error) {
          console.error('编辑失败:', error);
          Message.error('编辑请求失败');
          done(false);
        }
      };

      // 处理编辑取消
      const handleEditCancel = () => {
        isEditModalVisible.value = false;
        currentEditRecord.value = null;
      };

      // 删除记录
      const deleteRecord = (record: DomainItem, rowIndex: number) => {
        domainTableData.value.splice(rowIndex, 1);
        // 重新设置索引
        domainTableData.value.forEach((item, index) => {
          item.index = index + 1;
        });
        Message.success('删除成功');
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        rules,
        domainColumns,
        domainTableData,
        isDomainLoading,
        isAddModalVisible,
        isEditModalVisible,
        addFormData,
        editFormData,
        addRules,
        editRules,
        addFormRef,
        editFormRef,
        saveAction,
        resetAction,
        showAddModal,
        handleAddConfirm,
        handleAddCancel,
        editRecord,
        handleEditConfirm,
        handleEditCancel,
        deleteRecord,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }

  .uniform-form-item {
    margin-bottom: 16px;
  }

  .form-label {
    font-weight: 500;
    color: #1d2129;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
