<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- IPv6协议栈开关配置 -->
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 4 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="ipv6_stack_switch" class="uniform-form-item">
        <template #label>
          <span class="form-label">Start IPV6 protocol stack:</span>
        </template>
        <a-select
          :style="{ width: '100px' }"
          v-model="formData.stack"
          @change="handleStackSwitchChange"
        >
          <a-option value="disable">No</a-option>
          <a-option value="enable">Yes</a-option>
        </a-select>
        <span style="margin-left: 10px; color: #f53f3f; font-size: 12px">
          Need to restart the virtual machine after configuration
        </span>
      </a-form-item>

      <!-- 当开启IPv6协议栈时显示的配置项 -->
      <template v-if="formData.stack === 'enable'">
        <a-form-item :rules="rules.dns1" field="dns1" class="uniform-form-item">
          <template #label>
            <span class="form-label">Preferred IPV6 DNS:</span>
          </template>
          <a-input
            v-model="formData.dns1"
            placeholder="240c:cd22:cd22::8"
            :style="{ width: '300px' }"
          />
        </a-form-item>

        <a-form-item :rules="rules.dns2" field="dns2" class="uniform-form-item">
          <template #label>
            <span class="form-label">Alternative IPV6 DNS:</span>
          </template>
          <a-input
            v-model="formData.dns2"
            placeholder="240c:cd22:cd22::9"
            :style="{ width: '300px' }"
          />
        </a-form-item>

        <a-form-item
          :rules="rules.route"
          field="route"
          class="uniform-form-item"
        >
          <template #label>
            <span class="form-label">IPV6 default routing:</span>
          </template>
          <a-input
            v-model="formData.route"
            placeholder="240c:cd22:cd22::1"
            :style="{ width: '300px' }"
          />
        </a-form-item>

        <a-form-item>
          <a-button
            :disabled="!hasPermission('evrrpSubmit')"
            type="primary"
            @click="saveAction"
          >
            <template #icon>
              <icon-check />
            </template>
            <template #default>Submit</template>
          </a-button>
          <a-button style="margin-left: 10px" @click="resetAction">
            <template #icon>
              <icon-refresh />
            </template>
            <template #default>Reset</template>
          </a-button>
        </a-form-item>
      </template>
    </a-form>

    <!-- 当开启IPv6协议栈时显示的端口IPv6地址表格 -->
    <template v-if="formData.stack === 'enable'">
      <a-divider />

      <!-- 表格标题和新建按钮 -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <h4 style="margin: 0; color: #1d2129"> The port's IPV6 address </h4>
        </a-col>
        <a-col :span="12" style="text-align: right">
          <a-button type="primary" @click="showAddModal">
            <template #icon>
              <icon-plus />
            </template>
            Add
          </a-button>
        </a-col>
      </a-row>

      <!-- IPv6地址配置表格 -->
      <a-table
        row-key="index"
        :columns="portColumns"
        :data="portTableData"
        :pagination="false"
        :bordered="true"
        :loading="isPortLoading"
      >
        <template #ip="{ record }">
          <span>{{ record.ip || '-' }}</span>
        </template>

        <template #port="{ record }">
          <span>{{ record.port || '-' }}</span>
        </template>

        <template #vlan="{ record }">
          <span>{{ record.vlan || '-' }}</span>
        </template>

        <template #switch="{ record }">
          <span>{{ record.switch || 'Open' }}</span>
        </template>

        <template #desc="{ record }">
          <span>{{ record.desc || '-' }}</span>
        </template>

        <template #operate="{ record, rowIndex }">
          <a-space>
            <a-button type="primary" size="small" @click="editRecord(record)">
              <template #icon>
                <icon-edit />
              </template>
              Edit
            </a-button>
            <a-button
              type="primary"
              status="danger"
              size="small"
              @click="deleteRecord(record, rowIndex)"
            >
              <template #icon>
                <icon-delete />
              </template>
              Delete
            </a-button>
          </a-space>
        </template>
      </a-table>
    </template>
  </a-card>

  <!-- 新建IPv6地址模态框 -->
  <a-modal
    v-model:visible="isAddModalVisible"
    title="Add IPv6 Address"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleAddConfirm"
    @cancel="handleAddCancel"
    :width="600"
  >
    <a-form :model="addFormData" :rules="addRules" ref="addFormRef">
      <a-form-item label="IPv6 Address" field="ip">
        <a-input
          v-model="addFormData.ip"
          placeholder="请输入IPv6地址"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="Port" field="port">
        <a-input
          v-model="addFormData.port"
          placeholder="请输入端口"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="VLAN" field="vlan">
        <a-input
          v-model="addFormData.vlan"
          placeholder="请输入VLAN"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="Description" field="desc">
        <a-input
          v-model="addFormData.desc"
          placeholder="请输入描述"
          style="width: 300px"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 编辑IPv6地址模态框 -->
  <a-modal
    v-model:visible="isEditModalVisible"
    title="Edit IPv6 Address"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleEditConfirm"
    @cancel="handleEditCancel"
    :width="600"
  >
    <a-form :model="editFormData" :rules="editRules" ref="editFormRef">
      <a-form-item label="IPv6 Address" field="ip">
        <a-input
          v-model="editFormData.ip"
          placeholder="请输入IPv6地址"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="Port" field="port">
        <a-input
          v-model="editFormData.port"
          placeholder="请输入端口"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="VLAN" field="vlan">
        <a-input
          v-model="editFormData.vlan"
          placeholder="请输入VLAN"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item label="Description" field="desc">
        <a-input
          v-model="editFormData.desc"
          placeholder="请输入描述"
          style="width: 300px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface PortIPv6Item {
    index: number;
    ip: string;
    port: string;
    vlan: string;
    desc: string;
    isEditing?: boolean;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      // IPv6协议栈配置表单数据
      const formData = reactive({
        stack: 'disable',
        dns1: '',
        dns2: '',
        route: '',
      });

      // 表单验证规则
      const rules = {
        dns1: [{ required: false, message: 'Preferred DNS可以为空' }],
        dns2: [{ required: false, message: 'Alternative DNS可以为空' }],
        route: [{ required: false, message: 'Default routing可以为空' }],
      };

      // 端口IPv6地址表格列定义
      const portColumns = [
        {
          title: 'IPV6 address',
          dataIndex: 'ip',
          slotName: 'ip',
          align: 'center' as const,
          width: 200,
        },
        {
          title: 'Port',
          dataIndex: 'port',
          slotName: 'port',
          align: 'center' as const,
          width: 80,
        },
        {
          title: 'Vlan',
          dataIndex: 'vlan',
          slotName: 'vlan',
          align: 'center' as const,
          width: 100,
        },
        {
          title: 'Switch',
          dataIndex: 'switch',
          slotName: 'switch',
          align: 'center' as const,
          width: 80,
        },
        {
          title: 'Description',
          dataIndex: 'desc',
          slotName: 'desc',
          align: 'center' as const,
          width: 150,
        },
        {
          title: 'Operate',
          dataIndex: 'operate',
          slotName: 'operate',
          align: 'center' as const,
          width: 200,
        },
      ];

      const portTableData = ref<PortIPv6Item[]>([]);
      const isPortLoading = ref(false);
      const isAddModalVisible = ref(false);
      const isEditModalVisible = ref(false);
      const currentEditRecord = ref<PortIPv6Item | null>(null);

      // 新建表单数据
      const addFormData = reactive({
        ip: '',
        port: '',
        vlan: '',
        desc: '',
      });

      // 编辑表单数据
      const editFormData = reactive({
        ip: '',
        port: '',
        vlan: '',
        desc: '',
      });

      // 新建表单规则
      const addRules = {
        ip: [{ required: true, message: 'IPv6地址不能为空' }],
        port: [{ required: false, message: '端口可以为空' }],
        vlan: [{ required: false, message: 'VLAN可以为空' }],
        desc: [{ required: false, message: '描述可以为空' }],
      };

      // 编辑表单规则
      const editRules = {
        ip: [{ required: true, message: 'IPv6地址不能为空' }],
        port: [{ required: false, message: '端口可以为空' }],
        vlan: [{ required: false, message: 'VLAN可以为空' }],
        desc: [{ required: false, message: '描述可以为空' }],
      };

      const addFormRef = ref();
      const editFormRef = ref();

      // 获取IPv6协议栈配置数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_ipv6_setting.lua',
            new URLSearchParams({ act: 'protocol_stack' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            const {
              stack = 'disable',
              dns1 = '',
              dns2 = '',
              route = '',
              addr,
            } = response.data.data;
            formData.stack = stack;
            formData.dns1 = dns1;
            formData.dns2 = dns2;
            formData.route = route;

            // 处理地址数组
            if (addr && Array.isArray(addr)) {
              portTableData.value = addr.map((item: any, index: number) => ({
                index: index + 1,
                ip: item.ip || '',
                port: item.port || '',
                vlan: item.vlan || '',
                desc: item.desc || '',
                isEditing: false,
              }));
            } else {
              portTableData.value = [];
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // IPv6协议栈开关变化处理
      const handleStackSwitchChange = (value: string) => {
        if (value === 'disable') {
          // 当关闭IPv6协议栈时，清空其他配置
          formData.dns1 = '';
          formData.dns2 = '';
          formData.route = '';
        }
      };

      // 保存IPv6协议栈配置
      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_ipv6_setting.lua',
            new URLSearchParams({
              act: 'protocol_stack',
              act_type: 'mod',
              stack: formData.stack,
              dns1: formData.dns1,
              dns2: formData.dns2,
              route: formData.route,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      // 重置表单
      const resetAction = () => {
        formData.dns1 = '';
        formData.dns2 = '';
        formData.route = '';
        Message.success('重置成功');
      };

      // 显示新建模态框
      const showAddModal = () => {
        addFormData.ip = '';
        addFormData.port = '';
        addFormData.vlan = '';
        addFormData.desc = '';
        isAddModalVisible.value = true;
      };

      // 处理新建确认
      const handleAddConfirm = async (done: any) => {
        try {
          const errors = await addFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          // 添加到表格数据
          const newRecord: PortIPv6Item = {
            index: portTableData.value.length + 1,
            ip: addFormData.ip,
            port: addFormData.port,
            vlan: addFormData.vlan,
            desc: addFormData.desc,
            isEditing: false,
          };
          portTableData.value.push(newRecord);

          Message.success('添加成功');
          done(true);
        } catch (error) {
          console.error('添加失败:', error);
          Message.error('添加请求失败');
          done(false);
        }
      };

      // 处理新建取消
      const handleAddCancel = () => {
        isAddModalVisible.value = false;
      };

      // 编辑记录
      const editRecord = (record: PortIPv6Item) => {
        currentEditRecord.value = record;
        editFormData.ip = record.ip;
        editFormData.port = record.port;
        editFormData.vlan = record.vlan;
        editFormData.desc = record.desc;
        isEditModalVisible.value = true;
      };

      // 处理编辑确认
      const handleEditConfirm = async (done: any) => {
        try {
          const errors = await editFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          if (currentEditRecord.value) {
            currentEditRecord.value.ip = editFormData.ip;
            currentEditRecord.value.port = editFormData.port;
            currentEditRecord.value.vlan = editFormData.vlan;
            currentEditRecord.value.desc = editFormData.desc;
          }

          Message.success('编辑成功');
          done(true);
        } catch (error) {
          console.error('编辑失败:', error);
          Message.error('编辑请求失败');
          done(false);
        }
      };

      // 处理编辑取消
      const handleEditCancel = () => {
        isEditModalVisible.value = false;
        currentEditRecord.value = null;
      };

      // 删除记录
      const deleteRecord = (record: PortIPv6Item, rowIndex: number) => {
        portTableData.value.splice(rowIndex, 1);
        // 重新设置索引
        portTableData.value.forEach((item, index) => {
          item.index = index + 1;
        });
        Message.success('删除成功');
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        rules,
        portColumns,
        portTableData,
        isPortLoading,
        isAddModalVisible,
        isEditModalVisible,
        addFormData,
        editFormData,
        addRules,
        editRules,
        addFormRef,
        editFormRef,
        handleStackSwitchChange,
        saveAction,
        resetAction,
        showAddModal,
        handleAddConfirm,
        handleAddCancel,
        editRecord,
        handleEditConfirm,
        handleEditCancel,
        deleteRecord,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }

  .uniform-form-item {
    margin-bottom: 16px;
  }

  .form-label {
    font-weight: 500;
    color: #1d2129;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
