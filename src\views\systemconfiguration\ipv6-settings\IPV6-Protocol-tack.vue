<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- IPv6协议栈开关配置 -->
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 4 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="ipv6_stack_switch" class="uniform-form-item">
        <template #label>
          <span class="form-label">Start IPV6 protocol stack:</span>
        </template>
        <a-select
          :style="{ width: '100px' }"
          v-model="formData.stack"
          @change="handleStackSwitchChange"
        >
          <a-option value="disable">No</a-option>
          <a-option value="enable">Yes</a-option>
        </a-select>
        <span style="margin-left: 10px; color: #f53f3f; font-size: 12px">
          Need to restart the virtual machine after configuration
        </span>
      </a-form-item>

      <!-- 当开启IPv6协议栈时显示的配置项 -->
      <template v-if="formData.stack === 'enable'">
        <a-form-item :rules="rules.dns1" field="dns1" class="uniform-form-item">
          <template #label>
            <span class="form-label">Preferred IPV6 DNS:</span>
          </template>
          <a-input
            v-model="formData.dns1"
            placeholder="240c:cd22:cd22::8"
            :style="{ width: '300px' }"
          />
        </a-form-item>

        <a-form-item :rules="rules.dns2" field="dns2" class="uniform-form-item">
          <template #label>
            <span class="form-label">Alternative IPV6 DNS:</span>
          </template>
          <a-input
            v-model="formData.dns2"
            placeholder="240c:cd22:cd22::9"
            :style="{ width: '300px' }"
          />
        </a-form-item>

        <a-form-item
          :rules="rules.route"
          field="route"
          class="uniform-form-item"
        >
          <template #label>
            <span class="form-label">IPV6 default routing:</span>
          </template>
          <a-input
            v-model="formData.route"
            placeholder="240c:cd22:cd22::1"
            :style="{ width: '300px' }"
          />
        </a-form-item>

        <a-form-item>
          <a-button
            :disabled="!hasPermission('evrrpSubmit')"
            type="primary"
            @click="saveAction"
          >
            <template #icon>
              <icon-check />
            </template>
            <template #default>Submit</template>
          </a-button>
          <a-button style="margin-left: 10px" @click="resetAction">
            <template #icon>
              <icon-refresh />
            </template>
            <template #default>Reset</template>
          </a-button>
        </a-form-item>
      </template>
    </a-form>

    <!-- 当开启IPv6协议栈时显示的端口IPv6地址表格 -->
    <template v-if="formData.stack === 'enable'">
      <a-divider />

      <!-- 表格标题 -->
      <div style="margin-bottom: 16px">
        <h4 style="margin: 0; color: #1d2129"> The port's IPV6 address </h4>
      </div>

      <!-- IPv6地址配置表格 -->
      <a-table
        row-key="index"
        :columns="portColumns"
        :data="portTableData"
        :pagination="false"
        :bordered="true"
        :loading="isPortLoading"
      >
        <template #ip="{ record, rowIndex }">
          <a-input
            v-if="record.isEditing"
            v-model="record.ip"
            placeholder="请输入IPv6地址"
            :style="{ width: '200px' }"
          />
          <span v-else>{{ record.ip || '-' }}</span>
        </template>

        <template #port="{ record }">
          <a-input
            v-if="record.isEditing"
            v-model="record.port"
            placeholder="请输入端口"
            :style="{ width: '80px' }"
          />
          <span v-else>{{ record.port || '-' }}</span>
        </template>

        <template #vlan="{ record }">
          <a-input
            v-if="record.isEditing"
            v-model="record.vlan"
            placeholder="请输入VLAN"
            :style="{ width: '100px' }"
          />
          <span v-else>{{ record.vlan || '-' }}</span>
        </template>

        <template #switch="{ record }">
          <a-select
            v-if="record.isEditing"
            v-model="record.switch"
            :style="{ width: '80px' }"
          >
            <a-option value="Open">Open</a-option>
            <a-option value="Close">Close</a-option>
          </a-select>
          <span v-else>{{ record.switch || 'Open' }}</span>
        </template>

        <template #desc="{ record }">
          <a-input
            v-if="record.isEditing"
            v-model="record.desc"
            placeholder="请输入描述"
            :style="{ width: '150px' }"
          />
          <span v-else>{{ record.desc || '-' }}</span>
        </template>

        <template #operate="{ record, rowIndex }">
          <a-space>
            <a-button
              v-if="record.isEditing"
              type="primary"
              size="small"
              @click="savePortRecord(record, rowIndex)"
            >
              Save
            </a-button>
            <a-button
              v-else
              type="primary"
              size="small"
              @click="editPortRecord(record, rowIndex)"
            >
              <template #icon>
                <icon-edit />
              </template>
              Edit
            </a-button>
            <a-button
              type="primary"
              status="danger"
              size="small"
              @click="deletePortRecord(record, rowIndex)"
            >
              <template #icon>
                <icon-delete />
              </template>
              Delete
            </a-button>
            <a-button
              v-if="rowIndex === portTableData.length - 1"
              type="primary"
              size="small"
              @click="addPortRecord"
            >
              <template #icon>
                <icon-plus />
              </template>
              Add
            </a-button>
          </a-space>
        </template>
      </a-table>
    </template>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface PortIPv6Item {
    index: number;
    ip: string;
    port: string;
    vlan: string;
    desc: string;
    isEditing?: boolean;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      // IPv6协议栈配置表单数据
      const formData = reactive({
        stack: 'disable',
        dns1: '',
        dns2: '',
        route: '',
      });

      // 表单验证规则
      const rules = {
        dns1: [{ required: false, message: 'Preferred DNS可以为空' }],
        dns2: [{ required: false, message: 'Alternative DNS可以为空' }],
        route: [{ required: false, message: 'Default routing可以为空' }],
      };

      // 端口IPv6地址表格列定义
      const portColumns = [
        {
          title: 'IPV6 address',
          dataIndex: 'ip',
          slotName: 'ip',
          align: 'center' as const,
          width: 200,
        },
        {
          title: 'Port',
          dataIndex: 'port',
          slotName: 'port',
          align: 'center' as const,
          width: 80,
        },
        {
          title: 'Vlan',
          dataIndex: 'vlan',
          slotName: 'vlan',
          align: 'center' as const,
          width: 100,
        },
        {
          title: 'Switch',
          dataIndex: 'switch',
          slotName: 'switch',
          align: 'center' as const,
          width: 80,
        },
        {
          title: 'Description',
          dataIndex: 'desc',
          slotName: 'desc',
          align: 'center' as const,
          width: 150,
        },
        {
          title: 'Operate',
          dataIndex: 'operate',
          slotName: 'operate',
          align: 'center' as const,
          width: 200,
        },
      ];

      const portTableData = ref<PortIPv6Item[]>([]);
      const isPortLoading = ref(false);

      // 获取IPv6协议栈配置数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_ipv6_settings.lua',
            new URLSearchParams({ act: 'protocol_stack' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            const data = response.data.data;
            formData.stack = data.stack || 'disable';
            formData.dns1 = data.dns1 || '';
            formData.dns2 = data.dns2 || '';
            formData.route = data.route || '';

            // 处理地址数组
            if (data.addr && Array.isArray(data.addr)) {
              portTableData.value = data.addr.map(
                (item: any, index: number) => ({
                  index: index + 1,
                  ip: item.ip || '',
                  port: item.port || '',
                  vlan: item.vlan || '',
                  desc: item.desc || '',
                  isEditing: false,
                })
              );
            } else {
              portTableData.value = [];
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // IPv6协议栈开关变化处理
      const handleStackSwitchChange = (value: string) => {
        if (value === 'disable') {
          // 当关闭IPv6协议栈时，清空其他配置
          formData.dns1 = '';
          formData.dns2 = '';
          formData.route = '';
        }
      };

      // 保存IPv6协议栈配置
      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_ipv6_settings.lua',
            new URLSearchParams({
              act: 'protocol_stack',
              act_type: 'mod',
              stack: formData.stack,
              dns1: formData.dns1,
              dns2: formData.dns2,
              route: formData.route,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      // 重置表单
      const resetAction = () => {
        formData.dns1 = '';
        formData.dns2 = '';
        formData.route = '';
        Message.success('重置成功');
      };

      // 编辑端口记录
      const editPortRecord = (record: PortIPv6Item, rowIndex: number) => {
        record.isEditing = true;
      };

      // 保存端口记录
      const savePortRecord = async (record: PortIPv6Item, rowIndex: number) => {
        try {
          // 这里可以添加保存到后端的逻辑
          record.isEditing = false;
          Message.success('保存成功');
        } catch (error) {
          console.error('保存失败:', error);
          Message.error('保存请求失败');
        }
      };

      // 删除端口记录
      const deletePortRecord = async (
        record: PortIPv6Item,
        rowIndex: number
      ) => {
        try {
          portTableData.value.splice(rowIndex, 1);
          // 重新设置索引
          portTableData.value.forEach((item, index) => {
            item.index = index + 1;
          });
          Message.success('删除成功');
        } catch (error) {
          console.error('删除失败:', error);
          Message.error('删除请求失败');
        }
      };

      // 添加端口记录
      const addPortRecord = () => {
        const newRecord: PortIPv6Item = {
          index: portTableData.value.length + 1,
          ip: '',
          port: '',
          vlan: '',
          desc: '',
          isEditing: true,
        };
        portTableData.value.push(newRecord);
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        rules,
        portColumns,
        portTableData,
        isPortLoading,
        handleStackSwitchChange,
        saveAction,
        resetAction,
        editPortRecord,
        savePortRecord,
        deletePortRecord,
        addPortRecord,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }

  .uniform-form-item {
    margin-bottom: 16px;
  }

  .form-label {
    font-weight: 500;
    color: #1d2129;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
