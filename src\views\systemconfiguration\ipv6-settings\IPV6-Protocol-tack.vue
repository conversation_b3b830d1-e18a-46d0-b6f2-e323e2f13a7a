<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- IPv6协议栈开关配置 -->
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 4 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="ipv6_stack_switch" class="uniform-form-item">
        <template #label>
          <span class="form-label">Start IPV6 protocol stack:</span>
        </template>
        <a-select
          :style="{ width: '100px' }"
          v-model="formData.ipv6_stack_switch"
          @change="handleStackSwitchChange"
        >
          <a-option value="No">No</a-option>
          <a-option value="Yes">Yes</a-option>
        </a-select>
        <span style="margin-left: 10px; color: #f53f3f; font-size: 12px">
          Need to restart the virtual machine after configuration
        </span>
      </a-form-item>

      <!-- 当开启IPv6协议栈时显示的配置项 -->
      <template v-if="formData.ipv6_stack_switch === 'Yes'">
        <a-form-item
          :rules="rules.preferred_dns"
          field="preferred_dns"
          class="uniform-form-item"
        >
          <template #label>
            <span class="form-label">Preferred IPV6 DNS:</span>
          </template>
          <a-input
            v-model="formData.preferred_dns"
            placeholder="240c:cd22:cd22::8"
            :style="{ width: '300px' }"
          />
        </a-form-item>

        <a-form-item
          :rules="rules.alternative_dns"
          field="alternative_dns"
          class="uniform-form-item"
        >
          <template #label>
            <span class="form-label">Alternative IPV6 DNS:</span>
          </template>
          <a-input
            v-model="formData.alternative_dns"
            placeholder="240c:cd22:cd22::9"
            :style="{ width: '300px' }"
          />
        </a-form-item>

        <a-form-item
          :rules="rules.default_routing"
          field="default_routing"
          class="uniform-form-item"
        >
          <template #label>
            <span class="form-label">IPV6 default routing:</span>
          </template>
          <a-input
            v-model="formData.default_routing"
            placeholder="240c:cd22:cd22::1"
            :style="{ width: '300px' }"
          />
        </a-form-item>

        <a-form-item>
          <a-button
            :disabled="!hasPermission('evrrpSubmit')"
            type="primary"
            @click="saveAction"
          >
            <template #icon>
              <icon-check />
            </template>
            <template #default>Submit</template>
          </a-button>
          <a-button style="margin-left: 10px" @click="resetAction">
            <template #icon>
              <icon-refresh />
            </template>
            <template #default>Reset</template>
          </a-button>
        </a-form-item>
      </template>
    </a-form>

    <!-- 当开启IPv6协议栈时显示的端口IPv6地址表格 -->
    <template v-if="formData.ipv6_stack_switch === 'Yes'">
      <a-divider />

      <!-- 表格标题 -->
      <div style="margin-bottom: 16px">
        <h4 style="margin: 0; color: #1d2129"> The port's IPV6 address </h4>
      </div>

      <!-- IPv6地址配置表格 -->
      <a-table
        row-key="index"
        :columns="portColumns"
        :data="portTableData"
        :pagination="false"
        :bordered="true"
        :loading="isPortLoading"
      >
        <template #ipv6_address="{ record, rowIndex }">
          <a-input
            v-if="record.isEditing"
            v-model="record.ipv6_address"
            placeholder="请输入IPv6地址"
            :style="{ width: '200px' }"
          />
          <span v-else>{{ record.ipv6_address || '-' }}</span>
        </template>

        <template #port="{ record }">
          <a-select
            v-if="record.isEditing"
            v-model="record.port"
            :style="{ width: '80px' }"
          >
            <a-option value="0">0</a-option>
            <a-option value="1">1</a-option>
            <a-option value="2">2</a-option>
            <a-option value="3">3</a-option>
            <a-option value="4">4</a-option>
          </a-select>
          <span v-else>{{ record.port || '-' }}</span>
        </template>

        <template #vlan="{ record }">
          <a-input
            v-if="record.isEditing"
            v-model="record.vlan"
            placeholder="请输入VLAN"
            :style="{ width: '100px' }"
          />
          <span v-else>{{ record.vlan || '-' }}</span>
        </template>

        <template #switch="{ record }">
          <a-select
            v-if="record.isEditing"
            v-model="record.switch"
            :style="{ width: '80px' }"
          >
            <a-option value="Open">Open</a-option>
            <a-option value="Close">Close</a-option>
          </a-select>
          <span v-else>{{ record.switch || 'Open' }}</span>
        </template>

        <template #description="{ record }">
          <a-input
            v-if="record.isEditing"
            v-model="record.description"
            placeholder="请输入描述"
            :style="{ width: '150px' }"
          />
          <span v-else>{{ record.description || '-' }}</span>
        </template>

        <template #operate="{ record, rowIndex }">
          <a-space>
            <a-button
              v-if="record.isEditing"
              type="primary"
              size="small"
              @click="savePortRecord(record, rowIndex)"
            >
              Save
            </a-button>
            <a-button
              v-else
              type="primary"
              size="small"
              @click="editPortRecord(record, rowIndex)"
            >
              <template #icon>
                <icon-edit />
              </template>
              Edit
            </a-button>
            <a-button
              type="primary"
              status="danger"
              size="small"
              @click="deletePortRecord(record, rowIndex)"
            >
              <template #icon>
                <icon-delete />
              </template>
              Delete
            </a-button>
            <a-button
              v-if="rowIndex === portTableData.length - 1"
              type="primary"
              size="small"
              @click="addPortRecord"
            >
              <template #icon>
                <icon-plus />
              </template>
              Add
            </a-button>
          </a-space>
        </template>
      </a-table>
    </template>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface PortIPv6Item {
    index: number;
    ipv6_address: string;
    port: string;
    vlan: string;
    switch: string;
    description: string;
    isEditing?: boolean;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      // IPv6协议栈配置表单数据
      const formData = reactive({
        ipv6_stack_switch: 'No',
        preferred_dns: '',
        alternative_dns: '',
        default_routing: '',
      });

      // 表单验证规则
      const rules = {
        preferred_dns: [{ required: false, message: 'Preferred DNS可以为空' }],
        alternative_dns: [
          { required: false, message: 'Alternative DNS可以为空' },
        ],
        default_routing: [
          { required: false, message: 'Default routing可以为空' },
        ],
      };

      // 端口IPv6地址表格列定义
      const portColumns = [
        {
          title: 'IPV6 address',
          dataIndex: 'ipv6_address',
          slotName: 'ipv6_address',
          align: 'center' as const,
          width: 200,
        },
        {
          title: 'Port',
          dataIndex: 'port',
          slotName: 'port',
          align: 'center' as const,
          width: 80,
        },
        {
          title: 'Vlan',
          dataIndex: 'vlan',
          slotName: 'vlan',
          align: 'center' as const,
          width: 100,
        },
        {
          title: 'Switch',
          dataIndex: 'switch',
          slotName: 'switch',
          align: 'center' as const,
          width: 80,
        },
        {
          title: 'Description',
          dataIndex: 'description',
          slotName: 'description',
          align: 'center' as const,
          width: 150,
        },
        {
          title: 'Operate',
          dataIndex: 'operate',
          slotName: 'operate',
          align: 'center' as const,
          width: 200,
        },
      ];

      const portTableData = ref<PortIPv6Item[]>([
        {
          index: 1,
          ipv6_address: '240c:cd22:cd22::3/80',
          port: '0',
          vlan: '',
          switch: 'Open',
          description: '',
          isEditing: false,
        },
        {
          index: 2,
          ipv6_address: '2003:da8:2011:2000::1/64',
          port: '0',
          vlan: '',
          switch: 'Open',
          description: '',
          isEditing: false,
        },
        {
          index: 3,
          ipv6_address: '2001:da8:2011:2000::1/64',
          port: '4',
          vlan: '2000',
          switch: 'Open',
          description: '',
          isEditing: false,
        },
        {
          index: 4,
          ipv6_address: '2001:da8:2011:2001::1/64',
          port: '3',
          vlan: '1000',
          switch: 'Open',
          description: '',
          isEditing: false,
        },
        {
          index: 5,
          ipv6_address: '2005:da8:2011:2000::1/64',
          port: '2',
          vlan: '800',
          switch: 'Open',
          description: '',
          isEditing: false,
        },
      ]);
      const isPortLoading = ref(false);

      // 获取IPv6协议栈配置数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_ipv6_settings.lua',
            new URLSearchParams({ act: 'protocol_stack' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.ipv6_stack_switch =
              response.data.data.ipv6_stack_switch || 'No';
            formData.preferred_dns = response.data.data.preferred_dns || '';
            formData.alternative_dns = response.data.data.alternative_dns || '';
            formData.default_routing = response.data.data.default_routing || '';
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // IPv6协议栈开关变化处理
      const handleStackSwitchChange = (value: string) => {
        if (value === 'No') {
          // 当关闭IPv6协议栈时，清空其他配置
          formData.preferred_dns = '';
          formData.alternative_dns = '';
          formData.default_routing = '';
        }
      };

      // 保存IPv6协议栈配置
      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_ipv6_settings.lua',
            new URLSearchParams({
              act: 'protocol_stack',
              act_type: 'mod',
              ipv6_stack_switch: formData.ipv6_stack_switch,
              preferred_dns: formData.preferred_dns,
              alternative_dns: formData.alternative_dns,
              default_routing: formData.default_routing,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      // 重置表单
      const resetAction = () => {
        formData.preferred_dns = '';
        formData.alternative_dns = '';
        formData.default_routing = '';
        Message.success('重置成功');
      };

      // 编辑端口记录
      const editPortRecord = (record: PortIPv6Item, rowIndex: number) => {
        record.isEditing = true;
      };

      // 保存端口记录
      const savePortRecord = async (record: PortIPv6Item, rowIndex: number) => {
        try {
          // 这里可以添加保存到后端的逻辑
          record.isEditing = false;
          Message.success('保存成功');
        } catch (error) {
          console.error('保存失败:', error);
          Message.error('保存请求失败');
        }
      };

      // 删除端口记录
      const deletePortRecord = async (
        record: PortIPv6Item,
        rowIndex: number
      ) => {
        try {
          portTableData.value.splice(rowIndex, 1);
          // 重新设置索引
          portTableData.value.forEach((item, index) => {
            item.index = index + 1;
          });
          Message.success('删除成功');
        } catch (error) {
          console.error('删除失败:', error);
          Message.error('删除请求失败');
        }
      };

      // 添加端口记录
      const addPortRecord = () => {
        const newRecord: PortIPv6Item = {
          index: portTableData.value.length + 1,
          ipv6_address: '',
          port: '0',
          vlan: '',
          switch: 'Open',
          description: '',
          isEditing: true,
        };
        portTableData.value.push(newRecord);
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        rules,
        portColumns,
        portTableData,
        isPortLoading,
        handleStackSwitchChange,
        saveAction,
        resetAction,
        editPortRecord,
        savePortRecord,
        deletePortRecord,
        addPortRecord,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }

  .uniform-form-item {
    margin-bottom: 16px;
  }

  .form-label {
    font-weight: 500;
    color: #1d2129;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
