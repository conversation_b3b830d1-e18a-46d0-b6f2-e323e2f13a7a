<!-- 5G网络接入 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.rate-settings']" />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane
        key="1"
        title="dhcp开关"
        v-if="hasPermission('bindMultiplePort')"
      >
        <dhcpswithch :active="activeTabKey === '1'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane
        key="2"
        title="地址池"
        v-if="hasPermission('bindMultiplePort')"
      >
        <AddrPool :active="activeTabKey === '2'" />
      </a-tab-pane>
      <!-- 标签页3 -->
      <a-tab-pane
        key="3"
        title="IP-地址池"
        v-if="hasPermission('bindMultiplePort')"
      >
        <IPGreaccess :active="activeTabKey === '3'" />
      </a-tab-pane>
      <!-- 标签页4 -->
      <a-tab-pane
        key="4"
        title="IP-地址池"
        v-if="hasPermission('bindMultiplePort')"
      >
        <IPGreaccess :active="activeTabKey === '4'" />
      </a-tab-pane>
      <!-- 标签页5 -->
      <a-tab-pane
        key="5"
        title="IP-地址池"
        v-if="hasPermission('bindMultiplePort')"
      >
        <IPGreaccess :active="activeTabKey === '5'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane
        key="6"
        title="IP-地址池"
        v-if="hasPermission('bindMultiplePort')"
      >
        <IPGreaccess :active="activeTabKey === '6'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane
        key="7"
        title="IP-地址池"
        v-if="hasPermission('bindMultiplePort')"
      >
        <IPGreaccess :active="activeTabKey === '7'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane
        key="8"
        title="IP-地址池"
        v-if="hasPermission('bindMultiplePort')"
      >
        <IPGreaccess :active="activeTabKey === '8'" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  import dhcpswithch from './dhcp-switch.vue';
  import AddrPool from './addr-pool.vue';

  export default defineComponent({
    components: {
      dhcpswithch,
      AddrPool,
    },
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const activeTabKey = ref('1');

      const handleTabChange = (key: string) => {
        activeTabKey.value = key;
      };

      return {
        activeTabKey,
        handleTabChange,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
</style>
