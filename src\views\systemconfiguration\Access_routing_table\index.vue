<!-- 接入路由表组件 -->
<template>
  <div v-if="isComponentVisible" class="container">
    <div v-if="isRefreshing" class="overlay">
      <div class="loader"></div>
    </div>
    <Breadcrumb
      :items="['menu.system-configuration', 'menu.Access_routing_table']"
    />
    <a-card class="general-card" :title="$t('menu.Access_routing_table')">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="{ searchQuery }"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="number" :label="$t('IP地址')">
                  <a-input
                    v-model="searchQuery"
                    :placeholder="$t('searchTable.form.number.placeholder')"
                  />
                  <a-button
                    type="primary"
                    @click="filterData"
                    style="margin-left: 10px"
                  >
                    <template #icon>
                      <icon-search />
                    </template>
                    {{ $t('searchTable.form.search') }}
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-col :flex="'86px'" style="text-align: right"> </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <!-- 新建按钮 -->
            <a-button
              id="accessRoutingTableAdd"
              type="primary"
              :disabled="!hasPermission('accessRoutingTableAdd')"
              @click="showModal"
            >
              <template #icon>
                <icon-plus />
              </template>
              {{ $t('searchTable.operation.create') }}
            </a-button>
            <a-upload action="/">
              <template #upload-button>
                <!-- <a-button>
                  {{ $t('searchTable.operation.import') }}
                </a-button> -->
              </template>
            </a-upload>
          </a-space>
        </a-col>
        <!-- 右侧按钮 -->
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <!-- <a-button>
            <template #icon>
              <icon-download />
            </template>
            {{ $t('searchTable.operation.download') }}
          </a-button> -->
          <a-tooltip :content="$t('searchTable.actions.refresh')">
            <div class="action-icon" @click="handleRefresh">
              <icon-refresh size="18" />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 列表 -->
      <a-table
        :columns="columns5"
        :data="paginatedData"
        style="margin-top: 20px"
        :pagination="false"
      >
        <template #ip="{ record }">
          <span>{{ record.ip }}</span>
        </template>

        <template #mask="{ record }">
          <span>{{ record.mask }}</span>
        </template>

        <template #gateway="{ record }">
          <span>{{ record.gateway }}</span>
        </template>

        <template #option="{ record }">
          <a-button
            id="accessRoutingTableDelete"
            type="primary"
            :disabled="!hasPermission('accessRoutingTableDelete')"
            @click="deleteRow(record)"
          >
            <template #icon>
              <IconEye />
            </template>
            <template #default>删除</template>
          </a-button>
        </template>
      </a-table>
      <a-pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="filteredData.length"
        show-total
        show-size-changer
        show-jumper
        show-page-size
        style="margin-top: 20px"
      />
    </a-card>

    <a-modal
      v-model:visible="isModalVisible"
      title="新建"
      width="40%"
      draggable
      :mask-closable="false"
      :unmount-on-close="false"
      @before-ok="handleOk"
      @cancel="handleCancel"
    >
      <a-col :span="24">
        <a-alert type="info" banner closable>
          <template #icon> </template>
          <template #title
            >说明：接入端口的路由表，发送用户地址的路由。</template
          >
        </a-alert>
        <br />
      </a-col>
      <a-form :model="formData" :rules="rules" ref="formRef">
        <a-form-item label="IP地址" field="ip">
          <a-input v-model="formData.ip" />
        </a-form-item>
        <a-form-item label="子网掩码" field="mask">
          <a-input v-model="formData.mask" />
        </a-form-item>
        <a-form-item label="网关" field="gateway">
          <a-input v-model="formData.gateway" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import {
    handleNotification1,
    handleNotification2,
  } from '../../../utils/info';

  // 定义数据接口
  export interface ExportInfo {
    exportName: string;
    groupId: string;
    portaddresses: string;
  }
  const { hasPermission } = usePermission();

  export default defineComponent({
    setup() {
      // 定义options的类型为OptionValue，明确其结构
      const userStore = useUserStore();
      const formData = reactive({
        ip: '',
        mask: '',
        gateway: '',
      });

      
      const currentPage = ref(1);
      const pageSize = ref(10);

      const paginatedData = computed(() => {
        const start = (currentPage.value - 1) * pageSize.value;
        return filteredData.value.slice(start, start + pageSize.value);
      });

      // 验证IP地址格式
      const validateIpAddress = (ip: string): boolean => {
        const ipRegex =
          /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
      };

      // 验证IP地址格式
      const validateGateway = (gateway: string): boolean => {
        const gatewayRegex =
          /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return gatewayRegex.test(gateway);
      };

      // 验证子网掩码
      const validateSubnetMask = (mask: string): boolean => {
        // 检查是否符合IP地址格式
        if (!validateIpAddress(mask)) {
          return false;
        }

        // 检查是否是有效的子网掩码
        const parts = mask.split('.');
        const binary = parts
          .map((part) => parseInt(part, 10).toString(2).padStart(8, '0'))
          .join('');

        // 有效的子网掩码应该是连续的1后跟连续的0
        return /^1*0*$/.test(binary);
      };

      // 定义columns数组中元素的类型
      const columns5 = [
        {
          title: 'IP地址',
          dataIndex: 'ip',
          slotName: 'ip',
        },
        {
          title: '子网掩码',
          dataIndex: 'mask',
          slotName: 'mask',
        },
        {
          title: '网关',
          dataIndex: 'gateway',
          slotName: 'gateway',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];
      const data = ref<any[]>([]);
      const filteredData = ref<any[]>([]);

      const isModalVisible = ref(false);
      const isComponentVisible = ref(true);
      const isRefreshing = ref(false);
      const searchQuery = ref('');

      const rules = {
        ip: [
          { required: true, message: 'IP地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateIpAddress(value)) {
                callback('IP地址格式不正确，应为：***********');
              } else {
                callback();
              }
            },
          },
        ],
        mask: [
          { required: true, message: '子网掩码不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateSubnetMask(value)) {
                callback('子网掩码格式不正确，应为：*************');
              } else {
                callback();
              }
            },
          },
        ],
        gateway: [
          { required: true, message: '网关不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!validateGateway(value)) {
                callback('网关格式不正确，应为：***********');
              } else {
                callback();
              }
            },
          },
        ],
      };
      const formRef = ref();
      // 获取数据
      // fetchData函数
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'route' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            data.value = response.data.data || [];
            filteredData.value = [...data.value];
          } else {
            console.error('Failed to fetch data:', response.data.err);
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Fetch error:', error);
          Message.error({
            content: '获取数据失败',
            duration: 5000,
          });
        }
      };
      // 删除行
      const deleteRow = async (record: {
        ip: string;
        mask: string;
        gateway: string;
      }) => {
        try {
          if (!hasPermission('accessRoutingTableDelete')) {
            Message.error({
              content: '您没有权限',
              duration: 5000,
            });
            return;
          }
          const { ip, mask, gateway } = record;

          // 发送删除请求
          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'route_del',
              ip,
              mask,
              gateway,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 删除成功后重新获取数据
            await fetchData();
            Message.success('删除成功');
          } else {
            Message.error({
              content: '删除失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error({
            content: '删除失败',
            duration: 5000,
          });
        }
      };

      // 显示模态框
      const showModal = () => {
        if (!hasPermission('accessRoutingTableAdd')) {
          Message.error({
            content: '您没有权限',
            duration: 5000,
          });
          return;
        }
        isModalVisible.value = true;
      };

      // 处理确认
      const handleOk = async (done) => {
        
        if (!hasPermission('accessRoutingTableAdd')) {
          Message.error({
            content: '您没有权限',
            duration: 5000,
          });
          return;
        }
        
        // 只通过表单验证规则验证
        formRef.value.validate((errors) => {
          if (errors) {
            // 表单验证失败
            Message.error('表单验证失败，请检查输入');
            done(false); // 阻止模态框关闭
            return;
          }

          // 表单验证通过后，发送添加请求
          const { ip, mask, gateway } = formData;

          // 发送添加请求
          axios
            .post(
              '/lua/set_cfg.lua',
              new URLSearchParams({
                act: 'route_add',
                ip,
                mask,
                gateway,
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )
            .then((response) => {
              if (response.data.code === 200) {
                fetchData();
                Message.success('添加成功');
                isModalVisible.value = false;
                formData.ip = ''; // 清空表单
                formData.mask = '';
                formData.gateway = '';
                done(true); // 允许模态框关闭
                isModalVisible.value = false;
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });
        return false;
      };
      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      // 刷新数据
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 过滤数据
      const filterData = () => {
        const rawData = data.value;

        if (searchQuery.value.trim() === '') {
          filteredData.value = [...rawData];
          Message.error('请填写用户地址');
          return;
        }

        filteredData.value = rawData.filter((item) =>
          item.ip.includes(searchQuery.value)
        );

        if (filteredData.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      // 组件加载时获取数据
      onMounted(() => {
        fetchData();
      });

      return {
        columns5,
        data,
        filteredData,
        isModalVisible,
        formData,
        rules,
        formRef,
        showModal,
        handleOk,
        handleCancel,
        isComponentVisible,
        handleRefresh,
        isRefreshing,
        searchQuery,
        filterData,
        deleteRow,
        hasPermission,     
        paginatedData,
        currentPage,
        pageSize,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }

  .arco-alert-with-title {
    padding: 0px 5px;
    text-align: center;
    justify-content: center;
    align-items: center;
  }
</style>
