<!-- 系统状态信息 -->
<template>
  <a-spin :loading="loading" style="width: 100%">
    <a-card
      class="general-card"
      :header-style="{ paddingBottom: '0' }"
      :body-style="{ padding: '17px 20px 21px 20px' }"
    >
      <template #title>
        {{ $t('systemstate_information') }}
      </template>
      <!-- <template #extra>
        <a-link>{{ $t('workplace.viewMore') }}</a-link>
      </template> -->
      <a-space direction="vertical" :size="10" fill>        
        <a-table
          :data="renderList"
          :pagination="false"
          :bordered="false"
          :scroll="{ x: '100%', y: '264px' }"
        >
          <template #columns>
            <!-- <a-table-column title="排名" data-index="key"></a-table-column> -->
            <a-table-column title="描述" data-index="describe">
              <template #cell="{ record }">
                <a-typography-paragraph
                  :ellipsis="{
                    rows: 1,
                  }"
                >
                  {{ record.describe }}
                </a-typography-paragraph>
              </template>
            </a-table-column>
            <a-table-column title="数值" data-index="data">
              <template #cell="{ record }">
                <a-typography-paragraph
                  :ellipsis="{
                    rows: 1,
                  }"
                >
                  {{ record.data }}
                </a-typography-paragraph>
              </template>
            </a-table-column>
            <!-- <a-table-column
              title="日涨幅"
              data-index="increases"
              :sortable="{
                sortDirections: ['ascend', 'descend'],
              }"
            >
              <template #cell="{ record }">
                <div class="increases-cell">
                  <span>{{ record.increases }}%</span>
                  <icon-caret-up
                    v-if="record.increases !== 0"
                    style="color: #f53f3f; font-size: 8px"
                  />
                </div>
              </template>
            </a-table-column> -->
          </template>
        </a-table>
      </a-space>
    </a-card>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import axios from 'axios';
  import useLoading from '@/hooks/loading';
  import type { TableData } from '@arco-design/web-vue/es/table/interface';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const type = ref('system');
  const { loading, setLoading } = useLoading();
  const renderList = ref<TableData[]>();

  const fetchData = async (contentType: string) => {
    try {
      setLoading(true);
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({ act: 'sys_info' }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data.code === 200) {
        renderList.value = response.data.data;
      } else {
        console.error(
          'Error fetching data:',
          response.data ? response.data.msg : 'No data received'
        );
      }
    } catch (err) {
      console.error('Error fetching system info:', err);
    } finally {
      setLoading(false);
    }
  };

  const typeChange = (contentType: string) => {
    fetchData(contentType);
  };

  onMounted(() => {
    fetchData('system');
  });
</script>

<style scoped lang="less">
  .general-card {
    min-height: 395px;
  }
  :deep(.arco-table-tr) {
    height: 44px;
    .arco-typography {
      margin-bottom: 0;
    }
  }
  .increases-cell {
    display: flex;
    align-items: center;
    span {
      margin-right: 4px;
    }
  }
</style>
