import { RouteLocationNormalized, RouteRecordRaw } from 'vue-router';
import { useUserStore } from '@/store';

export default function usePermission() {
  const userStore = useUserStore();

  // 检查是否有访问路由的权限
  const accessRouter = (route: RouteLocationNormalized | RouteRecordRaw) => {
    // 如果路由不需要认证，或者没有设置权限要求，则允许访问
    if (!route.meta?.requiresAuth) return true;

    // 如果路由设置了权限ID，则检查用户是否有该权限
    if (route.meta?.permissions) {
      const requiredPermissions = route.meta.permissions as string[];
      const userPermissions = userStore.permissions || [];

      // 检查用户是否拥有所需的权限（至少一个）
      return requiredPermissions.some((permission) =>
        userPermissions.includes(permission)
      );
    }

    return true;
  };

  // 检查是否有特定操作的权限
  const hasPermission = (permissionId: string) => {
    const userPermissions = userStore.permissions || [];
    return userPermissions.includes(permissionId);
  };

  return {
    accessRouter,
    hasPermission,
    findFirstPermissionRoute(_routers: any, role = 'admin') {
      const cloneRouters = [..._routers];
      while (cloneRouters.length) {
        const firstElement = cloneRouters.shift();
        if (
          firstElement?.meta?.roles?.find((el: string[]) => {
            return el.includes('*') || el.includes(role);
          })
        )
          return { name: firstElement.name };
        if (firstElement?.children) {
          cloneRouters.push(...firstElement.children);
        }
      }
      return null;
    },
    // You can add any rules you want
  };
}
