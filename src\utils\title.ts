import axios from 'axios';

// 从API获取标题
export const fetchTitleFromApi = async (): Promise<string | null> => {
  try {
    const response = await axios.post(
      '/lua/project_title.lua',
      new URLSearchParams({
        act: 'get',
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    if (response.data.code === 200) {
      return response.data.data || null;
    }
    return null;
  } catch (error) {
    console.error('获取网页标题失败:', error);
    return null;
  }
};

// 获取并设置网页标题
export const loadAndSetAppTitle = async (): Promise<void> => {
  // 首先从本地存储中获取保存的标题
  const savedTitle = localStorage.getItem('app_project_title');
  if (savedTitle) {
    // 如果存在保存的标题，直接使用
    document.title = savedTitle;
    return;
  }

  // 如果没有保存的标题，尝试从API获取
  const apiTitle = await fetchTitleFromApi();
  if (apiTitle) {
    document.title = apiTitle;
    // 保存到本地存储
    localStorage.setItem('app_project_title', apiTitle);
  }
};

// 设置新的网页标题并保存
export const setAndSaveAppTitle = (title: string): void => {
  document.title = title;
  localStorage.setItem('app_project_title', title);
};
