import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Message, Modal } from '@arco-design/web-vue';
import { useUserStore } from '@/store';
import { getToken, clearToken } from '@/utils/auth';

export interface HttpResponse<T = unknown> {
  status: number;
  msg: string;
  code: number;
  data: T;
}

if (import.meta.env.VITE_API_BASE_URL) {
  axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;
}

axios.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // let each request carry token
    // this example using the JWT token
    // Authorization is a custom headers key
    // please modify it according to the actual situation
    const token = getToken();
    if (token) {
      if (!config.headers) {
        config.headers = {};
      }
      // config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    // do something
    return Promise.reject(error);
  }
);
// add response interceptors
axios.interceptors.response.use(
  (response) => {
    // 检查响应类型，确保不对特定类型的响应进行不适当的处理
    if (
      response.config.responseType === 'blob' ||
      response.headers['content-type']?.includes('blob')
    ) {
      return response;
    }

    try {
      // 对于 JSON 响应，尝试解析 JSON
      if (
        typeof response.data === 'string' &&
        response.headers['content-type']?.includes('application/json')
      ) {
        response.data = JSON.parse(response.data);
      }
    } catch (error) {
      console.error('Error parsing response data:', error);
      return response;
    }

    // 检查是否是登录错误
    const responseData = response.data;
    if (
      // 精确匹配后端返回的登录错误格式
      (responseData &&
        responseData.err_login === '请登录！' &&
        responseData.code === 0) ||
      // 保留其他可能的认证错误情况
      (responseData && responseData.code === 401) ||
      // 保留其他可能的登录错误情况
      (responseData &&
        responseData.code === -1 &&
        typeof responseData.result === 'string' &&
        responseData.result.includes('登录'))
    ) {
      console.log('检测到登录失效，即将跳转到登录页');
      Message.error('登录已过期，请重新登录');

      // 清除用户相关的所有数据
      const userStore = useUserStore();
      if (userStore) {
        userStore.logoutCallBack();
      } else {
        // 如果无法获取userStore，至少清除token
        clearToken();
        localStorage.removeItem('userState');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userId');
      }

      // 跳转到登录页
      window.location.href = '/#/login';
      return Promise.reject(new Error('请登录'));
    }

    // 不在拦截器中自动抛出业务错误，由具体业务代码处理
    return response;
  },
  (error) => {
    // 处理网络错误或服务器错误
    if (error.response) {
      // 服务器返回了错误状态码
      if (error.response.status === 401) {
        Message.error('认证失败，请重新登录');
        // 清除用户相关的所有数据
        const userStore = useUserStore();
        if (userStore) {
          userStore.logoutCallBack();
        } else {
          clearToken();
        }

        // 跳转到登录页
        window.location.href = '/#/login';
      } else if (error.response.status === 403) {
        Message.error('您没有权限访问此资源');
      } else {
        Message.error(`请求错误: ${error.message}`);
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      Message.error('服务器无响应，请稍后重试');
    } else {
      // 请求配置出错
      Message.error(`请求异常: ${error.message}`);
    }
    return Promise.reject(error);
  }
);
