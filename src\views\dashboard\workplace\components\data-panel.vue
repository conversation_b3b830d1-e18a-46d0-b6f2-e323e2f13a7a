<template>
  <a-grid :cols="24" :row-gap="16" class="panel">
    <a-grid-item
      class="panel-col"
      :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 8, xxl: 6 }"
    >
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/288b89194e657603ff40db39e8072640.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          :title="$t('entrance_Receptionrate')"
          :value="entranceReceiveRate"
          :value-from="0"
          animation
          show-group-separator
        >
          <!-- <template #suffix>
            W+ <span class="unit">{{ $t('') }}</span>
          </template> -->
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
      class="panel-col"
      :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 8, xxl: 6 }"
    >
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/fdc66b07224cdf18843c6076c2587eb5.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          :title="$t('entrance_Transferrate')"
          :value="entranceTransferRate"
          :value-from="0"
          animation
          show-group-separator
        >
          <!-- <template #suffix>
            <span class="unit">{{ $t('workplace.pecs') }}</span>
          </template> -->
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
      class="panel-col"
      :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 8, xxl: 6 }"
    >
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/77d74c9a245adeae1ec7fb5d4539738d.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          :title="$t('export_Receptionrate')"
          :value="exitReceiveRate"
          :value-from="0"
          animation
          show-group-separator
        >
          <!-- <template #suffix>
            <span class="unit">{{ $t('workplace.pecs') }}</span>
          </template> -->
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
      class="panel-col"
      :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }"
      style="border-right: none"
    >
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/c8b36e26d2b9bb5dbf9b74dd6d7345af.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          :title="$t('export_Transferrate')"
          :value="exitTransferRate"
          :value-from="0"
          animation
        >
          <!-- <template #suffix> % <icon-caret-up class="up-icon" /> </template> -->
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item :span="24">
      <a-divider class="panel-border" />
    </a-grid-item>
  </a-grid>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import axios from 'axios';
  import useLoading from '@/hooks/loading';
  import type { TableData } from '@arco-design/web-vue/es/table/interface';

  const type = ref('system');
  const { loading, setLoading } = useLoading();
  const renderList = ref<TableData[]>();

  const entranceReceiveRate = ref();
  const entranceTransferRate = ref();
  const exitReceiveRate = ref();
  const exitTransferRate = ref();

  const fetchData = async (contentType: string) => {
    try {
      setLoading(true);
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({ act: 'sys_info' }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data.code === 200) {
        const allData = response.data.data;
        let filteredData = [];

        filteredData = allData.filter((item: any) =>
          [
            '入口接收速率(Kbits/s)',
            '入口转送速率(Kbits/s)',
            '出口接收速率(Kbits/s)',
            '出口转送速率(Kbits/s)',
          ].includes(item.describe)
        );

        renderList.value = filteredData;

        // 提取并更新统计数据，确保数据是数字类型
        entranceReceiveRate.value = Number(
          allData.find((item: any) => item.describe === '入口接收速率(Kbits/s)')
            ?.data || 0
        );
        entranceTransferRate.value = Number(
          allData.find((item: any) => item.describe === '入口转送速率(Kbits/s)')
            ?.data || 0
        );
        exitReceiveRate.value = Number(
          allData.find((item: any) => item.describe === '出口接收速率(Kbits/s)')
            ?.data || 0
        );
        exitTransferRate.value = Number(
          allData.find((item: any) => item.describe === '出口转送速率(Kbits/s)')
            ?.data || 0
        );
      } else {
        console.error(
          'Error fetching data:',
          response.data ? response.data.msg : 'No data received'
        );
      }
    } catch (err) {
      console.error('Error fetching system info:', err);
    } finally {
      setLoading(false);
    }
  };

  const typeChange = (contentType: string) => {
    fetchData(contentType);
  };

  onMounted(() => {
    fetchData('system');
  });
</script>

<style lang="less" scoped>
  .arco-grid.panel {
    margin-bottom: 0;
    padding: 16px 20px 0 20px;
  }
  .panel-col {
    padding-left: 43px;
    border-right: 1px solid rgb(var(--gray-2));
  }
  .col-avatar {
    margin-right: 12px;
    background-color: var(--color-fill-2);
  }
  .up-icon {
    color: rgb(var(--red-6));
  }
  .unit {
    margin-left: 8px;
    color: rgb(var(--gray-8));
    font-size: 12px;
  }
  :deep(.panel-border) {
    margin: 4px 0 0 0;
  }
</style>
