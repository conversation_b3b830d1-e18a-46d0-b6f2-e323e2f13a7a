import axios from 'axios';
import type { RouteRecordNormalized } from 'vue-router';
import { UserState } from '@/store/modules/user/types';
import md5 from 'md5';

export interface LoginData {
  userid: string;
  password: string;
}

export interface LoginRes {
  code: number;
  data: {
    userid?: string;
    [key: string]: any;
  };
  result: string;
}

export interface MenuResponse {
  code: number;
  data: {
    menu?: any[];
    [key: string]: any;
  };
  result: string;
}

export function login(data: LoginData) {
  return axios
    .post<LoginRes>(
      '/lua/login.lua',
      new URLSearchParams({
        userid: data.userid,
        password: md5(data.password),
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    )
    .then((response) => {
      console.log('原始登录响应:', response);
      return response.data;
    });
}

// export function logout() {
//   return axios.post<LoginRes>('/lua/logout.lua', null, {
//     headers: {
//       'Content-Type': 'application/x-www-form-urlencoded',
//     },
//   });
// }

// export function getUserInfo() {
//   return axios.post<UserState>('/lua/user_info.lua', null, {
//     headers: {
//       'Content-Type': 'application/x-www-form-urlencoded',
//     },
//   });
// }

// export function getMenuList() {
//   return axios.post<RouteRecordNormalized[]>('/api/user/menu');
// }

// 获取指定管理员账号的菜单
export function getMenuByUserId(userId: string) {
  return axios
    .post<MenuResponse>(
      '/lua/permission.lua',
      new URLSearchParams({
        act: 'get_menu_by_userid',
        userid: userId,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.error('getMenuByUserId错误:', error);
      throw error;
    });
}

// // 直接调试permission接口
// export function debugPermission() {
//   const userId = localStorage.getItem('userId');
//   if (!userId) {
//     return Promise.reject(new Error('未找到用户ID'));
//   }

//   return axios
//     .post(
//       '/lua/permission.lua',
//       new URLSearchParams({
//         act: 'get_menu_by_userid',
//         userid: userId,
//       }),
//       {
//         headers: {
//           'Content-Type': 'application/x-www-form-urlencoded',
//         },
//       }
//     )
//     .then((response) => {
//       console.log('调试permission接口响应:', response);
//       return response;
//     })
//     .catch((error) => {
//       console.error('调试permission接口错误:', error);
//       throw error;
//     });
// }
