<!-- 下载上传配置组件 -->
<template>
  <div class="container">
    <Breadcrumb
      :items="['menu.system-control', 'menu.Download_Upload_Configuration']"
    />
    <a-card title="下载上传配置">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-col :span="24">
          <a-card :bordered="false" :style="{ width: '100%' }">
            <template #extra> </template>
            <div class="session">
              <div class="form-item">
                <span>cfg配置文件：</span>
                <!-- 修改为原生 input 标签 -->
                <input
                  ref="fileInput"
                  style="width: 200px"
                  class="input-field"
                  :model-value="upgradeFileName"
                  type="file"
                  accept=".cfg"
                  @change="handleFileChange"
                />
                <a-button
                  id="downloadUploadConfigImport"
                  :disabled="!hasPermission('downloadUploadConfigImport')"
                  type="primary"
                  @click="submitData"
                  >导入</a-button
                >
                <a-button type="primary" @click="triggerFileOutput"
                  >导出</a-button
                >
              </div>

              <div class="form-item">
                <span>系统程序启动日志：</span>
                <a-button
                  id="downloadUploadConfigExport"
                  :disabled="!hasPermission('downloadUploadConfigExport')"
                  type="primary"
                  @click="triggerFileInput"
                  >导出</a-button
                >
              </div>
              <a-textarea
                style="width: 80%; height: 50vh; margin-left: 10%"
                placeholder="Please enter something"
                allow-clear
                v-model="formData.log"
              />
            </div>
          </a-card>
        </a-col>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { ref } from 'vue';
  import { defineComponent, reactive, onMounted } from 'vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { Message } from '@arco-design/web-vue';
  import {
    handleNotification4,
    handleNotification1,
    handleNotification2,
    handleNotification3,
  } from '../../../utils/info';

  interface ApiResponse {
    code: number;
    result?: string;
    err?: string;
  }

  const { hasPermission } = usePermission();

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const formData = reactive({
        log: '',
      });

      const upgradeFile = ref<File | null>(null);
      const upgradeFileName = ref('');

      // 处理文件选择
      const handleFileChange = (event: Event) => {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];
        if (file) {
          upgradeFile.value = file;
          upgradeFileName.value = file.name;
          console.log('选择的文件是：', file.name);
        }
      };

      const submitData = async () => {
        if (!upgradeFile.value) {
          Message.error('请选择要上传的配置文件');
          return;
        }
        if (!hasPermission('downloadUploadConfigImport')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const formDataToSend = new FormData();
          formDataToSend.append('file', upgradeFile.value);

          const response = await axios.post<ApiResponse>(
            '/lua/import_cfg.lua',
            formDataToSend,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(
              response.data.result || '上传成功，请重启虚机使生效！'
            );
          } else {
            Message.error(response.data.err || '上传失败');
          }
        } catch (error) {
          console.error('Error setting data:', error);
          Message.error('提交失败，请稍后重试');
        }
      };

      onMounted(async () => {
        try {
          const response = await axios.post(
            '/lua/system_info.lua',
            new URLSearchParams({ act: 'vm_log' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            formData.log = response.data.data;
          } else {
            console.error('Failed to fetch data:', response.data);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      });

      // 引用文件输入框
      const fileInput = ref<HTMLInputElement | null>(null);

      // 触发文件选择
      const triggerFileInput = () => {
        if (!hasPermission('downloadUploadConfigExport')) {
          Message.error('您没有权限');
          return;
        }
        // 导出日志内容
        const content = formData.log; // 使用textarea中的日志内容
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        // 创建一个隐藏的 a 标签用于触发下载
        const link = document.createElement('a');
        link.href = url;
        link.download = 'init_vm.log'; // 导出日志文件的名称
        link.click();

        // 清理 URL 对象
        URL.revokeObjectURL(url);
      };

      // 导出文件逻辑
      const triggerFileOutput = async () => {
        if (!hasPermission('downloadUploadConfigExport')) {
          Message.error('您没有权限');
          return;
        }
        try {
          // 向服务器请求cfg配置文件
          const response = await axios.post(
            '/lua/download.lua',
            new URLSearchParams({ act: 'cfg_file' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              responseType: 'blob', // 指定响应类型为blob
            }
          );

          // 使用默认文件名，不依赖于content-disposition头
          const filename = 'config.cfg';

          // 创建下载链接
          const blob = new Blob([response.data]);
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = filename;
          link.click();

          // 清理URL对象
          URL.revokeObjectURL(url);

          Message.success('配置文件导出成功');
        } catch (error) {
          console.error('下载配置文件失败:', error);
          Message.error('配置文件导出失败，请稍后重试');
        }
      };

      return {
        handleNotification4,
        handleNotification2,
        formData,
        triggerFileOutput,
        handleFileChange,
        upgradeFileName,
        triggerFileInput,
        onUpgradeFileChange: handleFileChange,
        submitData,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .session {
    width: 80%;
    margin-left: 10%;
    padding: 20px;
    background-color: #fff;
    margin-bottom: 20px;
  }

  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px; /* 设置子元素之间的间距 */
    margin-left: 25%;
  }

  span {
    flex-shrink: 0;
    font-size: 20px;
  }

  .hint {
    color: red;
    font-size: 20px;
  }

  .input-field {
    flex-shrink: 0;
    width: 150px; /* 调整输入框的宽度 */
  }

  a-button {
    flex-shrink: 0;
  }

  .button {
    margin-top: 20px;
    text-align: center;
  }
</style>
