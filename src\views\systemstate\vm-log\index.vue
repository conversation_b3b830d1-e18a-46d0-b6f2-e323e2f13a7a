<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.vm-log']" />
    <a-card :style="{ width: '100%' }" title="vBase系统日志信息">
      <a-row style="margin-bottom: 16px">

            <!-- 右侧按钮 -->
        <a-col
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip :content="$t('searchTable.actions.refresh')">
            <div class="action-icon" @click="handleRefresh">
              <icon-refresh size="18" />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 加载状态 -->
      <a-spin v-if="loading" />
      <a-empty v-else-if="vmData.length === 0" />
      
      <div v-for="(line, index) in vmData" :key="index" class="log-line">
        {{ line }}
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, reactive, onMounted, computed } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  
  export default defineComponent({
    setup() {
      const vmData = ref([]);
      const loading = ref(true);
  
      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/system_info.lua',
            new URLSearchParams({ act: 'vm_log' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {   
            vmData.value = response.data.data;
          } else {
            console.error(
              'Error fetching data:',
              response.data ? response.data.msg : 'No data received'
            );
          }
        } catch (error) {
          console.error('Error fetching port info:', error);
        } finally {
          loading.value = false;
        }
      };

      const handleRefresh = () => {
        fetchData().finally(() => {
          Message.success('刷新成功');
        });
      };

      onMounted(() => {
        fetchData();       
      });

      return {
        vmData,
        loading,
        handleRefresh,
      };
    },
  });

</script>

<style scoped>
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .session,
  .session2 {
    text-align: left;
    background-color: #fff;
    padding: 5px;
    white-space: nowrap;
  }

  .session {
    margin-bottom: 20px;
  }
  span {
    font-size: 20px;
  }
</style>
