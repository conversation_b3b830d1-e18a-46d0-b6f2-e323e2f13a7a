<!-- 出口状态组件 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.oeclient-session']" />
    
    <div class="export">
      <div class="export2">
        <a-row>
          <a-col :flex="1">
            <a-form
              :model="{ searchQuery }"
              :label-col-props="{ span: 6 }"
              :wrapper-col-props="{ span: 18 }"
              label-align="left"
            >
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item field="name" label="账号名">
                    <a-input
                      v-model="searchQuery"
                      :placeholder="$t('searchTable.form.number.placeholder')"
                    />
                    <a-button
                      type="primary"
                      @click="filterData"
                      style="margin-left: 10px"
                    >
                      <template #icon>
                        <icon-search />
                      </template>
                      {{ $t('searchTable.form.search') }}
                    </a-button>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-col>
          <a-col :flex="'86px'" style="text-align: right"> </a-col>
        </a-row>
        <a-divider style="margin-top: 0" />
        <a-row style="margin-bottom: 16px">
          <!-- 右侧按钮 -->
          <a-col
            style="display: flex; align-items: center; justify-content: end"
          >
            <a-tooltip :content="$t('searchTable.actions.refresh')">
              <div class="action-icon" @click="handleRefresh">
                <icon-refresh size="18" />
              </div>
            </a-tooltip>
          </a-col>
        </a-row>
          
        <a-table
          :columns="columns"
          :data="paginatedData"
          style="margin-top: 20px"
          :pagination="false"
        >
          <template #name="{ record }">
            <span>{{ record.name }}</span>
          </template>

          <template #ip="{ record }">
            <span>{{ record.ip }}</span>
          </template>
        </a-table>
        
        <a-pagination
          v-model:current="currentPage"
          v-model:page-size="pageSize"
          :total="filteredData.length"
          show-total
          show-size-changer
          show-jumper
          show-page-size
          style="margin-top: 20px"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, computed } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';

  interface ExportData {
    name: string;
    ip: number;
  }
  
  export default defineComponent({
    setup() {
      const columns = [
        {
          title: '账号名',
          dataIndex: 'name',
          slotName: 'name',
        },
        {
          title: 'IP地址',
          dataIndex: 'ip',
          slotName: 'ip',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];

      const data = ref<ExportData[]>([]);
      const filteredData = ref<ExportData[]>([]);

      const searchQuery = ref('');
      const currentPage = ref(1);
      const pageSize = ref(10);

      const paginatedData = computed(() => {
        const start = (currentPage.value - 1) * pageSize.value;
        return filteredData.value.slice(start, start + pageSize.value);
      });

      // 获取所有数据
      const fetchData = async () => {
        try {          
          const response = await axios.post(
            '/lua/system_info.lua',
            new URLSearchParams({ act: 'oeclient' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          // console.log('Full response:', response);
          // console.log('Response data:', response.data);

          // 使用 response.status 检查 HTTP 状态码
          if (response.data.code === 200) {
            data.value =
              response.data.data.map((item: any) => ({
                name: item.name || '',
                ip: item.dialIp || '',
              })) || [];
            
            filteredData.value = [...data.value];
          } else {
            console.error(
              'Error fetching data:',
              response.data ? response.data.msg : 'No data received'
            );
          }
        } catch (error) {
          console.error('Fetch error:', error);
          Message.error('获取数据失败');
        } 
      };

      const handleRefresh = () => {
        fetchData().then(() => {
          Message.success('刷新成功');
        });
      };

      const filterData = () => {
        if (searchQuery.value.trim() === '') {
          filteredData.value = [...data.value];
          Message.error('请填写账号名');
          return;
        }

        filteredData.value = data.value.filter((item) =>
          item.name.includes(searchQuery.value)
        );

      };

      onMounted(() => {
        fetchData();
      });

      return {
        columns,
        data,
        filteredData,
        handleRefresh,
        searchQuery,
        filterData,
        paginatedData,
        currentPage,
        pageSize,
      };
    },
  });
      
</script>

<style scoped>
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }
  .export,
  .export2 {
    background-color: #fff;
    padding: 5px;
  }

  .export {
    background-color: #fff;
    margin-bottom: 20px;
  }
</style>
