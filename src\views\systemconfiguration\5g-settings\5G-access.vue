<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 3 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="work" class="uniform-form-item">
        <template #label>
          <span class="form-label">功能开关：</span>
        </template>
        <a-tooltip
          content="可实时配置"
          position="tl"
          background-color="#3491FA"
        >
          <a-select :style="{ width: '100px' }" v-model="formData.work">
            <a-option value="disable">关闭</a-option>
            <a-option value="enable">开启</a-option>
          </a-select>
        </a-tooltip>
      </a-form-item>
      <a-form-item
        :rules="rules.map_head"
        field="map_head"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">映射v4地址头：</span>
        </template>
        <a-input
          v-model="formData.map_head"
          placeholder=""
          :style="{ width: '200px' }"
        />
      </a-form-item>

      <a-form-item
        :rules="rules.map_tail"
        field="map_tail"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label"> 映射v4地址尾：</span>
        </template>
        <a-tooltip
          content="地址头到地址尾范围最大0xffff"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.map_tail"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>

      <a-form-item field="map6_tail" class="uniform-form-item">
        <template #label>
          <span class="form-label"> 映射v6地址头：</span>
        </template>
        <a-input
          v-model="formData.map6_tail"
          placeholder=""
          :style="{ width: '200px' }"
        />
      </a-form-item>

      <a-form-item field="map6_head" class="uniform-form-item">
        <template #label>
          <span class="form-label"> 映射v6地址尾：</span>
        </template>
        <a-tooltip
          content=" 地址头到地址尾范围最大0xffff"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.map6_head"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>提交</template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';
  import message from '@/utils/message';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      const formData = reactive({
        work: 'disable',
        map_head: '',
        map_tail: '',
        map6_head: '',
        map6_tail: '',
      });
      const rules = {
        map_head: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        map_tail: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
      };
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_5g_net_gw.lua',
            new URLSearchParams({ act: 'net_5g' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.map_head = response.data.data.map_head || '';
            formData.map_tail = response.data.data.map_tail || '';
            formData.map6_head = response.data.data.map6_head || '';
            formData.map6_tail = response.data.data.map6_tail || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          // Arco Design表单验证失败时会返回errors对象
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'net_5g',
              act_type: 'mod',
              map_head: String(formData.map_head),
              map_tail: String(formData.map_tail),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        saveAction,
        hasPermission,
        rules,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
