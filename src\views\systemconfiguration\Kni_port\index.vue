<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.Kni_port']" />

    <a-card title="Kni端口">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-col :span="8">
          <a-card title="Kni端口" :bordered="false" :style="{ width: '100%' }">
            <a-alert type="info" banner closable>
              <template #title>
                <span style="font-size: 15px"
                  >注意：配置后需要重新启动虚机才能生效
                </span>
              </template>
            </a-alert>
            <br />
            <div class="session">
              <div class="form-item">
                <span>kni端口：</span>
                <a-switch v-model="switchValue" @change="handleSwitchChange" />
                <span class="status-text">{{
                  switchValue ? '启动' : '关闭'
                }}</span>
                <span class="hint"></span>
              </div>
              <br />
              <div class="form-item">
                <span>kni虚拟端口地址：</span>
                <a-input v-model="formData.ip" class="input-field" />
                <span class="hint"></span>
              </div>
              <br />
              <div class="form-item">
                <span>kni虚拟端口掩码：</span>
                <a-input v-model="formData.mask" class="input-field" />
                <span class="hint"></span>
              </div>
              <br />
              <div class="form-item">
                <span>主机端口地址：</span>
                <a-input v-model="formData.peer" class="input-field" />
                <span class="hint"></span>
              </div>
              <br />
              <div class="button">
                <a-button
                  id="kniPortSubmit"
                  type="primary"
                  :disabled="!hasPermission('kniPortSubmit')"
                  @click="handleNotification1"
                >
                  <template #icon>
                    <icon-check />
                  </template>
                  <template #default>提交</template>
                </a-button>

                <a-button
                  type="secondary"
                  style="margin-left: 10px"
                  @click="handleNotification2"
                >
                  <template #icon>
                    <icon-refresh />
                  </template>
                  <template #default>重置</template>
                </a-button>
              </div>
            </div>
          </a-card>
        </a-col>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, onMounted, ref, computed } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import {
    handleNotification4,
    handleNotification2,
  } from '../../../utils/info';

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formData = reactive({
        kni_work: '',
        ip: '',
        mask: '',
        peer: '',
      });

      // 添加开关值
      const switchValue = ref(false);

      // 处理开关变化
      const handleSwitchChange = (value: boolean) => {
        formData.kni_work = value ? 'enable' : 'disable';
      };

      onMounted(async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'kni' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            formData.kni_work = response.data.data.kni_work;
            // 设置开关初始值
            switchValue.value = formData.kni_work === 'enable';
            formData.ip = response.data.data.ip;
            formData.mask = response.data.data.mask;
            formData.peer = response.data.data.peer;
          } else {
            console.error('Failed to fetch data:', response.data);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      });

      const submitData = async () => {
        try {
          if (!hasPermission('kniPortSubmit')) {
            Message.error('您没有权限');
            return;
          }
          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'kni',
              kni_work: formData.kni_work,
              ip: formData.ip,
              mask: formData.mask,
              peer: formData.peer,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            Message.success(response.data.data.result);
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Error setting data:', error);
        }
      };

      return {
        handleNotification4,
        handleNotification1: submitData, // 更新按钮点击事件处理函数
        handleNotification2,
        formData,
        switchValue,
        handleSwitchChange,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .hint {
    flex: 1; /* 占据剩余空间 */
    text-align: left;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }

  /* 为上传按钮添加居中样式 */
  .upload {
    display: block;
    margin: 0 auto;
  }

  /* 为按钮添加居中样式 */
  .button {
    text-align: center;
  }

  /* 开关状态文本样式 */
  .status-text {
    margin-left: 10px;
    font-size: 14px;
  }

  .arco-input-wrapper {
    margin: 10px 0 !important;
  }
  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
</style>
