<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.port']" />
    <a-card title="">
      <a-table
        :columns="columns"
        :data="tableData"
        :bordered="true"
        :pagination="false"
      />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import axios from 'axios';

  const tableData = ref([]);
  const columns = ref([
    // 默认列（可选）
    { title: '端口ID', dataIndex: '端口ID' }
  ]);

  onMounted(async () => {
    try {
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({ act: 'port_info' }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data?.code === 200) {
        const formattedData: Record<string, any>[] = [];
        const columnSet = new Set<string>(); // 用于去重列名
        
        let currentPortData: Record<string, any> = {};
        
        response.data.data.forEach((item) => {
          if (item.describe === '端口ID' && Object.keys(currentPortData).length > 0) {
            formattedData.push(currentPortData);
            currentPortData = {};
          }
          
          if (item.describe && item.data !== undefined) {
            currentPortData[item.describe] = item.data;
            columnSet.add(item.describe);
          }
        });
        
        // 添加最后一条数据
        if (Object.keys(currentPortData).length > 0) {
          formattedData.push(currentPortData);
        }
        
        // 生成列配置
        columns.value = Array.from(columnSet).map(key => ({
          title: key,
          dataIndex: key,
          key: key // 建议添加唯一key
        }));
        
        tableData.value = formattedData;
      } else {
        console.error('Error:', response.data?.err || 'No data');
      }
    } catch (error) {
      console.error('API error:', error);
    }
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .a-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
</style>
