<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.evrrp-port-status']" />

    <a-card>
      <a-row :gutter="80">
        <a-col :span="8"
          ><label>监视主路由线路断线：{{ monitor_master_down }}</label></a-col
        >
        <a-col :span="8"
          ><label>工作模式：{{ model }}</label></a-col
        >
      </a-row>
    </a-card>

    <a-card>
      <!-- <template #title>
          evrrp 虚机端口状态
        </template> -->
      <div>
        <a-space direction="vertical" size="large" fill>
          <a-descriptions title="evrrp 虚机端口状态"></a-descriptions>
          <a-descriptions
            v-for="item of data"
            title=""
            :column="{ xs: 1, md: item.length, lg: item.length }"
            bordered
          >
            <a-descriptions-item v-for="i of item" :label="i.label">
              {{ i.value }}
            </a-descriptions-item>
          </a-descriptions>
        </a-space>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import axios from 'axios';

  const data = ref([]);
  const model = ref('');
  const monitor_master_down = ref('');

  onMounted(async () => {
    try {
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({ act: 'evrrp_state' }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data?.code === 200) {
        model.value = response.data.data.model == 0 ? '独立' : '相互作用';
        monitor_master_down.value =
          response.data.data.monitor_master_down == 0 ? '无' : '有';

        const formattedData: Record<string, any>[] = [];
        let currentPortData: Record<string, any> = [];

        for (let i = 0; i < response.data.data.port.length; i++) {
          let vid = response.data.data.vid[i] ? response.data.data.vid[i] : '';
          let port = response.data.data.port[i]
            ? response.data.data.port[i]
            : '';
          let vlan = response.data.data.vlan[i]
            ? response.data.data.vlan[i]
            : '';
          let role = response.data.data.role[i]
            ? response.data.data.role[i]
            : '';
          let netStat = response.data.data.netStat[i]
            ? response.data.data.netStat[i]
            : '';
          let linkStat = response.data.data.linkStat[i]
            ? response.data.data.linkStat[i]
            : '';
          let times = response?.data?.data?.times?.[i] ?? '';

          currentPortData = [];
          currentPortData.push({ label: 'evrrp协议路由标识', value: vid });
          if (port != '')
            currentPortData.push({ label: '虚机端口', value: port });
          if (vlan != '')
            currentPortData.push({ label: '虚机vlan', value: vlan });
          currentPortData.push({ label: '当前角色', value: role });
          if (netStat != '')
            currentPortData.push({ label: '网络状态', value: netStat });
          if (linkStat != '')
            currentPortData.push({ label: '链路状态', value: linkStat });
          if (times != '')
            currentPortData.push({ label: '失效时间', value: times });

          formattedData.push(currentPortData);
        }

        data.value = formattedData;
        // console.log(formattedData)
      } else {
        console.error('Error:', response.data?.err || 'No data');
      }
    } catch (error) {
      console.error('API error:', error);
    }
  });
</script>

<style scoped lang="less">
  @media (max-width: 768px) {
    .card-container {
      --card-width: 100%;
    }
  }
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .a-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  .card-demo {
    width: 360px;
    margin-left: 24px;
    transition-property: all;
  }
  .card-demo:hover {
    transform: translateY(-4px);
  }
</style>
