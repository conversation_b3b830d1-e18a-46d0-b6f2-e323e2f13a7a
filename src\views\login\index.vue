<template>
  <div class="container">
    <div class="background-image"></div>
    <div class="content">
      <div class="content-inner">
        <LoginForm />
      </div>
      <div class="footer">
        <Footer />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import Footer from '@/components/footer/index.vue';
  import { useUserStore } from '@/store';
  import { useRouter } from 'vue-router';
  import { loadAndSetAppTitle } from '@/utils/title';
  import { onMounted } from 'vue';
  import LoginForm from './components/login-form.vue';
  import LoginBanner from './components/banner.vue';

  const userStore = useUserStore();
  const router = useRouter();

  // 在登录页面加载时立即设置标题
  onMounted(() => {
    loadAndSetAppTitle();
  });

  const handleSubmit = async () => {
    try {
      // 登录成功后获取用户权限
      console.log("login-----------");
      await userStore.fetchUserPermissions();

      // 跳转到首页或其他页面
      const { redirect, ...othersQuery } = router.currentRoute.value.query;
      router.push({
        name: (redirect as string) || 'Workplace',
        query: {
          ...othersQuery,
        },
      });
    } catch (err) {
      // 处理错误
    }
  };
</script>

<style lang="less" scoped>
  .container {
    display: flex;
    height: 100vh;
    position: relative;

    .background-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('../../assets/images/image.png');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      z-index: 0;
    }

    .content {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 1;

      .content-inner {
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
    }

    .footer {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 100%;
      z-index: 1;
    }
  }

  .logo {
    position: fixed;
    top: 24px;
    left: 22px;
    z-index: 1;
    display: inline-flex;
    align-items: center;

    &-text {
      margin-right: 4px;
      margin-left: 4px;
      color: var(--color-fill-1);
      font-size: 20px;
    }
  }
</style>

<style lang="less" scoped>
  // responsive
  @media (max-width: @screen-lg) {
    .container {
      .banner {
        width: 25%;
      }
    }
  }
</style>
