import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const VIRTUALMACHINESETTINGS: AppRouteRecordRaw = {
  path: '/Virtual_Machine_Settings',
  name: 'Virtual_Machine_Settings',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.Virtual_Machine_Settings',
    permissions: ['vmSettings'],
    icon: 'icon-file',
    requiresAuth: true,
    order: 4,
  },
  children: [
    {
      path: 'network_port',
      name: 'network_port ',
      component: () =>
        import('@/views/Virtual_Machine_Settings/network_port/index.vue'),
      meta: {
        locale: 'menu.basic_settings',
        icon: 'icon-share-alt',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    // {
    //   path: 'CPU_resources',
    //   name: 'CPU_resources ',
    //   component: () =>
    //     import('@/views/Virtual_Machine_Settings/CPU_resources/index.vue'),
    //   meta: {
    //     locale: 'menu.CPU_resources',
    //     icon: 'icon-mobile',
    //     requiresAuth: true,
    //     roles: ['admin'],
    //   },
    // },
    // {
    //   path: 'Memory_Channel',
    //   name: 'Memory_Channel ',
    //   component: () =>
    //     import('@/views/Virtual_Machine_Settings/Memory_Channel/index.vue'),
    //   meta: {
    //     locale: 'menu.Memory_Channel',
    //     icon: 'icon-mind-mapping',
    //     requiresAuth: true,
    //     roles: ['admin'],
    //   },
    // },

    // {
    //   path: 'Memory_row_and_column',
    //   name: 'Memory_row_and_column ',
    //   component: () =>
    //     import(
    //       '@/views/Virtual_Machine_Settings/Memory_row_and_column/index.vue'
    //     ),
    //   meta: {
    //     locale: 'menu.Memory_row_and_column',
    //     icon: 'icon-nav',
    //     requiresAuth: true,
    //     roles: ['admin'],
    //   },
    // },
  ],
};

export default VIRTUALMACHINESETTINGS;
