<!-- 审计设置组件 -->
<template>
  <div class="container">
    <Breadcrumb
      :items="[
        'menu.system-configuration',
        'menu.Reverse_mapping_audit_settings',
      ]"
    />

    <a-card title="审计设置">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-col :span="8">
          <a-card title="审计设置" :bordered="false" :style="{ width: '100%' }">
            <template #extra> </template>
            <div class="session">
              <div class="form-item">
                <span>网络审计:&nbsp;&nbsp;</span>
                <a-switch
                  v-model="netSwitchValue"
                  @change="handleNetSwitchChange"
                />
                <span class="status-text">{{
                  netSwitchValue ? '开启' : '关闭'
                }}</span>
                <span class="hint"></span>
              </div>
              <br />
              <div class="form-item">
                <span>URL审计：</span>
                <a-switch
                  v-model="urlSwitchValue"
                  @change="handleUrlSwitchChange"
                />
                <span class="status-text">{{
                  urlSwitchValue ? '开启' : '关闭'
                }}</span>
                <span class="hint"></span>
              </div>
              <br />
              <div class="form-item">
                <span>审计服务器地址：</span>
                <a-tooltip
                  content="不填或0.0.0.0表示不启动该功能"
                  position="tr"
                  background-color="#3491FA"
                >
                  <a-input
                    v-model="formData.log_srv_addr"
                    class="input-field"
                    :status="logSrvAddrError ? 'error' : undefined"
                  />
                </a-tooltip>
                <div v-if="logSrvAddrError" class="error-message">{{
                  logSrvAddrError
                }}</div>
              </div>
              <br />
              <div class="button">
                <a-button
                  id="auditSettingsSubmit"
                  :disabled="!hasPermission('auditSettingsSubmit')"
                  type="primary"
                  @click="handleNotification1"
                >
                  <template #icon>
                    <icon-check />
                  </template>
                  <template #default>提交</template>
                </a-button>

                <a-button
                  type="secondary"
                  style="margin-left: 10px"
                  @click="handleNotification2"
                >
                  <template #icon>
                    <icon-refresh />
                  </template>
                  <template #default>重置</template>
                </a-button>
              </div>
            </div>
          </a-card>
        </a-col>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import {
    handleNotification4,
    handleNotification1,
    handleNotification2,
  } from '../../../utils/info';

  export default defineComponent({
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formData = reactive({
        log_net: '',
        log_url: '',
        log_srv_addr: '',
      });

      // 添加错误状态
      const logSrvAddrError = ref('');

      // 添加开关状态值
      const netSwitchValue = ref(false);
      const urlSwitchValue = ref(false);

      // 验证IP地址格式
      const validateIpAddress = (ip: string): boolean => {
        if (!ip || ip === '0.0.0.0') return true; // 特殊情况，不填或0.0.0.0表示不启动该功能
        const ipRegex =
          /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
      };

      // 处理网络审计开关变化
      const handleNetSwitchChange = (value: boolean) => {
        formData.log_net = value ? 'enable' : 'disable';
      };

      // 处理URL审计开关变化
      const handleUrlSwitchChange = (value: boolean) => {
        formData.log_url = value ? 'enable' : 'disable';
      };

      onMounted(async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'audit' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            formData.log_net = response.data.data.log_net;
            formData.log_url = response.data.data.log_url;
            formData.log_srv_addr = response.data.data.log_srv_addr;

            // 根据获取的数据设置开关状态
            netSwitchValue.value = formData.log_net === 'enable';
            urlSwitchValue.value = formData.log_url === 'enable';
          } else {
            console.error('Failed to fetch data:', response.data);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      });

      // 校验所有字段
      const validateAllFields = (): boolean => {
        let isValid = true;

        // 验证审计服务器地址
        if (
          formData.log_srv_addr &&
          !validateIpAddress(formData.log_srv_addr)
        ) {
          logSrvAddrError.value = 'IP地址格式不正确，请重新输入';
          isValid = false;
        } else {
          logSrvAddrError.value = '';
        }

        return isValid;
      };

      // 将设置数据的逻辑移动到一个方法中
      const submitData = async () => {
        try {
          if (!hasPermission('auditSettingsSubmit')) {
            Message.error('您没有权限');
            return;
          }

          // 在提交前验证所有字段
          if (!validateAllFields()) {
            Message.error({
              content: '请修正表单中的错误再提交',
              duration: 5000,
            });
            return;
          }

          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'audit',
              log_net: formData.log_net,
              log_url: formData.log_url,
              log_srv_addr: formData.log_srv_addr,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            Message.success(response.data.data.result);
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Error setting data:', error);
        }
      };

      // 重置函数
      const resetForm = () => {
        formData.log_net = 'disable';
        formData.log_url = 'disable';
        formData.log_srv_addr = '';
        // 同步更新开关状态
        netSwitchValue.value = false;
        urlSwitchValue.value = false;
        // 清除错误状态
        logSrvAddrError.value = '';
      };

      return {
        handleNotification4,
        handleNotification1: submitData, // 更新按钮点击事件处理函数
        handleNotification2: resetForm, // 更新重置按钮事件处理函数
        formData,
        netSwitchValue,
        urlSwitchValue,
        handleNetSwitchChange,
        handleUrlSwitchChange,
        hasPermission,
        logSrvAddrError,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .hint {
    flex: 1; /* 占据剩余空间 */
    text-align: left;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }

  /* 为上传按钮添加居中样式 */
  .upload {
    display: block;
    margin: 0 auto;
  }

  /* 为按钮添加居中样式 */
  .button {
    text-align: center;
  }

  .status-text {
    margin-left: 10px;
    font-size: 14px;
  }
  .arco-input-wrapper {
    margin: 10px 0 !important;
  }

  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>
