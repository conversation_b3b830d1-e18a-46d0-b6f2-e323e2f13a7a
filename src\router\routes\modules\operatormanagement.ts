import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const OPERATORMANAGEMENT: AppRouteRecordRaw = {
  path: '/Operator_management',
  name: 'Operator_management',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.Operator management',
    icon: 'icon-user',
    permissions: ['operatorManagement', 'operationPermissions'],
    requiresAuth: true,
    order: 6,
  },
  children: [
    {
      path: 'Operator_Configuration',
      name: 'Operator_Configuration',
      component: () =>
        import('@/views/Operator_management/Operator_Configuration/index.vue'),
      meta: {
        locale: 'menu.Operator Configuration',
        icon: 'icon-settings',
        permissions: ['operators'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },

    {
      path: 'Change_Password',
      name: 'Change_Password',
      component: () =>
        import('@/views/Operator_management/Change_Password/index.vue'),
      meta: {
        locale: 'menu.Change_Password',
        icon: 'icon-safe',
        requiresAuth: true,
        hideInMenu: true,
      },
    },

    {
      path: 'Role_Permission',
      name: 'Role_Permission',
      component: () =>
        import('@/views/Operator_management/Role_Permission/index.vue'),
      meta: {
        locale: 'menu.Role_Permission',
        icon: 'icon-tag',
        permissions: ['operationPermissions'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },

    {
      path: 'project_title',
      name: 'project_title',
      component: () =>
        import('@/views/Operator_management/project_title/index.vue'),
      meta: {
        locale: 'menu.project title',
        icon: 'icon-tag',
        permissions: ['projectTitle'],
        requiresAuth: true,
        roles: ['admin'],
      },
    },
  ],
};

export default OPERATORMANAGEMENT;
