<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.chart']" />
    <a-card title="">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '10px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <!-- 日期选择框 -->
        <a-select
          v-model="selectedDate"
          placeholder="选择日期"
          style="width: 10%; justify-content: center"
          @change="fetchChartData"
        >
          <a-option v-for="date in dateOptions" :key="date" :value="date">
            {{ date }}
          </a-option>
        </a-select>

        <div
          ref="sessionChartRef"
          class="chart-item"
          style="width: 100%; height: 400px"
        ></div>
        <div
          ref="rateChartRef"
          class="chart-item"
          style="width: 100%; height: 400px"
        ></div>
        <div
          ref="cpuChartRef"
          class="chart-item"
          style="width: 100%; height: 400px"
        ></div>
        <!-- 动态渲染所有出口图表 -->
        <div
          v-for="(portName, index) in dataNames"
          :key="index"
          :ref="(el) => (portChartRefs[index] = el)"
          class="chart-item"
          style="width: 100%; height: 400px"
        ></div>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, nextTick } from 'vue';
  import axios from 'axios';
  import * as echarts from 'echarts';

  const sessionChartRef = ref(null);
  const rateChartRef = ref(null);
  const cpuChartRef = ref(null);
  const portChartRefs = ref([null]); // 用于存储所有的出口图表容器
  const dataNames = ref([null]); // 用于存储所有的出口图表名字

  // 获取当前日期，格式化为YYYYMMDD
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  };

  // 生成当前日期和前6天的日期
  const generateDateOptions = (): string[] => {
    const dates: string[] = [];
    const today = new Date();

    for (let i = 0; i < 7; i += 1) {
      const date = new Date();
      date.setDate(today.getDate() - i);
      dates.push(formatDate(date));
    }

    return dates;
  };

  const selectedDate = ref(formatDate(new Date())); // 默认为当前日期
  const dateOptions = ref<string[]>(generateDateOptions()); // 生成包括当前日期在内的前7天日期

  const fetchData = async (date: string) => {
    try {
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({
          act: 'draw_info',
          show_time: date, // 使用传入的日期
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      
      // 获取出口图表数据
      const dataOutName = response.data.data.res_port || [];
      dataNames.value = dataOutName;

      const initSessionChart = () => {
        const sessionChart = echarts.init(sessionChartRef.value);
        const dataTime = response.data.data.data_value.data_time || [];
        const sessionDataArray = response.data.data.data_value.session || [];

        sessionChart.clear();
        sessionChart.setOption({
          title: {
            text: '用户信息',
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            data: response.data.data.resu[0].legend || [],
          },
          xAxis: {
            type: 'category',
            data: dataTime,
          },
          yAxis: {
            type: 'value',
          },
          series:
            sessionDataArray && sessionDataArray.length > 0
              ? sessionDataArray.map((sessionItem, index) => ({
                  name: response.data.data.resu[0].legend[index] || '',
                  type: 'line',
                  data: sessionItem || [],
                  symbol: 'none',
                }))
              : [],
        });
      };

      const initRateChart = () => {
        const rateChart = echarts.init(rateChartRef.value);
        const dataTime = response.data.data.data_value.data_time || [];
        const rateDataArray = response.data.data.data_value.rate || [];

        rateChart.clear();
        rateChart.setOption({
          title: {
            text: '流速信息',
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            data: response.data.data.resu[1]?.legend || [],
          },
          xAxis: {
            type: 'category',
            data: dataTime,
          },
          yAxis: {
            type: 'value',
          },
          series:
            rateDataArray && rateDataArray.length > 0
              ? rateDataArray.map((rateItem, index) => ({
                  name: response.data.data.resu[1]?.legend[index] || '',
                  type: 'line',
                  data: rateItem || [],
                  symbol: 'none',
                }))
              : [],
        });
      };
      
      const initCpuChart = () => {
        const cpuChart = echarts.init(cpuChartRef.value);
        const dataTime = response.data.data.data_value.data_time || [];
        const rateDataArray = response.data.data.data_value.cpu || [];

        cpuChart.clear();
        cpuChart.setOption({
          title: {
            text: 'CPU空载运行',
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            data: response.data.data.resu[2]?.legend || [],
          },
          xAxis: {
            type: 'category',
            data: dataTime,
          },
          yAxis: {
            type: 'value',
          },
          series:
            rateDataArray && rateDataArray.length > 0
              ? rateDataArray.map((rateItem, index) => ({
                  name: response.data.data.resu[2]?.legend[index] || '',
                  type: 'line',
                  data: rateItem || [],
                  symbol: 'none',
                }))
              : [],
        });
      };

      const initPortChart = () => {
        nextTick(() => {
          // 使用 dataNames 渲染所有图表
          portChartRefs.value.forEach((outChartDiv, index) => {
            const portChart = echarts.init(outChartDiv);
            const portName = dataNames.value[index].title;
            const dataTime = response.data.data.data_value.data_time || [];
            const portData = response.data.data.data_value.port || {};
           
            portChart.clear();
            portChart.setOption({
              title: {
                text: `${portName || ''}`,
              },
              tooltip: {
                trigger: 'axis',
              },
              legend: {
                data: response.data.data.res_port[1]?.legend || [],
              },
              xAxis: {
                type: 'category',
                data: dataTime,
              },
              yAxis: {
                type: 'value',
              },
              series:
                portData[index] && portData[index].length > 0
                  ? portData[index].map((data, seriesIndex) => ({
                      name:
                       response.data.data.res_port[1]?.legend[seriesIndex] ||
                       '',
                      type: 'line',
                      data: data || [],
                      symbol: 'none',
                    }))
                  : [],
            });
          });
        });
      };

      if (response.data.code === 200) {
        initSessionChart();
        initRateChart();
        initCpuChart();
        initPortChart();
      } else {
        console.error('获取数据失败:', response.data);
      }
    } catch (error) {
      console.error('获取数据时出错:', error);
    }
  };

  const fetchChartData = (date: string) => {
    fetchData(date); // 使用传入的日期调用 fetchData
  };

  const onDateChange = (date: string) => {
    fetchData(date); // 当选择日期改变时，重新获取数据
  };

  onMounted(() => {
    nextTick(() => {
      fetchData(selectedDate.value); // 默认使用选中的日期获取数据
    });
  });
</script>

<style scoped>
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .chart-item {
    width: 100%;
    height: 400px;
    background-color: #fff;
    justify-content: center;
    text-align: center;
    display: flex;
    margin-bottom: 8px;
  }
</style>
