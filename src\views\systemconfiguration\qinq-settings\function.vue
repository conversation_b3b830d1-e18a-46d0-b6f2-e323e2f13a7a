<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="work" class="uniform-form-item">
          <template #label>
            <span class="form-label">qinq绑mac功能</span>
          </template>
          <a-select :style="{ width: '100px' }" v-model="formData.work">
            <a-option value="disable">关闭</a-option>
            <a-option value="enable">开启</a-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <a-button
      :disabled="!hasPermission('evrrpSubmit')"
      type="primary"
      style="margin-top: 2%"
      @click="saveAction"
    >
      <template #icon>
        <icon-check />
      </template>
      <template #default>提交</template>
    </a-button>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      const formData = reactive({
        work: '',
        chk_interval: '',
        chk_interva: '',
        chk_over_bw: '',
        ctrl_bw: '',
        ctrl_length: '',
      });

      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_spec_bw_ctrl.lua',
            new URLSearchParams({ act: 'chk_base' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.chk_interval = response.data.data.chk_interval || '';
            formData.chk_over_bw = response.data.data.chk_over_bw || '';
            formData.ctrl_bw = response.data.data.ctrl_bw || '';
            formData.ctrl_length = response.data.data.ctrl_length || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        try {
          if (!hasPermission('evrrpSubmit')) {
            Message.error('您没有权限');
            return;
          }

          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'mod',
              chk_interval: String(formData.chk_interval),
              chk_over_bw: String(formData.chk_over_bw),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        saveAction,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .form-row {
    margin-bottom: 16px;
  }

  .uniform-form-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .form-label {
    display: inline-block;
    margin-bottom: 2px;
    white-space: normal;
    word-break: break-word;
    width: 100%;
  }

  .uniform-form-item >>> .arco-form-item-wrapper {
    width: 100%;
  }

  /* 中屏幕及以上时改为水平布局 */
  @media (min-width: 768px) {
    .uniform-form-item {
      flex-direction: row;
      align-items: center;
    }

    .form-label {
      width: 160px;
      text-align: right;
      padding-right: 1px;
      margin-bottom: 0;
    }
  }

  /* 大屏幕时调整标签宽度 */
  @media (min-width: 1200px) {
    .form-label {
      width: 180px;
    }
  }

  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>
