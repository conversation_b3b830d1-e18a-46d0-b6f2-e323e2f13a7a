<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.port-settings']" />
    <a-tabs v-model:activeKey="activeTabKey" @tab-click="handleTabChange">
      <a-tab-pane
        key="1"
        title="绑定多个端口"
        v-if="hasPermission('bindMultiplePort')"
      >
        <BindMultPort :active="activeTabKey === '1'" />
      </a-tab-pane>
      <!-- 标签页2 -->
      <a-tab-pane key="2" title="端口地址" v-if="hasPermission('portAddress')">
        <PortAddress :active="activeTabKey === '2'" />
      </a-tab-pane>
      <!-- 标签页3 -->
      <a-tab-pane key="3" title="端口备份" v-if="hasPermission('evrrp')">
        <Evrrp :active="activeTabKey === '3'" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  import BindMultPort from './bind_mult_port.vue';
  import PortAddress from './port_address.vue';
  import Evrrp from './evrrp.vue';

  export default defineComponent({
    components: {
      BindMultPort,
      PortAddress,
      Evrrp,
    },
    setup() {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const activeTabKey = ref('1');

      const handleTabChange = (key: string) => {
        activeTabKey.value = key;
      };

      return {
        activeTabKey,
        handleTabChange,
        hasPermission,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
  }
</style>
