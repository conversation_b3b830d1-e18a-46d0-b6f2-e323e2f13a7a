<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="base_name" class="uniform-form-item">
          <template #label>
            <span class="form-label">base名字</span>
          </template>
          <a-tooltip content="不填缺省为Base" position="tl" background-color="#3491FA">
            <a-input v-model="formData.base_name" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="idle_timeout" class="uniform-form-item">
          <template #label>
            <span class="form-label">闲置超时</span>
          </template>
          <a-tooltip content="单位：秒，不填缺省为180" position="tl" background-color="#3491FA">
            <a-input-number v-model="formData.idle_timeout" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="max_idle_disc_num" class="uniform-form-item">
          <template #label>
            <span class="form-label">断线个数</span>
          </template>
          <a-tooltip content="每秒断线个数受控制，不填缺省为512" position="tl" background-color="#3491FA">
            <a-input-number v-model="formData.max_idle_disc_num" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="active_flow" class="uniform-form-item">
          <template #label>
            <span class="form-label">最低活动流量</span>
          </template>
          <a-tooltip content="低于该流量则认为用户处于闲置状态。不填缺省为0，单位：字节" position="tl" background-color="#3491FA">
            <a-input-number v-model="formData.active_flow" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="max_init_ses" class="uniform-form-item">
          <template #label>
            <span class="form-label">最大非认证会话数</span>
          </template>
          <a-tooltip content="不填缺省为4096" position="tl" background-color="#3491FA">
            <a-input-number v-model="formData.max_init_ses" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="acct_alive_interval" class="uniform-form-item">
          <template #label>
            <span class="form-label">定时发送计费Alive包</span>
          </template>
          <a-tooltip content="0表示不发送。不填缺省为0，单位秒" position="tl" background-color="#3491FA">
            <a-input-number v-model="formData.acct_alive_interval" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="init_idle_timeout" class="uniform-form-item">
          <template #label>
            <span class="form-label">init会话闲置断开时间</span>
          </template>
          <a-tooltip content="不填缺省为180秒" position="tl" background-color="#3491FA">
            <a-input-number v-model="formData.init_idle_timeout" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="mac_query_address" class="uniform-form-item">
          <template #label>
            <span class="form-label">查询用户MAC dhcp server地址</span>
          </template>
          <a-tooltip content="三层无感知使用，0.0.0.0表示删除该功能" position="tl" background-color="#3491FA">
            <a-input v-model="formData.mac_query_address" placeholder="" :status="dhcpIpError ? 'error' : undefined" />
            <div v-if="dhcpIpError" class="error-message">{{ dhcpIpError }}</div>
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="icmp_reply_limit" class="uniform-form-item">
          <template #label>
            <span class="form-label">本机回应icmp数据包每秒最大个数</span>
          </template>
          <a-tooltip content="不填缺省为0" position="tl" background-color="#3491FA">
            <a-input-number v-model="formData.icmp_reply_limit" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="icmp_forward_limit" class="uniform-form-item">
          <template #label>
            <span class="form-label">本机转发icmp每秒最大个数</span>
          </template>
          <a-tooltip content="不填缺省为0" position="tl" background-color="#3491FA">
            <a-input-number v-model="formData.icmp_forward_limit" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="def_split_pvlan" class="uniform-form-item">
          <template #label>
            <span class="form-label">剥离vlan端口-外层vlan</span>
          </template>
          <a-tooltip content="不填缺省为0" position="tl" background-color="#3491FA">
            <a-input-number v-model="formData.def_split_pvlan" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="def_split_cvlan" class="uniform-form-item">
          <template #label>
            <span class="form-label">剥离vlan端口-内层vlan</span>
          </template>
          <a-tooltip content="不填缺省为0" position="tl" background-color="#3491FA">
            <a-input-number v-model="formData.def_split_cvlan" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" class="form-row">
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item label="" field="snmp_com" class="uniform-form-item">
          <template #label>
            <span class="form-label">SNMP通信ID</span>
          </template>
          <a-tooltip content="不填缺省：amnoon" position="tl" background-color="#3491FA">
            <a-input v-model="formData.snmp_com" placeholder="" />
          </a-tooltip>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="12" :md="12" :lg="8">
        <a-form-item field="alog_srv" class="uniform-form-item">
          <template #label>
            <span class="form-label">远端日志服务器地址</span>
          </template>
          <a-tooltip content="0.0.0.0 表示不配置" position="tl" background-color="#3491FA">
            <a-input v-model="formData.alog_srv" placeholder=""  :status="logIpError ? 'error' : undefined" />
            <div v-if="logIpError" class="error-message">{{ logIpError }}</div>
          </a-tooltip>
        </a-form-item>
      </a-col>
    </a-row>
    
    <a-button :disabled="!hasPermission('BasicConfigurationSubmit')" type="primary" style="margin-top: 2%"
      @click="saveAction">
      <template #icon>
        <icon-check />
      </template>
      <template #default>提交</template>
    </a-button>
  </a-card>

</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed,
    watch
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from "@/utils/validate";

  export default defineComponent({
    props: {
      active: Boolean
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      const formData = reactive({
        base_name: 'Base',
        idle_timeout: 180,
        max_idle_disc_num: 512,
        active_flow: 0,
        max_init_ses: 4096,
        acct_alive_interval: 0,
        init_idle_timeout: 180,
        mac_query_address: '',
        icmp_reply_limit: 0,
        icmp_forward_limit: 0,
        def_split_pvlan: 0,
        def_split_cvlan: 0,
        snmp_com: 'amnoon',
        alog_srv: ''
      });
      
      const dhcpIpError = ref('');
      const logIpError = ref('');
         
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_basic_setting.lua',
            new URLSearchParams({ act: 'basic_setting' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          // console.log(response);
          
          if (response.data.code === 200) {
            formData.base_name = response.data.data.base_name || 'Base';
            formData.idle_timeout = Number(response.data.data.idle_timeout) || 180;  
            formData.max_idle_disc_num = Number(response.data.data.max_idle_disc_num) || 512;
            formData.active_flow = Number(response.data.data.active_flow) || 0;
            formData.max_init_ses = Number(response.data.data.max_init_ses) || 4096;
            formData.acct_alive_interval = Number(response.data.data.acct_alive_interval) || 0;
            formData.init_idle_timeout = Number(response.data.data.init_idle_timeout) || 180;
            formData.mac_query_address = response.data.data.mac_query_address || '';
            formData.icmp_reply_limit = Number(response.data.data.icmp_reply_limit) || 0;
            formData.icmp_forward_limit = Number(response.data.data.icmp_forward_limit) || 0;
            formData.def_split_pvlan = Number(response.data.data.def_split_pvlan) || 0;
            formData.def_split_cvlan = Number(response.data.data.def_split_cvlan) || 0;
            formData.snmp_com = response.data.data.snmp_com || 'amnoon';
            formData.alog_srv = response.data.data.alog_srv || '';                      
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        try {
          if (!hasPermission('BasicConfigurationSubmit')) {
            Message.error('您没有权限');
            return;
          }  
          if (formData.mac_query_address && !isValidIPv4(formData.mac_query_address)) {
            dhcpIpError.value = 'IP地址格式不正确，应如：*******';
            return false;
          } else {
            dhcpIpError.value = '';
          }     
          if (formData.alog_srv && !isValidIPv4(formData.alog_srv)) {
            logIpError.value = 'IP地址格式不正确，应如：*******';
            return false;
          } else {
            logIpError.value = '';
          }         
          
          const response = await axios.post(
            '/lua/set_cfg_basic_setting.lua',
            new URLSearchParams({
              act: 'basic_setting',
              base_name: formData.base_name,
              idle_timeout: String(formData.idle_timeout),
              active_flow: String(formData.active_flow),
              max_init_ses: String(formData.max_init_ses),
              max_idle_disc_num: String(formData.max_idle_disc_num),
              acct_alive_interval: String(formData.acct_alive_interval),
              mac_query_address: formData.mac_query_address,
              init_idle_timeout: String(formData.init_idle_timeout),
              icmp_reply_limit: String(formData.icmp_reply_limit),
              icmp_forward_limit: String(formData.icmp_forward_limit),
              def_split_pvlan: String(formData.def_split_pvlan),
              def_split_cvlan: String(formData.def_split_cvlan),
              snmp_com: formData.snmp_com,
              alog_srv: formData.alog_srv,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };
      
      watch(() => props.active, (newVal) => {
        if (newVal) {
          fetchData();
        }
      });
      
      onMounted(() => {
        if (props.active) {
          fetchData();
        }        
      });

      return {
        formData,
        saveAction,
        hasPermission,
        dhcpIpError,
        logIpError,
      };
    },
  });
</script>

<style scoped> 
  .form-row {
    margin-bottom: 16px;
  }
  
  .uniform-form-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .form-label {
    display: inline-block;
    margin-bottom: 2px;
    white-space: normal;
    word-break: break-word;
    width: 100%;
  }

  .uniform-form-item >>> .arco-form-item-wrapper {
    width: 100%;
  }

  /* 中屏幕及以上时改为水平布局 */
  @media (min-width: 768px) {
    .uniform-form-item {
      flex-direction: row;
      align-items: center;
    }
    
    .form-label {
      width: 160px;
      text-align: right;
      padding-right: 1px;
      margin-bottom: 0;
    }
  }

  /* 大屏幕时调整标签宽度 */
  @media (min-width: 1200px) {
    .form-label {
      width: 180px;
    }
  }
  
  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>