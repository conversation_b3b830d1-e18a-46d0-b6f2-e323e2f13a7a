<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.radClient-chart']" />
    <a-card title="">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '10px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <!-- 日期选择框 -->
        <a-select
          v-model="selectedDate"
          placeholder="选择日期"
          style="width: 10%; justify-content: center"
          @change="fetchChartData"
        >
          <a-option v-for="date in dateOptions" :key="date" :value="date">
            {{ date }}
          </a-option>
        </a-select>

        <div
          ref="sessionChartRef"
          class="chart-item"
          style="width: 100%; height: 400px"
        ></div>
        <div
          ref="rateChartRef"
          class="chart-item"
          style="width: 100%; height: 400px"
        ></div>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, nextTick } from 'vue';
  import axios from 'axios';
  import * as echarts from 'echarts';
  import { Message } from '@arco-design/web-vue';

  const sessionChartRef = ref(null);
  const rateChartRef = ref(null);

  // 获取当前日期，格式化为YYYYMMDD
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  };

  // 生成当前日期和前6天的日期
  const generateDateOptions = (): string[] => {
    const dates: string[] = [];
    const today = new Date();

    for (let i = 0; i < 7; i += 1) {
      const date = new Date();
      date.setDate(today.getDate() - i);
      dates.push(formatDate(date));
    }

    return dates;
  };

  const selectedDate = ref(formatDate(new Date())); // 默认为当前日期
  const dateOptions = ref<string[]>(generateDateOptions()); // 生成包括当前日期在内的前7天日期

  const fetchData = async (date: string) => {
    try {
      
      console.log(date)
      
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({
          act: 'radclient_draw_info',
          show_time: date, // 使用传入的日期
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      
      // console.log(response)
   
      const initSessionChart = () => {
        const sessionChart = echarts.init(sessionChartRef.value);
        const dataTime = response.data.data.data_value.data_time || [];
        const sessionDataArray = response.data.data.data_value.auth || [];
        
        if(sessionDataArray.length == 0){
           Message.error('没有该天认证信息数据');
        }
        sessionChart.clear();
        sessionChart.setOption({
          title: {
            text: '认证信息',
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            data: response.data.data.legend_value[0].legend || [],
          },
          xAxis: {
            type: 'category',
            data: dataTime,
          },
          yAxis: {
            type: 'value',
          },
          series:
            sessionDataArray && sessionDataArray.length > 0
              ? sessionDataArray.map((sessionItem, index) => ({
                  name: response.data.data.legend_value[0].legend[index] || '',
                  type: 'line',
                  symbol: 'none',
                  data: sessionItem || [],
                }))
              : [],
        });
      };

      const initRateChart = () => {
        const rateChart = echarts.init(rateChartRef.value);
        const dataTime = response.data.data.data_value.data_time || [];
        const rateDataArray = response.data.data.data_value.acct || [];

        if(rateDataArray.length == 0){
           Message.error('没有该天计费信息数据');
        }
        rateChart.clear();
        rateChart.setOption({
          title: {
            text: '计费信息',
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            data: response.data.data.legend_value[1]?.legend || [],
          },
          xAxis: {
            type: 'category',
            data: dataTime,
          },
          yAxis: {
            type: 'value',
          },
          series:
            rateDataArray && rateDataArray.length > 0
              ? rateDataArray.map((rateItem, index) => ({
                  name: response.data.data.legend_value[1]?.legend[index] || '',
                  type: 'line',
                  symbol: 'none',
                  data: rateItem || [],
                }))
              : [],
        });
      };

      if (response.data.code === 200) {
        initSessionChart();
        initRateChart();
      } else {
        console.error('获取数据失败:', response.data);
      }
    } catch (error) {
      console.error('获取数据时出错:', error);
    }
  };

  const fetchChartData = (date: string) => {
    fetchData(date); // 使用传入的日期调用 fetchData
  };

  const onDateChange = (date: string) => {
    fetchData(date); // 当选择日期改变时，重新获取数据
  };

  onMounted(() => {
    nextTick(() => {
      fetchData(selectedDate.value); // 默认使用选中的日期获取数据
    });
  });
</script>

<style scoped>
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .chart-item {
    width: 100%;
    height: 400px;
    background-color: #fff;
    justify-content: center;
    text-align: center;
    display: flex;
    margin-bottom: 8px;
  }
</style>
