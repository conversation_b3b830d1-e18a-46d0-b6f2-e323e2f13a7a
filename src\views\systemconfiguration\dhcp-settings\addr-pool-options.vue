<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <!-- 注意提示 -->
    <a-alert type="warning" style="margin-bottom: 16px" show-icon>
      {{ $t('pool_option_warning') }}
    </a-alert>

    <!-- 地址池列表表格 -->
    <a-table
      row-key="start_ip"
      :columns="poolColumns"
      :data="poolData"
      :pagination="false"
      :bordered="true"
      :loading="isLoading"
    >
      <template #start_ip="{ record }">
        <span>{{ record.start_ip }}</span>
      </template>

      <template #end_ip="{ record }">
        <span>{{ record.end_ip }}</span>
      </template>

      <template #option="{ record }">
        <span>{{ record.option_str || '-' }}</span>
      </template>

      <template #operation="{ record }">
        <a-button type="primary" size="small" @click="openEditModal(record)">
          <template #icon>
            <icon-edit />
          </template>
          {{ $t('edit') }}
        </a-button>
      </template>
    </a-table>

    <!-- 编辑Option对话框 -->
    <a-modal
      v-model:visible="isEditModalVisible"
      :width="700"
      :title="modalTitle"
      draggable
      :mask-closable="false"
      :unmount-on-close="false"
      @before-ok="handleSaveOptions"
      @cancel="handleCancelEdit"
    >
      <!-- Option配置表格 -->
      <a-table
        row-key="index"
        :columns="optionColumns"
        :data="optionTableData"
        :pagination="false"
        :bordered="true"
        size="small"
      >
        <template #option_type="{ record }">
          <a-select
            v-model="record.option_type"
            :style="{ width: '120px' }"
            :placeholder="$t('string')"
          >
            <a-option value="string">{{ $t('string') }}</a-option>
            <a-option value="hex">{{ $t('hexadecimal') }}</a-option>
            <a-option value="ip">{{ $t('ip_address') }}</a-option>
            <a-option value="number">{{ $t('integer') }}</a-option>
          </a-select>
        </template>

        <template #option_id="{ record }">
          <a-input
            v-model="record.id"
            :placeholder="$t('please_enter_option_id')"
            :style="{ width: '150px' }"
          />
        </template>

        <template #option_value="{ record }">
          <a-input
            v-model="record.value"
            :placeholder="$t('please_enter_option_value')"
            :style="{ width: '200px' }"
          />
        </template>
      </a-table>
    </a-modal>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, watch, computed } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import axios from 'axios';
  import usePermission from '@/hooks/permission';

  interface PoolItem {
    start_ip: string;
    end_ip: string;
    option_str: string;
  }

  interface DhcpOptionItem {
    index: number;
    id: string;
    option_type: string;
    value: string;
  }

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const { hasPermission } = usePermission();
      const { t } = useI18n();

      // 地址池列表表格列定义
      const poolColumns: TableColumnData[] = [
        {
          title: t('start_address'),
          dataIndex: 'start_ip',
          slotName: 'start_ip',
          align: 'center',
          width: 200,
        },
        {
          title: t('end_address'),
          dataIndex: 'end_ip',
          slotName: 'end_ip',
          align: 'center',
          width: 200,
        },
        {
          title: 'Option',
          dataIndex: 'option_str',
          slotName: 'option',
          align: 'center',
          width: 300,
        },
        {
          title: t('operation'),
          dataIndex: 'operation',
          slotName: 'operation',
          align: 'center',
          width: 120,
        },
      ];

      // Option配置表格列定义
      const optionColumns: TableColumnData[] = [
        {
          title: t('option_type'),
          dataIndex: 'option_type',
          slotName: 'option_type',
          align: 'center',
          width: 150,
        },
        {
          title: t('option_id'),
          dataIndex: 'id',
          slotName: 'option_id',
          align: 'center',
          width: 180,
        },
        {
          title: t('option_value'),
          dataIndex: 'value',
          slotName: 'option_value',
          align: 'center',
          width: 250,
        },
      ];

      const isLoading = ref(false);
      const isEditModalVisible = ref(false);
      const currentEditPool = ref<PoolItem | null>(null);

      // 地址池数据
      const poolData = ref<PoolItem[]>([]);

      // Option配置数据（8行固定）
      const optionTableData = ref<DhcpOptionItem[]>([]);

      // 初始化Option表格数据
      const initOptionTableData = () => {
        optionTableData.value = Array.from({ length: 8 }, (_, index) => ({
          index: index + 1,
          id: '',
          option_type: '',
          value: '',
        }));
      };

      // 模态框标题
      const modalTitle = computed(() => {
        if (currentEditPool.value) {
          return `${t('edit_option_start_address')}${
            currentEditPool.value.start_ip
          }${t('end_address_colon')}${currentEditPool.value.end_ip}`;
        }
        return t('edit_option');
      });

      // 获取地址池数据
      const fetchPoolData = async () => {
        isLoading.value = true;
        try {
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'dhcp_option' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            if (response.data.data && Array.isArray(response.data.data)) {
              poolData.value = response.data.data.map((item: any) => ({
                start_ip: item.start_ip || '',
                end_ip: item.end_ip || '',
                option_str: item.option_str || '',
              }));
            }
          } else {
            Message.error({
              content: response.data.err || t('get_pool_data_failed'),
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取地址池数据失败:', error);
          Message.error(t('get_pool_data_failed'));
        } finally {
          isLoading.value = false;
        }
      };

      // 打开编辑模态框
      // 获取option类型的key值
      const getOptionTypeKey = (typeName: string): string => {
        const typeMap: { [key: string]: string } = {
          [t('integer')]: 'number',
          [t('string')]: 'string',
          [t('hex_character')]: 'hex',
          [t('ip_address')]: 'ip',
        };
        return typeMap[typeName] || 'string';
      };
      // 解析option字符串
      const parseOptionString = (optionStr: string) => {
        // 解析格式：整型-43-100,字符串-2-aaaa,hex字符-1-23
        const options = optionStr.split(',');
        options.forEach((option, index) => {
          if (index < 8 && option.trim()) {
            const parts = option.trim().split('-');
            if (parts.length === 3) {
              optionTableData.value[index] = {
                index: index + 1,
                option_type: getOptionTypeKey(parts[0]),
                id: parts[1],
                value: parts[2],
              };
            }
          }
        });
      };
      const openEditModal = (pool: PoolItem) => {
        currentEditPool.value = pool;
        initOptionTableData();

        // 解析现有的option_str并填充到表格中
        if (pool.option_str) {
          parseOptionString(pool.option_str);
        }

        isEditModalVisible.value = true;
      };

      // 获取option类型的显示名称
      const getOptionTypeName = (typeKey: string): string => {
        const nameMap: { [key: string]: string } = {
          number: t('integer'),
          string: t('string'),
          hex: t('hex_character'),
          ip: t('ip_address'),
        };
        return nameMap[typeKey] || t('string');
      };

      // 保存Option配置
      const handleSaveOptions = async (done: any) => {
        try {
          if (!currentEditPool.value) {
            done(false);
            return;
          }

          // 构建option字符串
          const optionParts: string[] = [];
          optionTableData.value.forEach((item) => {
            if (item.option_type && item.id && item.value) {
              const typeName = getOptionTypeName(item.option_type);
              optionParts.push(`${typeName}-${item.id}-${item.value}`);
            }
          });

          const optionStr = optionParts.join(',');

          // 调用保存接口
          const response = await axios.post(
            'lua/set_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'dhcp_option',
              start_ip: currentEditPool.value.start_ip,
              end_ip: currentEditPool.value.end_ip,
              option_str: optionStr,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 更新本地数据
            currentEditPool.value.option_str = optionStr;
            Message.success(t('save_success'));
            done(true);
          } else {
            Message.error({
              content: response.data.err || t('save_failed'),
              duration: 5000,
            });
            done(false);
          }
        } catch (error) {
          console.error('保存失败:', error);
          Message.error(t('save_request_failed'));
          done(false);
        }
      };

      // 取消编辑
      const handleCancelEdit = () => {
        isEditModalVisible.value = false;
        currentEditPool.value = null;
      };

      // 初始化数据
      onMounted(() => {
        initOptionTableData();
        if (props.active) {
          fetchPoolData();
        }
      });

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchPoolData();
          }
        }
      );

      return {
        poolColumns,
        optionColumns,
        poolData,
        optionTableData,
        isLoading,
        isEditModalVisible,
        modalTitle,
        openEditModal,
        handleSaveOptions,
        handleCancelEdit,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  :deep(.arco-table-th) {
    text-align: center;
    background-color: #f7f8fa;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }

  :deep(.arco-table-td .arco-select) {
    width: 100%;
  }

  :deep(.arco-table-td .arco-input) {
    text-align: center;
  }

  :deep(.arco-btn-status-danger) {
    background-color: #f53f3f;
    border-color: #f53f3f;
  }

  :deep(.arco-btn-status-danger:hover) {
    background-color: #e03e3e;
    border-color: #e03e3e;
  }
</style>
