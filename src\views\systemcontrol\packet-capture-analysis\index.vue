<!-- 会话查询组件 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.packet-capture-analysis']" />
    <a-card title="">
      <div class="session">
        <span>IP/MAC：</span>
        <a-input
          v-model="formData.session_name"
          placeholder=""
          :style="{ width: '200px' }"
        />
        &nbsp;
        <span>端口：</span>
        <a-select :style="{ width: '200px' }" v-model="formData.activeState">
          <a-option value="active">活跃</a-option>
          <a-option value="no_active">不活跃</a-option>
        </a-select>

        &nbsp;
        <span>最大抓包数目：</span>
        <a-input
          v-model="formData.session_name"
          placeholder="最大值:100000"
          :style="{ width: '200px' }"
        />
      </div>
      <div class="session">
        <span>最大抓包时间：</span>
        <a-input
          v-model="formData.session_name"
          placeholder="最大值(秒):600"
          :style="{ width: '200px' }"
        />

        &nbsp;
        <span>数据包名：</span>
        <a-input
          v-model="formData.session_name"
          placeholder="数据包文件的名字前缀，后缀.pcap不用填"
          :style="{ width: '300px' }"
        />
      </div>
      <div class="session">
        <a-button type="primary" style="margin-left: 10px" @click="fetchData">
          <template #default>开始抓包</template>
        </a-button>

        <a-button type="primary" style="margin-left: 10px" @click="fetchData">
          <template #default>停止抓包</template>
        </a-button>

        <a-button type="primary" style="margin-left: 10px" @click="fetchData">
          <template #default>分析数据</template>
        </a-button>

        <a-button type="primary" style="margin-left: 10px" @click="fetchData">
          <template #default>下载数据包</template>
        </a-button>
      </div>
      <!--       
      <div class="session2">
          <br />
          <a-table
            :columns="columns"
            :data="paginatedData"
            column-resizable
            :pagination="false"
            :bordered="{ cell: true }"
          />
          <br />
          <br />
      </div> -->
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, computed } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import usePermission from '@/hooks/permission';

  const { hasPermission } = usePermission();
  const formData = reactive({
    session_name: '',
    activeState: '',
  });

  interface TableColumn {
    title: string;
    dataIndex: string;
  }

  interface TableItem {
    [key: string]: any;
  }

  const tableData = ref<TableItem[]>([]);
  const columns = ref<TableColumn[]>([]);

  const currentPage = ref(1);
  const pageSize = ref(10);
  const paginatedData = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    return tableData.value.slice(start, start + pageSize.value);
  });

  // 清空会话数据方法
  const clearSessionData = () => {
    tableData.value = [];
  };

  const fetchData = async () => {
    if (!formData.session_name) {
      Message.warning('请选择用户类型');
      return;
    }
    if (!formData.activeState) {
      Message.warning('请选择活动状态');
      return;
    }
    try {
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({
          act: 'session_list',
          session_name: formData.session_name,
          activeState: formData.activeState,
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      console.log(response);
      if (response.data.code === 200) {
        // 1. 处理列数据
        if (Array.isArray(response.data.data?.name)) {
          columns.value = response.data.data.name.map(
            (title: string, index: number) => ({
              title: title || `Column ${index}`,
              dataIndex: index.toString(),
            })
          );
        }

        // 2. 处理行数据
        if (Array.isArray(response.data.data?.data)) {
          tableData.value = response.data.data.data.map(
            (row: any[], rowIndex: number) => {
              const rowData: TableItem = { key: rowIndex.toString() };

              console.log(row);
              if (typeof row === 'object' && row !== null) {
                console.log('----------');
                Object.entries(row).forEach(([key, value]) => {
                  console.log(key, value);
                  rowData[key] = value ?? '-';
                });
              }

              return rowData;
            }
          );
        }

        console.log('Processed Columns:', columns.value);
        console.log('Processed Table Data:', tableData.value);
      } else {
        // 清空会话数据
        clearSessionData();
        Message.error(response.data.err || '查询失败');
      }
    } catch (error) {
      console.error('获取数据时出错:', error);
      clearSessionData();
      Message.error('查询会话请求失败');
    }
  };
</script>

<style scoped>
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .session,
  .session2 {
    text-align: left;
    background-color: #fff;
    padding: 2px;
    white-space: nowrap;
  }

  .session {
    margin-bottom: 20px;
  }
  span {
    font-size: 20px;
  }
</style>
