<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 4 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="work_mode" class="uniform-form-item">
        <template #label>
          <span class="form-label">Work Mode:</span>
        </template>
        <a-select :style="{ width: '150px' }" v-model="formData.work_mode">
          <a-option value="disable">Disable</a-option>
          <a-option value="enable">Enable</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="dhcpv6_mange_addr" class="uniform-form-item">
        <template #label>
          <span class="form-label">DHCPv6 Manage Address:</span>
        </template>
        <a-select
          :style="{ width: '150px' }"
          v-model="formData.dhcpv6_mange_addr"
        >
          <a-option value="none">None</a-option>
          <a-option value="enable">Enable</a-option>
        </a-select>
      </a-form-item>

      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>Submit</template>
        </a-button>
      </a-form-item>
    </a-form>

    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-space>
          <!-- 新建按钮 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            Add IPv6 Address
          </a-button>
        </a-space>
      </a-col>
      <!-- 右侧按钮 -->
      <a-col
        :span="12"
        style="display: flex; align-items: center; justify-content: end"
      >
        <!-- 刷新按钮 -->
        <a-tooltip content="刷新">
          <div class="action-icon" @click="handleRefresh">
            <icon-refresh size="18" />
          </div>
        </a-tooltip>
      </a-col>
    </a-row>

    <!-- 列表 -->
    <a-table
      :columns="columns"
      :data="data"
      style="margin-top: 20px"
      :pagination="false"
    >
      <template #operation="{ index }">
        <a-button status="danger" @click="deleteRow(index)">
          <template #icon>
            <icon-delete />
          </template>
          <template #default>Delete</template>
        </a-button>
      </template>
    </a-table>
  </a-card>

  <a-modal
    v-model:visible="isModalVisible"
    title="Add IPv6 Address"
    :width="700"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formData">
      <a-form-item label="ID" field="id">
        <a-input
          v-model="formData.id"
          style="width: 200px"
          placeholder="Enter ID"
        />
      </a-form-item>
      <a-form-item label="Gateway/Mask bit" field="gw_prefix">
        <a-input
          v-model="formData.gw_prefix"
          style="width: 300px"
          placeholder="Enter Gateway/Mask bit"
        />
      </a-form-item>
      <a-form-item label="Start IP" field="start_ip">
        <a-input
          v-model="formData.start_ip"
          style="width: 300px"
          placeholder="Enter Start IP"
        />
      </a-form-item>
      <a-form-item label="Number" field="num">
        <a-input
          v-model="formData.num"
          style="width: 200px"
          placeholder="Enter Number"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';
  import message from '@/utils/message';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formRef = ref(null);
      // 表格列定义
      const columns = [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center' as const,
          width: 100,
        },
        {
          title: 'Gateway/Mask bit',
          dataIndex: 'gw_prefix',
          align: 'center' as const,
          width: 200,
        },
        {
          title: 'Start IP',
          dataIndex: 'start_ip',
          align: 'center' as const,
          width: 200,
        },
        {
          title: 'Number',
          dataIndex: 'num',
          align: 'center' as const,
          width: 100,
        },
        {
          title: 'Operation',
          dataIndex: 'operation',
          slotName: 'operation',
          align: 'center' as const,
          width: 120,
        },
      ];
      const data = ref<
        {
          id: string;
          gw_prefix: string;
          start_ip: string;
          num: string;
        }[]
      >([]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);

      const formData = reactive({
        work_mode: 'disable',
        dhcpv6_mange_addr: 'none',
        // 模态框表单数据
        id: '',
        gw_prefix: '',
        start_ip: '',
        num: '',
      });
      const rules = {
        id: [{ required: true, message: 'ID is required' }],
        gw_prefix: [
          { required: true, message: 'Gateway/Mask bit is required' },
        ],
        start_ip: [{ required: true, message: 'Start IP is required' }],
        num: [{ required: true, message: 'Number is required' }],
      };
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_pppoe_server.lua',
            new URLSearchParams({ act: 'ipv6_pool' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            const responseData = response.data.data;
            formData.work_mode = responseData.work_mode || 'disable';
            formData.dhcpv6_mange_addr =
              responseData.dhcpv6_mange_addr || 'none';

            // 更新表格数据
            if (responseData.addr && Array.isArray(responseData.addr)) {
              data.value = responseData.addr;
            } else {
              data.value = [];
            }
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }

        try {
          const response = await axios.post(
            '/lua/set_cfg_pppoe_server.lua',
            new URLSearchParams({
              act: 'ipv6_pool',
              act_type: 'mod',
              work_mode: formData.work_mode,
              dhcpv6_mange_addr: formData.dhcpv6_mange_addr,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };
      // 刷新数据
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };
      // 显示模态框
      const showModal = () => {
        formData.id = '';
        formData.gw_prefix = '';
        formData.start_ip = '';
        formData.num = '';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleBeforeOk = async (done: any) => {
        // 简单验证
        if (
          !formData.id ||
          !formData.gw_prefix ||
          !formData.start_ip ||
          !formData.num
        ) {
          Message.error('请填写完整信息');
          done(false);
          return;
        }

        // 添加到列表
        data.value.push({
          id: formData.id,
          gw_prefix: formData.gw_prefix,
          start_ip: formData.start_ip,
          num: formData.num,
        });

        Message.success('添加成功');
        done(true); // 允许模态框关闭
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      // 删除行
      const deleteRow = (index: number) => {
        data.value.splice(index, 1);
        Message.success('删除成功');
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        saveAction,
        hasPermission,
        rules,
        columns,
        data,
        handleRefresh,
        isRefreshing,
        isModalVisible,
        showModal,
        handleBeforeOk,
        handleCancel,
        deleteRow,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
