<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 4 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="work" class="uniform-form-item">
        <template #label>
          <span class="form-label">指定管理地址：</span>
        </template>
        <a-tooltip
          content="可实时配置"
          position="tl"
          background-color="#3491FA"
        >
          <a-select :style="{ width: '100px' }" v-model="formData.work">
            <a-option value="disable">关闭</a-option>
            <a-option value="enable">开启</a-option>
          </a-select>
        </a-tooltip>
      </a-form-item>
      <a-form-item
        :rules="rules.map_head"
        field="map_head"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">pppoe启动ipv6接入方式：</span>
        </template>
        <a-select :style="{ width: '350px' }" v-model="formData.work">
          <a-option value="disable">不启动pppoe ipv6双栈</a-option>
          <a-option value="enable"
            >用户选择pppoe ipv6双栈，用户名后加@ipv6
          </a-option>
          <a-option value="enable1">强制pppoe ipv6双栈 </a-option>
        </a-select>
      </a-form-item>

      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>提交</template>
        </a-button>
      </a-form-item>
    </a-form>

    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-space>
          <!-- 新建按钮 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            添加内层vlan
          </a-button>
        </a-space>
      </a-col>
      <!-- 右侧按钮 -->
      <a-col
        :span="12"
        style="display: flex; align-items: center; justify-content: end"
      >
        <!-- 刷新按钮 -->
        <a-tooltip content="刷新">
          <div class="action-icon" @click="handleRefresh">
            <icon-refresh size="18" />
          </div>
        </a-tooltip>
      </a-col>
    </a-row>

    <!-- 列表 -->
    <a-table
      :columns="columns"
      :data="data"
      style="margin-top: 20px"
      :pagination="false"
    >
      <template #operation="{ index }">
        <a-button status="danger" @click="deleteRow(index)">
          <template #icon>
            <icon-delete />
          </template>
          <template #default>删除</template>
        </a-button>
      </template>
    </a-table>
  </a-card>

  <a-modal
    v-model:visible="isModalVisible"
    title="添加内层vlan"
    :width="700"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formData" :rules="rules">
      <a-form-item label="内层起始vlan" field="start_vlan">
        <a-input v-model="formData.start_vlan" style="width: 200px" />
      </a-form-item>
      <a-form-item label="内层结束vlan" field="end_vlan">
        <a-input v-model="formData.end_vlan" style="width: 200px" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';
  import message from '@/utils/message';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formRef = ref(null);
      // 表格列定义
      const columns = [
        {
          title: '内层起始vlan',
          dataIndex: 'start_vlan',
        },
        {
          title: '内层结束vlan',
          dataIndex: 'end_vlan',
        },
        {
          title: '操作',
          dataIndex: 'operation',
          slotName: 'operation',
        },
      ];
      const data = ref<{ start_vlan: string; end_vlan: string }[]>([]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);

      const formData = reactive({
        work: 'disable',
        map_head: '',
        map_tail: '',
        map6_head: '',
        map6_tail: '',
        start_vlan: '',
        end_vlan: '',
      });
      const rules = {
        map_head: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        map_tail: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
      };
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_5g_net_gw.lua',
            new URLSearchParams({ act: 'net_5g' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.map_head = response.data.data.map_head || '';
            formData.map_tail = response.data.data.map_tail || '';
            formData.map6_head = response.data.data.map6_head || '';
            formData.map6_tail = response.data.data.map6_tail || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          // Arco Design表单验证失败时会返回errors对象
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'mod',
              map_head: String(formData.map_head),
              map_tail: String(formData.map_tail),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };
      // 刷新数据
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };
      // 显示模态框
      const showModal = () => {
        formData.start_vlan = '';
        formData.end_vlan = '';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleBeforeOk = async (done) => {
        formRef.value.validate().then((errors) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 添加到列表
          data.value.push({
            start_vlan: formData.start_vlan,
            end_vlan: formData.end_vlan,
          });

          Message.success('添加成功');
          done(true); // 允许模态框关闭
        });
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      // 删除行
      const deleteRow = (index: number) => {
        data.value.splice(index, 1);
        Message.success('删除成功');
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        saveAction,
        hasPermission,
        rules,
        columns,
        data,
        handleRefresh,
        isRefreshing,
        isModalVisible,
        showModal,
        handleBeforeOk,
        handleCancel,
        deleteRow,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
