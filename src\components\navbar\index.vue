<!-- 登录后顶部导航栏 -->
<template>
  <div class="navbar">
    <div class="left-side">
      <a-space>
        <img
          style="width: 100px"
          alt="logo"
          src="../../assets/images/company_logo.png"
        />
        <a-typography-title
          :style="{ margin: 0, fontSize: '18px' }"
          :heading="5"
        >
          vBase web管理
        </a-typography-title>
        <icon-menu-fold
          v-if="!topMenu && appStore.device === 'mobile'"
          style="font-size: 22px; cursor: pointer"
          @click="toggleDrawerMenu"
        />
      </a-space>
    </div>
    <div class="center-side">
      <Menu v-if="topMenu" />
    </div>
    <!-- 顶部导航栏右侧部分 -->
    <ul class="right-side">
      <!-- 搜索按钮 -->
      <!-- <li>
        <a-tooltip :content="$t('settings.search')">
          <a-button class="nav-btn" type="outline" :shape="'circle'">
            <template #icon>
              <icon-search />
            </template>
          </a-button>
        </a-tooltip>
      </li> -->
      <!-- 重启虚机按钮 -->
      <li>
        <a-tooltip :content="$t('settings.Restart_the_virtual_machine')">
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            :disabled="!hasPermission('restartVM')"
            @click="handleClick('vm')"
          >
            <template #icon>
              <IconPoweroff />
            </template>
          </a-button>
          <a-modal
            v-model:visible="visibleVM"
            :on-before-ok="handleBeforeOk"
            unmountOnClose
            @cancel="handleCancel"
          >
            <template #title> 重启虚机 </template>
            <div style="color: red">* 确认重启虚机吗？ </div>
          </a-modal>
        </a-tooltip>
      </li>

      <!-- 重启系统按钮 -->
      <li>
        <a-tooltip :content="$t('settings.Restart_the_system')">
          <a-button
            class="nav-btn"
            :disabled="!hasPermission('restartSystem')"
            type="outline"
            :shape="'circle'"
            @click="handleClick('system')"
          >
            <template #icon>
              <IconSync />
            </template>
          </a-button>
        </a-tooltip>
        <a-modal
          v-model:visible="visibleSystem"
          :on-before-ok="handleBeforeOk"
          @cancel="handleCancel"
          unmountOnClose
        >
          <template #title> 重启系统 </template>
          <div style="color: red">* 确认重启系统吗 </div>
        </a-modal>
      </li>

      <!-- 恢复出厂设置按钮 -->
      <li>
        <a-tooltip :content="$t('settings.Restore_factory_configuration')">
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            :disabled="!hasPermission('factoryReset')"
            @click="handleClick('factory')"
          >
            <template #icon>
              <IconRefresh />
            </template>
          </a-button>
          <a-modal
            v-model:visible="visibleFactory"
            :on-before-ok="handleBeforeOk"
            @cancel="handleCancel"
            unmountOnClose
          >
            <template #title> 恢复出厂配置 </template>
            <div style="color: red">* 确定要恢复出厂配置？？！！ </div>
          </a-modal>
        </a-tooltip>
      </li>

      <!-- 语言转换按钮 -->
      <!-- <li>
        <a-tooltip :content="$t('settings.language')">
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="setDropDownVisible"
          >
            <template #icon>
              <icon-language />
            </template>
          </a-button>
        </a-tooltip>
        <a-dropdown trigger="click" @select="changeLocale as any">
          <div ref="triggerBtn" class="trigger-btn"></div>
          <template #content>
            <a-doption
              v-for="item in locales"
              :key="item.value"
              :value="item.value"
            >
              <template #icon>
                <icon-check v-show="item.value === currentLocale" />
              </template>
              {{ item.label }}
            </a-doption>
          </template>
        </a-dropdown>
      </li> -->
      <!-- 暗黑模式切换按钮 -->
      <!-- <li>
        <a-tooltip
          :content="
            theme === 'light'
              ? $t('settings.navbar.theme.toDark')
              : $t('settings.navbar.theme.toLight')
          "
        >
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="handleToggleTheme"
          >
            <template #icon>
              <icon-moon-fill v-if="theme === 'dark'" />
              <icon-sun-fill v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li> -->

      <!-- 消息通知按钮 -->
      <!-- <li>
        <a-tooltip :content="$t('settings.navbar.alerts')">
          <div class="message-box-trigger">
            <a-badge :count="9" dot>
              <a-button
                class="nav-btn"
                type="outline"
                :shape="'circle'"
                @click="setPopoverVisible"
              >
                <icon-notification />
              </a-button>
            </a-badge>
          </div>
        </a-tooltip>
        <a-popover
          trigger="click"
          :arrow-style="{ display: 'none' }"
          :content-style="{ padding: 0, minWidth: '400px' }"
          content-class="message-popover"
        >
          <div ref="refBtn" class="ref-btn"></div>
          <template #content>
            <message-box />
          </template>
        </a-popover>
      </li> -->

      <!-- 全屏按钮 -->
      <li>
        <a-tooltip
          :content="
            isFullscreen
              ? $t('settings.navbar.screen.toExit')
              : $t('settings.navbar.screen.toFull')
          "
        >
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="toggleFullScreen"
          >
            <template #icon>
              <icon-fullscreen-exit v-if="isFullscreen" />
              <icon-fullscreen v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li>

      <!-- 页面配置按钮 -->
      <!-- <li>
        <a-tooltip :content="$t('settings.title')">
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="setVisible"
          >
            <template #icon>
              <icon-settings />
            </template>
          </a-button>
        </a-tooltip>
      </li> -->
      <li>
        <a-dropdown trigger="click">
          <a-avatar
            :size="32"
            :style="{ marginRight: '8px', cursor: 'pointer' }"
          >
           {{ userStore.userInfo?.accountId }}
            <!-- <img alt="avatar" :src="avatar" /> -->
          </a-avatar>
          <template #content>
            <!-- <a-doption>
              <a-space @click="switchRoles">
                <icon-tag />
                <span>
                  {{ $t('messageBox.switchRoles') }}
                </span>
              </a-space>
            </a-doption> -->
            <!-- <a-doption>
              <a-space @click="$router.push({ name: 'Info' })">
                <icon-user />
                <span>
                  {{ $t('messageBox.userCenter') }}
                </span>
              </a-space>
            </a-doption> -->
            <!-- <a-doption>
              <a-space @click="$router.push({ name: 'Setting' })">
                <icon-settings />
                <span>
                  {{ $t('messageBox.userSettings') }}
                </span>
              </a-space>
            </a-doption> -->
            <a-doption>
              <a-space @click="router.push({ name: 'Change_Password' })">
                <icon-lock />
                <span>
                  {{ $t('messageBox.changePassword') }}
                </span>
              </a-space>
            </a-doption>
            <a-doption>
              <a-space @click="handleLogout">
                <icon-export />
                <span>
                  {{ $t('messageBox.logout') }}
                </span>
              </a-space>
            </a-doption>
          </template>
        </a-dropdown>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, inject } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useDark, useToggle, useFullscreen } from '@vueuse/core';
  import { useAppStore, useUserStore } from '@/store';
  import { useRouter } from 'vue-router';
  import { LOCALE_OPTIONS } from '@/locale';
  import useLocale from '@/hooks/locale';
  import axios from 'axios';
  import useUser from '@/hooks/user';
  import Menu from '@/components/menu/index.vue';
  import usePermission from '@/hooks/permission';

  const router = useRouter();
  const visible = ref(false);
  const { hasPermission } = usePermission();

  const visibleVM = ref(false); // 重启虚机
  const visibleSystem = ref(false); // 重启系统
  const visibleFactory = ref(false); // 恢复出厂配置

  // 当前操作类型
  const currentAction = ref('');

  const handleClick = (type: string) => {
    currentAction.value = type;
    if (type === 'vm') visibleVM.value = true;
    if (type === 'system') visibleSystem.value = true;
    if (type === 'factory') visibleFactory.value = true;
  };

  const handleBeforeOk = async () => {
    try {
      let act = '';
      let successMsg = '';

      // 根据当前操作类型设置请求参数
      if (currentAction.value === 'vm') {
        act = 'restart_vm';
        successMsg = '重启虚机指令已发送';
      } else if (currentAction.value === 'system') {
        act = 'reboot';
        successMsg = '重启系统指令已发送';
      } else if (currentAction.value === 'factory') {
        act = 'reset';
        successMsg = '恢复出厂配置指令已发送';
      }

      if (act) {
        const response = await axios.post(
          '/lua/system_control.lua',
          new URLSearchParams({
            act,
          }),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        );

        if (response.data.code === 200) {
          Message.success({
            content: response.data.success || successMsg,
            duration: 5000,
          });
        } else {
          Message.error(response.data.err || '操作失败');
          return false; // 阻止对话框关闭
        }
      }

      return true; // 允许对话框关闭
    } catch (error) {
      console.error('操作错误:', error);
      Message.error('请求发送失败');
      return false; // 阻止对话框关闭
    }
  };
  const handleCancel = () => {
    visible.value = false;
    visibleVM.value = false;
    visibleSystem.value = false;
    visibleFactory.value = false;
  };

  const appStore = useAppStore();
  const userStore = useUserStore();
  const { logout } = useUser();
  const { changeLocale, currentLocale } = useLocale();
  const { isFullscreen, toggle: toggleFullScreen } = useFullscreen();
  const locales = [...LOCALE_OPTIONS];
  const avatar = computed(() => {
    return userStore.avatar;
  });
  const theme = computed(() => {
    return appStore.theme;
  });
  const topMenu = computed(() => appStore.topMenu && appStore.menu);
  const isDark = useDark({
    selector: 'body',
    attribute: 'arco-theme',
    valueDark: 'dark',
    valueLight: 'light',
    storageKey: 'arco-theme',
    onChanged(dark: boolean) {
      // overridden default behavior
      appStore.toggleTheme(dark);
    },
  });
  const toggleTheme = useToggle(isDark);
  const handleToggleTheme = () => {
    toggleTheme();
  };
  const setVisible = () => {
    appStore.updateSettings({ globalSettings: true });
  };
  const refBtn = ref();
  const triggerBtn = ref();
  const setPopoverVisible = () => {
    const event = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true,
    });
    refBtn.value.dispatchEvent(event);
  };
  const handleLogout = () => {
    logout();
  };
  const setDropDownVisible = () => {
    const event = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true,
    });
    triggerBtn.value.dispatchEvent(event);
  };
  const switchRoles = async () => {
    const res = await userStore.switchRoles();
    Message.success(res as string);
  };
  const toggleDrawerMenu = inject('toggleDrawerMenu') as () => void;
</script>

<style scoped lang="less">
  .navbar {
    display: flex;
    justify-content: space-between;
    height: 100%;
    background-color: var(--color-bg-2);
    border-bottom: 1px solid var(--color-border);
  }

  .left-side {
    display: flex;
    align-items: center;
    padding-left: 20px;
  }

  .center-side {
    flex: 1;
  }

  .right-side {
    display: flex;
    padding-right: 20px;
    list-style: none;
    :deep(.locale-select) {
      border-radius: 20px;
    }
    li {
      display: flex;
      align-items: center;
      padding: 0 10px;
    }

    a {
      color: var(--color-text-1);
      text-decoration: none;
    }
    .nav-btn {
      border-color: rgb(var(--gray-2));
      color: rgb(var(--gray-8));
      font-size: 16px;
    }
    .trigger-btn,
    .ref-btn {
      position: absolute;
      bottom: 14px;
    }
    .trigger-btn {
      margin-left: 14px;
    }
  }
</style>

<style lang="less">
  .message-popover {
    .arco-popover-content {
      margin-top: 0;
    }
  }
</style>
