#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大麦网演唱会抢票脚本
支持最新的抢票流程，包括登录、选座、提交订单等功能
作者: AI Assistant
版本: 2.0
"""

import time
import json
import random
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import threading
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('damai_ticket.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DamaiTicketGrabber:
    def __init__(self, config):
        """
        初始化抢票器
        :param config: 配置字典，包含用户信息、演出信息等
        """
        self.config = config
        self.driver = None
        self.wait = None
        self.is_logged_in = False
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            
            # 性能优化选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # 禁用图片加载
            chrome_options.add_argument('--disable-javascript')  # 在某些页面禁用JS
            
            # 反检测选项
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            
            # 设置窗口大小
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 如果需要无头模式（后台运行）
            if self.config.get('headless', False):
                chrome_options.add_argument('--headless')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.wait = WebDriverWait(self.driver, 10)
            logging.info("浏览器驱动初始化成功")
            
        except Exception as e:
            logging.error(f"浏览器驱动初始化失败: {e}")
            raise

    def login(self):
        """登录大麦网"""
        try:
            logging.info("开始登录大麦网...")
            self.driver.get("https://passport.damai.cn/login")
            
            # 等待登录页面加载
            time.sleep(2)
            
            # 选择密码登录
            try:
                password_tab = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '密码登录')]"))
                )
                password_tab.click()
                time.sleep(1)
            except:
                logging.info("已经在密码登录页面")
            
            # 输入用户名
            username_input = self.wait.until(
                EC.presence_of_element_located((By.ID, "fm-login-id"))
            )
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            # 输入密码
            password_input = self.driver.find_element(By.ID, "fm-login-password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            # 点击登录按钮
            login_btn = self.driver.find_element(By.XPATH, "//button[contains(@class, 'fm-button') and contains(text(), '登录')]")
            login_btn.click()
            
            # 等待登录成功或需要验证码
            time.sleep(3)
            
            # 检查是否需要滑块验证
            try:
                slider = self.driver.find_element(By.CLASS_NAME, "nc_iconfont")
                if slider:
                    logging.info("检测到滑块验证，请手动完成...")
                    input("请手动完成滑块验证后按回车继续...")
            except:
                pass
            
            # 检查是否登录成功
            try:
                self.wait.until(EC.url_contains("damai.cn"))
                self.is_logged_in = True
                logging.info("登录成功！")
            except:
                logging.error("登录失败，请检查用户名密码")
                return False
                
            return True
            
        except Exception as e:
            logging.error(f"登录过程出错: {e}")
            return False

    def goto_target_page(self):
        """跳转到目标演出页面"""
        try:
            logging.info(f"跳转到目标页面: {self.config['target_url']}")
            self.driver.get(self.config['target_url'])
            time.sleep(2)
            
            # 等待页面加载完成
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            logging.info("目标页面加载完成")
            return True
            
        except Exception as e:
            logging.error(f"跳转目标页面失败: {e}")
            return False

    def select_ticket_and_buy(self):
        """选择票档并购买"""
        try:
            logging.info("开始选择票档...")
            
            # 等待票档信息加载
            time.sleep(2)
            
            # 查找并点击目标价位的票
            target_price = self.config.get('target_price', '')
            if target_price:
                price_elements = self.driver.find_elements(By.XPATH, f"//div[contains(text(), '{target_price}')]")
                if price_elements:
                    price_elements[0].click()
                    logging.info(f"选择了价位: {target_price}")
                    time.sleep(1)
            
            # 选择票数
            ticket_num = self.config.get('ticket_num', 1)
            try:
                # 查找票数选择器
                num_selectors = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'select-num')]//span")
                if len(num_selectors) >= ticket_num:
                    for i in range(ticket_num):
                        num_selectors[i].click()
                        time.sleep(0.1)
                    logging.info(f"选择了 {ticket_num} 张票")
            except:
                logging.warning("票数选择失败，使用默认数量")
            
            # 查找并点击立即购买按钮
            buy_buttons = [
                "//button[contains(text(), '立即购买')]",
                "//button[contains(text(), '立即预订')]", 
                "//button[contains(text(), '确认购买')]",
                "//a[contains(text(), '立即购买')]",
                "//div[contains(@class, 'buy-btn')]"
            ]
            
            for xpath in buy_buttons:
                try:
                    buy_btn = self.driver.find_element(By.XPATH, xpath)
                    if buy_btn.is_enabled():
                        buy_btn.click()
                        logging.info("点击购买按钮成功")
                        time.sleep(2)
                        return True
                except:
                    continue
            
            logging.error("未找到可用的购买按钮")
            return False
            
        except Exception as e:
            logging.error(f"选择票档失败: {e}")
            return False

    def select_viewer_and_submit(self):
        """选择观演人并提交订单"""
        try:
            logging.info("开始选择观演人...")
            
            # 等待观演人页面加载
            time.sleep(2)
            
            # 选择观演人
            viewers = self.config.get('viewers', [])
            if viewers:
                for i, viewer in enumerate(viewers):
                    try:
                        # 查找观演人选择框
                        viewer_checkbox = self.driver.find_element(
                            By.XPATH, f"//label[contains(text(), '{viewer}')]//input[@type='checkbox']"
                        )
                        if not viewer_checkbox.is_selected():
                            viewer_checkbox.click()
                            logging.info(f"选择观演人: {viewer}")
                            time.sleep(0.5)
                    except:
                        logging.warning(f"观演人 {viewer} 选择失败")
            else:
                # 如果没有指定观演人，选择第一个
                try:
                    first_viewer = self.driver.find_element(By.XPATH, "//input[@type='checkbox'][1]")
                    if not first_viewer.is_selected():
                        first_viewer.click()
                        logging.info("选择了第一个观演人")
                except:
                    logging.warning("自动选择观演人失败")
            
            # 提交订单
            submit_buttons = [
                "//button[contains(text(), '确认订单')]",
                "//button[contains(text(), '提交订单')]",
                "//button[contains(text(), '确认购买')]",
                "//div[contains(@class, 'submit-btn')]"
            ]
            
            for xpath in submit_buttons:
                try:
                    submit_btn = self.driver.find_element(By.XPATH, xpath)
                    if submit_btn.is_enabled():
                        submit_btn.click()
                        logging.info("提交订单成功！")
                        return True
                except:
                    continue
            
            logging.error("未找到提交订单按钮")
            return False
            
        except Exception as e:
            logging.error(f"选择观演人和提交订单失败: {e}")
            return False

    def check_ticket_status(self):
        """检查抢票状态"""
        try:
            # 检查是否进入支付页面
            if "trade.damai.cn" in self.driver.current_url or "支付" in self.driver.title:
                logging.info("🎉 抢票成功！已进入支付页面")
                return "success"
            
            # 检查是否售罄
            page_source = self.driver.page_source
            if any(keyword in page_source for keyword in ["售罄", "暂无票", "已售完", "无票"]):
                logging.warning("票已售罄")
                return "sold_out"
            
            # 检查是否需要排队
            if any(keyword in page_source for keyword in ["排队", "等待", "队列"]):
                logging.info("正在排队中...")
                return "queuing"
            
            return "unknown"
            
        except Exception as e:
            logging.error(f"检查抢票状态失败: {e}")
            return "error"

    def run(self):
        """运行抢票主流程"""
        try:
            # 初始化浏览器
            self.setup_driver()
            
            # 登录
            if not self.login():
                return False
            
            # 等待开抢时间
            start_time = self.config.get('start_time')
            if start_time:
                target_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                current_time = datetime.now()
                
                if current_time < target_time:
                    wait_seconds = (target_time - current_time).total_seconds()
                    logging.info(f"等待开抢，剩余时间: {wait_seconds:.1f} 秒")
                    time.sleep(max(0, wait_seconds - 1))  # 提前1秒准备
            
            # 跳转到目标页面
            if not self.goto_target_page():
                return False
            
            # 开始抢票循环
            max_attempts = self.config.get('max_attempts', 100)
            for attempt in range(max_attempts):
                logging.info(f"第 {attempt + 1} 次尝试抢票...")
                
                try:
                    # 刷新页面
                    if attempt > 0:
                        self.driver.refresh()
                        time.sleep(1)
                    
                    # 选择票档并购买
                    if self.select_ticket_and_buy():
                        # 选择观演人并提交
                        if self.select_viewer_and_submit():
                            # 检查状态
                            status = self.check_ticket_status()
                            if status == "success":
                                logging.info("🎉🎉🎉 抢票成功！请尽快完成支付！")
                                input("抢票成功！请手动完成支付，完成后按回车退出...")
                                return True
                            elif status == "sold_out":
                                logging.info("票已售罄，停止尝试")
                                return False
                    
                    # 随机等待，避免被检测
                    time.sleep(random.uniform(0.5, 1.5))
                    
                except Exception as e:
                    logging.error(f"第 {attempt + 1} 次尝试失败: {e}")
                    continue
            
            logging.info("达到最大尝试次数，抢票结束")
            return False
            
        except Exception as e:
            logging.error(f"抢票过程出错: {e}")
            return False
        finally:
            if self.driver:
                input("按回车键关闭浏览器...")
                self.driver.quit()

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logging.warning("配置文件 config.json 不存在，使用默认配置")
        return {
            'username': 'your_username',
            'password': 'your_password',
            'target_url': 'https://detail.damai.cn/item.htm?id=xxxxxx',
            'target_price': '380',
            'ticket_num': 1,
            'viewers': [],
            'start_time': '',
            'max_attempts': 200,
            'headless': False,
        }

def main():
    """主函数"""
    # 加载配置
    config = load_config()
    
    print("=" * 50)
    print("🎫 大麦网演唱会抢票脚本 v2.0")
    print("=" * 50)
    print("⚠️  使用前请确保:")
    print("1. 已安装 Chrome 浏览器和对应版本的 ChromeDriver")
    print("2. 已正确配置用户名、密码和目标URL")
    print("3. 网络连接稳定")
    print("=" * 50)
    
    # 创建抢票器实例
    grabber = DamaiTicketGrabber(config)
    
    # 开始抢票
    success = grabber.run()
    
    if success:
        print("🎉 抢票成功！")
    else:
        print("😞 抢票失败，请重试")

if __name__ == "__main__":
    main()
