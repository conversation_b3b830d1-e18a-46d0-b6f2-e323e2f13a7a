<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 3 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="oesrv_fail_login" class="uniform-form-item">
        <template #label>
          <span class="form-label">认证失败时web定向：</span>
        </template>
        <a-tooltip
          content="可实时配置"
          position="tl"
          background-color="#3491FA"
        >
          <a-select
            :style="{ width: '100px' }"
            v-model="formData.oesrv_fail_login"
          >
            <a-option value="disable">关闭</a-option>
            <a-option value="enable">开启</a-option>
          </a-select>
        </a-tooltip>
      </a-form-item>
      <a-form-item
        :rules="rules.oesrv_fail_hold"
        field="oesrv_fail_hold"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">认证失败时接入时间</span>
        </template>
        <a-tooltip
          content="单位：秒，最小值：300"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_fail_hold"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>

      <a-form-item
        :rules="rules.oesrv_info_captive"
        field="oesrv_info_captive"
        class="uniform-form-item"
      >
        <template #label>
          <span class="form-label">访问截获地址</span>
        </template>

        <a-input
          v-model="formData.oesrv_info_captive"
          placeholder=""
          :style="{ width: '200px' }"
        />
      </a-form-item>

      <a-form-item field="oesrv_info_addr" class="uniform-form-item">
        <template #label>
          <span class="form-label">信息页面服务器地址</span>
        </template>
        <a-input
          v-model="formData.oesrv_info_addr"
          placeholder=""
          :style="{ width: '200px' }"
        />
      </a-form-item>

      <a-form-item field="oesrv_info_url" class="uniform-form-item">
        <template #label>
          <span class="form-label">信息页面url</span>
        </template>
        <a-tooltip
          content="注意需要带上问号"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.oesrv_info_url"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>
      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>提交</template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4 } from '@/utils/validate';
  import message from '@/utils/message';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      const formData = reactive({
        oesrv_fail_login: 'disable',
        oesrv_fail_hold: '',
        oesrv_info_captive: '',
        oesrv_info_addr: '',
        oesrv_info_url: '',
      });
      const rules = {
        oesrv_fail_hold: [
          { required: true, message: '接入时间不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || parseInt(value, 10) < 300) {
                callback('接入时间最小值为300秒');
              } else {
                callback();
              }
            },
          },
        ],
        oesrv_info_captive: [
          { required: true, message: '访问截获地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
      };
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_pppoe_server.lua',
            new URLSearchParams({ act: 'auth_fail_orientation' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.oesrv_fail_hold = response.data.data.oesrv_fail_hold || '';
            formData.oesrv_info_captive =
              response.data.data.oesrv_info_captive || '';
            formData.oesrv_info_addr = response.data.data.oesrv_info_addr || '';
            formData.oesrv_info_url = response.data.data.oesrv_info_url || '';
            formData.oesrv_fail_login =
              response.data.data.oesrv_fail_login || 'disable';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          // Arco Design表单验证失败时会返回errors对象
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: '',
              act_type: '',
              oesrv_fail_login: String(formData.oesrv_fail_login),
              oesrv_fail_hold: String(formData.oesrv_fail_hold),
              oesrv_info_captive: String(formData.oesrv_info_captive),
              oesrv_info_addr: String(formData.oesrv_info_addr),
              oesrv_info_url: String(formData.oesrv_info_url),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        saveAction,
        hasPermission,
        rules,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
