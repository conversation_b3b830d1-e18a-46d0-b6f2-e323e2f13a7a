import localeMessageBox from '@/components/message-box/locale/en-US';
import localeLogin from '@/views/login/locale/en-US';

import localeWorkplace from '@/views/dashboard/workplace/locale/en-US';

import localeMonitor from '@/views/dashboard/monitor/locale/en-US';

import localeSearchTable from '@/views/list/search-table/locale/en-US';
import localeCardList from '@/views/list/card/locale/en-US';

import localeStepForm from '@/views/form/step/locale/en-US';
import localeGroupForm from '@/views/form/group/locale/en-US';

import localeBasicProfile from '@/views/profile/basic/locale/en-US';

import localeDataAnalysis from '@/views/visualization/data-analysis/locale/en-US';
import localeMultiDAnalysis from '@/views/visualization/multi-dimension-data-analysis/locale/en-US';

import localeSuccess from '@/views/result/success/locale/en-US';
import localeError from '@/views/result/error/locale/en-US';

import locale403 from '@/views/exception/403/locale/en-US';
import locale404 from '@/views/exception/404/locale/en-US';
import locale500 from '@/views/exception/500/locale/en-US';

import localeUserInfo from '@/views/user/info/locale/en-US';
import localeUserSetting from '@/views/user/setting/locale/en-US';

import localeSettings from './en-US/settings';

export default {
  'menu.dashboard': 'Dashboard',
  'menu.server.dashboard': 'Dashboard-Server',
  'menu.server.workplace': 'Workplace-Server',
  'menu.server.monitor': 'Monitor-Server',
  'menu.list': 'List',
  'menu.result': 'Result',
  'menu.exception': 'Exception',
  'menu.form': 'Form',
  'menu.profile': 'Profile',
  'menu.visualization': 'Data Visualization',
  'menu.user': 'User Center',
  'menu.arcoWebsite': 'Arco Design',
  'menu.faq': 'FAQ',
  'navbar.docs': 'Docs',
  'navbar.action.locale': 'Switch to English',

  // DHCP Server
  'enable': 'Enable',
  'disable': 'Disable',
  'submit': 'Submit',
  'cancel': 'Cancel',
  'import': 'Import',
  'export': 'Export',
  'add': 'Add',
  'delete': 'Delete',
  'edit': 'Edit',
  'edic_desc': 'Edit Description',
  'search_start_address': 'Search Start Address',

  // Address Pool Related
  'add_address_pool': 'Add Address Pool',
  'edit_address_pool': 'Edit Address Pool',
  'edit_description': 'Edit Description',
  'start_address': 'Start Address',
  'end_address': 'End Address',
  'network_mask': 'Network Mask',
  'gateway_address': 'Gateway Address',
  'dns_server_1': 'DNS Server 1',
  'dns_server_2': 'DNS Server 2',
  'time_seconds': 'Time (Seconds)',
  'port': 'Port',
  'vlan_id': 'VLAN ID',
  'description': 'Description',

  // Placeholder Text
  'please_enter_start_address': 'Please enter start address',
  'please_enter_end_address': 'Please enter end address',
  'please_enter_network_mask': 'Please enter network mask',
  'please_enter_gateway_address': 'Please enter gateway address',
  'please_enter_dns_server_1': 'Please enter DNS server 1',
  'please_enter_dns_server_2': 'Please enter DNS server 2',
  'please_enter_time': 'Please enter time',
  'please_enter_port': 'Please enter port',
  'please_enter_vlan_id': 'Please enter VLAN ID',
  'please_enter_description': 'Please enter description',

  // Validation Messages
  'start_address_required': 'Start address is required',
  'end_address_required': 'End address is required',
  'gateway_address_required': 'Gateway address is required',
  'dns_server_1_required': 'DNS server 1 is required',
  'dns_server_2_required': 'DNS server 2 is required',
  'network_mask_required': 'Network mask is required',
  'port_required': 'Port is required',
  'vlan_id_required': 'VLAN ID is required',
  'time_required': 'Time is required',
  'ip_format_error': 'IP address format is incorrect, should be: *******',
  'dns_format_error': 'DNS address format is incorrect, should be: *******',
  'mask_format_error':
    'Subnet mask format is incorrect, should be: *************',

  // Operation Messages
  'please_select_items_to_delete': 'Please select items to delete',
  'please_select_one_record_to_edit': 'Please select one record to edit',
  'delete_success': 'Delete successful',
  'add_success': 'Add successful',
  'edit_success': 'Edit successful',
  'description_edit_success': 'Description edit successful',
  'get_data_failed': 'Failed to get data',
  'edit_failed': 'Edit failed',
  'description_edit_failed': 'Description edit failed',
  'edit_request_failed': 'Edit request failed',
  'description_edit_request_failed': 'Description edit request failed',

  'Prohibit option': 'Prohibit option 60:',

  // Global Options Related
  'option_type': 'Option Type',
  'option_id': 'Option ID',
  'option_value': 'Option Value',
  'operation': 'Operation',
  'please_select': 'Please select',
  'string': 'String',
  'hexadecimal': 'Hexadecimal',
  'ip_address': 'IP Address',
  'number': 'Number',
  'please_enter_option_id': 'Please enter Option ID',
  'please_enter_option_value': 'Please enter Option value',
  'over_dhcp_option_num': 'Over DHCP option number',
  'please_fill_complete_option_info':
    'Please fill in complete Option information',
  'submit_success': 'Submit successful',
  'submit_failed': 'Submit failed',
  'submit_success_simulation': 'Submit successful (simulation)',
  'submit_request_failed': 'Submit request failed',
  'delete_failed': 'Delete failed',
  'delete_success_simulation': 'Delete successful (simulation)',
  'delete_request_failed': 'Delete request failed',

  ...localeSettings,
  ...localeMessageBox,
  ...localeLogin,
  ...localeWorkplace,

  ...localeMonitor,
  ...localeSearchTable,
  ...localeCardList,
  ...localeStepForm,
  ...localeGroupForm,
  ...localeBasicProfile,
  ...localeDataAnalysis,
  ...localeMultiDAnalysis,
  ...localeSuccess,
  ...localeError,
  ...locale403,
  ...locale404,
  ...locale500,
  ...localeUserInfo,
  ...localeUserSetting,
};
