import localeMessageBox from '@/components/message-box/locale/en-US';
import localeLogin from '@/views/login/locale/en-US';

import localeWorkplace from '@/views/dashboard/workplace/locale/en-US';

import localeMonitor from '@/views/dashboard/monitor/locale/en-US';

import localeSearchTable from '@/views/list/search-table/locale/en-US';
import localeCardList from '@/views/list/card/locale/en-US';

import localeStepForm from '@/views/form/step/locale/en-US';
import localeGroupForm from '@/views/form/group/locale/en-US';

import localeBasicProfile from '@/views/profile/basic/locale/en-US';

import localeDataAnalysis from '@/views/visualization/data-analysis/locale/en-US';
import localeMultiDAnalysis from '@/views/visualization/multi-dimension-data-analysis/locale/en-US';

import localeSuccess from '@/views/result/success/locale/en-US';
import localeError from '@/views/result/error/locale/en-US';

import locale403 from '@/views/exception/403/locale/en-US';
import locale404 from '@/views/exception/404/locale/en-US';
import locale500 from '@/views/exception/500/locale/en-US';

import localeUserInfo from '@/views/user/info/locale/en-US';
import localeUserSetting from '@/views/user/setting/locale/en-US';

import localeSettings from './en-US/settings';

export default {
  'menu.dashboard': 'Dashboard',
  'menu.server.dashboard': 'Dashboard-Server',
  'menu.server.workplace': 'Workplace-Server',
  'menu.server.monitor': 'Monitor-Server',
  'menu.list': 'List',
  'menu.result': 'Result',
  'menu.exception': 'Exception',
  'menu.form': 'Form',
  'menu.profile': 'Profile',
  'menu.visualization': 'Data Visualization',
  'menu.user': 'User Center',
  'menu.arcoWebsite': 'Arco Design',
  'menu.faq': 'FAQ',
  'navbar.docs': 'Docs',
  'navbar.action.locale': 'Switch to English',

  // DHCP Server
  'enable': 'Enable',
  'disable': 'Disable',
  'submit': 'Submit',
  'cancel': 'Cancel',
  'import': 'Import',
  'export': 'Export',
  'add': 'Add',
  'delete': 'Delete',
  'edit': 'Edit',
  'edic_desc': 'Edit Description',
  'search_start_address': 'Search Start Address',

  // Address Pool Related
  'add_address_pool': 'Add Address Pool',
  'edit_address_pool': 'Edit Address Pool',
  'edit_description': 'Edit Description',
  'start_address': 'Start Address',
  'end_address': 'End Address',
  'network_mask': 'Network Mask',
  'gateway_address': 'Gateway Address',
  'dns_server_1': 'DNS Server 1',
  'dns_server_2': 'DNS Server 2',
  'time_seconds': 'Time (Seconds)',
  'port': 'Port',
  'vlan_id': 'VLAN ID',
  'description': 'Description',

  // Placeholder Text
  'please_enter_start_address': 'Please enter start address',
  'please_enter_end_address': 'Please enter end address',
  'please_enter_network_mask': 'Please enter network mask',
  'please_enter_gateway_address': 'Please enter gateway address',
  'please_enter_dns_server_1': 'Please enter DNS server 1',
  'please_enter_dns_server_2': 'Please enter DNS server 2',
  'please_enter_time': 'Please enter time',
  'please_enter_port': 'Please enter port',
  'please_enter_vlan_id': 'Please enter VLAN ID',
  'please_enter_description': 'Please enter description',

  // Validation Messages
  'start_address_required': 'Start address is required',
  'end_address_required': 'End address is required',
  'gateway_address_required': 'Gateway address is required',
  'dns_server_1_required': 'DNS server 1 is required',
  'dns_server_2_required': 'DNS server 2 is required',
  'network_mask_required': 'Network mask is required',
  'port_required': 'Port is required',
  'vlan_id_required': 'VLAN ID is required',
  'time_required': 'Time is required',
  'ip_format_error': 'IP address format is incorrect, should be: *******',
  'dns_format_error': 'DNS address format is incorrect, should be: *******',
  'mask_format_error':
    'Subnet mask format is incorrect, should be: *************',

  // Operation Messages
  'please_select_items_to_delete': 'Please select items to delete',
  'please_select_one_record_to_edit': 'Please select one record to edit',
  'delete_success': 'Delete successful',
  'add_success': 'Add successful',
  'edit_success': 'Edit successful',
  'description_edit_success': 'Description edit successful',
  'get_data_failed': 'Failed to get data',
  'edit_failed': 'Edit failed',
  'description_edit_failed': 'Description edit failed',
  'edit_request_failed': 'Edit request failed',
  'description_edit_request_failed': 'Description edit request failed',

  'Prohibit option': 'Prohibit option 60:',

  // Global Options Related
  'option_type': 'Option Type',
  'option_id': 'Option ID',
  'option_value': 'Option Value',
  'operation': 'Operation',
  'please_select': 'Please select',
  'string': 'String',
  'hexadecimal': 'Hexadecimal',
  'ip_address': 'IP Address',
  'number': 'Number',
  'please_enter_option_id': 'Please enter Option ID',
  'please_enter_option_value': 'Please enter Option value',
  'over_dhcp_option_num': 'Over DHCP option number',
  'please_fill_complete_option_info':
    'Please fill in complete Option information',
  'submit_success': 'Submit successful',
  'submit_failed': 'Submit failed',
  'submit_success_simulation': 'Submit successful (simulation)',
  'submit_request_failed': 'Submit request failed',
  'delete_failed': 'Delete failed',
  'delete_success_simulation': 'Delete successful (simulation)',
  'delete_request_failed': 'Delete request failed',

  // Vendor ID Related
  'process_option_60': 'Process option 60:',
  'prohibit_option_tooltip': 'Prohibit packets matching vendor-id of option 60',
  'process_option_tooltip':
    'Only process packets matching vendor-id of option 60',
  'cancel_configuration': 'Cancel Configuration',
  'network_address_required': 'Network address is required',
  'no_permission': 'You do not have permission',
  'form_validation_failed': 'Form validation failed, please check input',
  'form_validation_error': 'Form validation process error occurred',
  'configuration_success': 'Configuration successful',
  'configuration_failed': 'Configuration failed',
  'configuration_request_failed': 'Configuration request failed',

  // DHCP Relay Related
  'yes': 'Yes',
  'no': 'No',
  'port_number': 'Port Number',
  'dhcp_relay_server_address': 'DHCP Relay Server Address:',
  'dhcp_relay_local_address': 'DHCP Relay Local Address:',
  'please_enter_dhcp_relay_server_address':
    'Please enter DHCP relay server address',
  'please_enter_dhcp_relay_local_address':
    'Please enter DHCP relay local address',
  'dhcp_relay_server_address_required': 'DHCP relay server address is required',
  'dhcp_relay_local_address_required': 'DHCP relay local address is required',
  'accept_user_dhcp_relay_request': 'Accept user DHCP relay requests',
  'get_dhcp_relay_config_failed': 'Failed to get DHCP Relay configuration',
  'dhcp_relay_config_save_success':
    'DHCP Relay configuration saved successfully',
  'configuration_save_failed': 'Configuration save failed',
  'save_request_failed': 'Save request failed',

  // Fixed Address Allocation Related
  'define_dhcp_pool_fixed_allocation_mac':
    'Define MAC addresses for fixed allocation in DHCP pool',
  'export_result': 'Export Result',
  'mac_address': 'MAC Address',
  'please_enter_ip_address': 'Please enter IP address',
  'please_enter_mac_address': 'Please enter MAC address',
  'import_fixed_address': 'Import Fixed Address',
  'import_file': 'Import File',
  'choose_file': 'Choose File',
  'note': 'Note',
  'import_note_1': 'Only import content files separated by spaces is supported',
  'sample_download': 'Sample download',
  'import_note_2':
    'The Internet Protocol Address and MAC address cannot be repeated, otherwise the execution of changing the configuration parameters will fail',
  'format_description_example': 'Format description (example)',
  'ip_address_mac_address': 'IP-Address MAC-Address',
  'ip_address_required': 'IP address is required',
  'mac_address_required': 'MAC address is required',
  'mac_format_error':
    'MAC address format is incorrect, should be: 11:22:33:44:55:66',
  'please_select_file_to_import': 'Please select a file to import',
  'import_success': 'Import successful',
  'export_function_not_implemented': 'Export function not implemented yet',
  'export_result_function_not_implemented':
    'Export result function not implemented yet',
  'new_row_added_please_fill_info':
    'New row added, please fill in the information',

  // Address Pool Options Related
  'pool_option_warning':
    'Note: If the address pool defines options, global options will not take effect',
  'integer': 'Integer',
  'hex_character': 'Hex Character',
  'edit_option_start_address': 'Edit option, start address: ',
  'end_address_colon': ', end address: ',
  'edit_option': 'Edit Option',
  'get_pool_data_failed': 'Failed to get address pool data',
  'save_success': 'Save successful',
  'save_failed': 'Save failed',

  // Multi DNS Related
  'multi_dns_config_tip': 'Configure 0.0.0.0 to delete configuration',
  'operate': 'Operate',

  ...localeSettings,
  ...localeMessageBox,
  ...localeLogin,
  ...localeWorkplace,

  ...localeMonitor,
  ...localeSearchTable,
  ...localeCardList,
  ...localeStepForm,
  ...localeGroupForm,
  ...localeBasicProfile,
  ...localeDataAnalysis,
  ...localeMultiDAnalysis,
  ...localeSuccess,
  ...localeError,
  ...locale403,
  ...locale404,
  ...locale500,
  ...localeUserInfo,
  ...localeUserSetting,
};
