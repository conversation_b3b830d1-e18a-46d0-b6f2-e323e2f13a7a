<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />
    <a-form
      ref="formRef"
      :model="formData"
      layout="horizontal"
      :label-col-props="{ span: 3 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item field="work" class="uniform-form-item">
        <template #label>
          <span class="form-label">共享端口：</span>
        </template>
        <a-tooltip
          content="可实时配置"
          position="tl"
          background-color="#3491FA"
        >
          <a-select :style="{ width: '100px' }" v-model="formData.work">
            <a-option value="disable">关闭</a-option>
            <a-option value="enable">开启</a-option>
          </a-select>
        </a-tooltip>
      </a-form-item>
      <a-form-item field="mode" class="uniform-form-item">
        <template #label>
          <span class="form-label">端口模式：</span>
        </template>
        <a-tooltip
          content="可实时配置"
          position="tl"
          background-color="#3491FA"
        >
          <a-select :style="{ width: '100px' }" v-model="formData.mode">
            <a-option value="share">共享</a-option>
            <a-option value="enable">连接</a-option>
          </a-select>
        </a-tooltip>
      </a-form-item>
      <a-form-item field="port" class="uniform-form-item">
        <template #label>
          <span class="form-label">绑定虚拟主机端口：</span>
        </template>
        <a-tooltip
          content="可实时配置"
          position="tl"
          background-color="#3491FA"
        >
          <a-select :style="{ width: '100px' }" v-model="formData.port">
            <a-option value="0">0</a-option>
            <a-option value="1">1</a-option>
            <a-option value="2">2</a-option>
            <a-option value="3">3</a-option>
            <a-option value="4">4</a-option>
          </a-select>
        </a-tooltip>
      </a-form-item>
      <a-form-item :rules="rules.ip" field="ip" class="uniform-form-item">
        <template #label>
          <span class="form-label">Linux主机IP地址</span>
        </template>
        <a-tooltip
          content="格式：*******"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.ip"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>

      <a-form-item :rules="rules.mask" field="mask" class="uniform-form-item">
        <template #label>
          <span class="form-label"> Linux主机子网掩码：</span>
        </template>
        <a-tooltip
          content="格式：*************"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.mask"
            placeholder=""
            :style="{ width: '200px' }"
          />
        </a-tooltip>
      </a-form-item>

      <a-form-item :rules="rules.ip" field="gateway" class="uniform-form-item">
        <template #label>
          <span class="form-label"> linux主机启动缺省路由：</span>
        </template>
        <a-tooltip
          content="格式：*******"
          position="tl"
          background-color="#3491FA"
        >
          <a-input
            v-model="formData.gateway"
            placeholder=""
            :style="{ width: '200px' }"
        /></a-tooltip>
      </a-form-item>

      <a-form-item>
        <a-button
          :disabled="!hasPermission('evrrpSubmit')"
          type="primary"
          @click="saveAction"
        >
          <template #icon>
            <icon-check />
          </template>
          <template #default>提交</template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4, isValidMask } from '@/utils/validate';
  import message from '@/utils/message';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const formRef = ref(null);

      const formData = reactive({
        port: '',
        work: '',
        mode: '',
        ip: '',
        mask: '',
        gateway: '',
      });
      const rules = {
        ip: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        mask: [
          { required: true, message: '网络地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidMask(value)) {
                callback('子网掩码格式不正确，应为：*************');
              } else {
                callback();
              }
            },
          },
        ],
      };
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_other.lua',
            new URLSearchParams({ act: 'kni' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            formData.work = response.data.data.work || '';
            formData.port = response.data.data.port || '';
            formData.mode = response.data.data.mode || '';
            formData.ip = response.data.data.ip || '';
            formData.mask = response.data.data.mask || '';
            formData.gateway = response.data.data.gateway || '';
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          Message.error('获取数据失败');
        }
      };

      const saveAction = async () => {
        if (!hasPermission('evrrpSubmit')) {
          Message.error('您没有权限');
          return;
        }
        try {
          const errors = await formRef.value.validate();
          // Arco Design表单验证失败时会返回errors对象
          if (errors) {
            Message.error('表单验证失败，请检查输入');
            return;
          }
        } catch (validationError) {
          Message.error('表单验证过程发生错误');
          console.error('Validation error:', validationError);
          return;
        }
        try {
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'evrrp',
              act_type: 'mod',
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            Message.success(response.data.result || '配置成功');
          } else {
            Message.error(response.data.err || '配置失败');
          }
        } catch (error) {
          Message.error('配置请求失败');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        formData,
        formRef,
        saveAction,
        hasPermission,
        rules,
      };
    },
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    text-align: right;
  }

  :deep(.arco-form-item) {
    margin-bottom: 20px;
  }

  :deep(.arco-form-item-label-required:before) {
    margin-right: 2px;
  }
</style>
