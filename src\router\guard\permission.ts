import type { Router, RouteRecordNormalized } from 'vue-router';
import NProgress from 'nprogress'; // progress bar

import usePermission from '@/hooks/permission';
import { useUserStore, useAppStore } from '@/store';
import { appRoutes } from '../routes';
import { WHITE_LIST, NOT_FOUND } from '../constants';

const checkRoutePermission = (to: any, userStore: any) => {
  if (!to.meta?.requiresAuth) return true;
  if (!to.meta?.permission) return true;
  return userStore.hasPermission(to.meta.permission);
};

export default function setupPermissionGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const appStore = useAppStore();
    const userStore = useUserStore();
    const Permission = usePermission();
    const permissionsAllow =
      Permission.accessRouter(to) && checkRoutePermission(to, userStore);

    if (appStore.menuFromServer) {
      // 针对来自服务端的菜单配置进行处理
      // 如果菜单为空但有权限信息，说明可能是刷新后的状态，需要重新获取菜单
      if (
        (!appStore.appAsyncMenus.length ||
          appStore.appAsyncMenus.length === 0) &&
        !WHITE_LIST.find((el) => el.name === to.name) &&
        userStore.permissions.length > 0
      ) {
        try {
          await appStore.fetchServerMenuConfig(false);
        } catch (error) {
          console.error('Failed to fetch menu config:', error);
        }
      }

      const serverMenuConfig = [...appStore.appAsyncMenus, ...WHITE_LIST];

      let exist = false;
      while (serverMenuConfig.length && !exist) {
        const element = serverMenuConfig.shift();
        if (element?.name === to.name) exist = true;

        if (element?.children) {
          serverMenuConfig.push(
            ...(element.children as unknown as RouteRecordNormalized[])
          );
        }
      }
      if (exist && permissionsAllow) {
        next();
      } else next(NOT_FOUND);
    } else {
      // eslint-disable-next-line no-lonely-if
      if (permissionsAllow) next();
      else {
        const destination =
          Permission.findFirstPermissionRoute(appRoutes, userStore.role) ||
          NOT_FOUND;
        next(destination);
      }
    }
    NProgress.done();
  });
}
