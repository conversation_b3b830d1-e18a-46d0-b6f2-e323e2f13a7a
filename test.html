<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POST 请求示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        #response {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            white-space: pre-wrap;
        }
    </style>
</head>

<body>
    <h1>POST 请求示例</h1>
    <button id="sendRequest">发送 POST 请求</button>
    <div id="response">响应结果将显示在这里</div>

    <script>
        // 绑定按钮点击事件
        document.getElementById('sendRequest').addEventListener('click', function () {
            sendPostRequest();
        });

        // 发送 POST 请求的函数
        function sendPostRequest() {
            // 目标 URL
            const url = ' http://*************:8088/lua/get_cfg_ipv6_setting.lua'
            // 请求参数
            const data = {
                act:'qinq_cvlan'
            };

            // 将对象转换为 URL 编码格式
            const formData = new URLSearchParams();
            for (const key in data) {
                formData.append(key, data[key]);
            }

            // 发送 POST 请求
            fetch(url, {
                method: 'POST', // 请求方法
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded' // 请求头，表示发送 URL 编码数据
                },
                body: formData // 将参数转换为 URL 编码格式
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应失败');
                    }
                    return response.json(); // 解析响应为 JSON
                })
                .then(result => {
                    // 显示响应结果
                    document.getElementById('response').textContent = JSON.stringify(result, null, 2);
                })
                .catch(error => {
                    // 显示错误信息
                    document.getElementById('response').textContent = '请求失败: ' + error.message;
                });
        }
    </script>
</body>

</html>