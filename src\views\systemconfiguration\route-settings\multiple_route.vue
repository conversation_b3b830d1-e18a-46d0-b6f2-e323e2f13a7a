<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row>
      <a-col :flex="1">
        <a-form
          :model="{ searchQuery }"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
        >
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="number" label="IP地址">
                <a-input v-model="searchQuery" />
                <a-button
                  type="primary"
                  @click="filterData"
                  style="margin-left: 10px"
                >
                  <template #icon>
                    <icon-search />
                  </template>
                  {{ $t('searchTable.form.search') }}
                </a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
      <a-col :flex="'86px'" style="text-align: right"> </a-col>
    </a-row>
    <a-divider style="margin-top: 0" />
    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-space>
          <!-- 新建按钮 -->
          <a-button
            id="portAddressAdd"
            :disabled="!hasPermission('portAddressAdd')"
            type="primary"
            @click="showModal"
          >
            <template #icon>
              <icon-plus />
            </template>
            {{ $t('searchTable.operation.create') }}
          </a-button>
        </a-space>
      </a-col>
      <!-- 右侧按钮 -->
      <a-col
        :span="12"
        style="display: flex; align-items: center; justify-content: end"
      >
        <!-- 刷新按钮 -->
        <a-tooltip :content="$t('searchTable.actions.refresh')">
          <div class="action-icon" @click="handleRefresh">
            <icon-refresh size="18" />
          </div>
        </a-tooltip>
      </a-col>
    </a-row>

    <!-- 列表 -->
    <a-table
      :columns="columns"
      :data="paginatedData"
      style="margin-top: 20px"
      :pagination="false"
    >
      <!-- <template #ip="{ record }">
        <span>{{ record.ip }}</span>
      </template>
      <template #mask="{ record }">
        <span>{{ record.mask }}</span>
      </template>
      <template #port="{ record }">
        <span>{{ record.port }}</span>
      </template>
      <template #vlan="{ record }">
        <span>{{ record.vlan }}</span>
      </template> -->
      
      <template #desc="{ record }">
        <a-input v-model="record.desc" />
      </template>

      <template #option="{ record }">
        <a-button
          id="portAddressDelete"
          :disabled="!hasPermission('portAddressDelete')"
          status="danger"
          @click="deleteRow(record)"
        >
          <template #icon>
            <IconMinus />
          </template>
          <template #default>删除</template>
        </a-button>
        <a-button
          id="portAddressMod"
          :disabled="!hasPermission('portAddressMod')"
          type="primary"
          @click="editRow(record)"
          style="margin-left: 2%;"
        >
          <template #icon>
            <IconEdit />
          </template>
          <template #default>编辑</template>
        </a-button>
      </template>
    </a-table>
    <a-pagination
      v-model:current="currentPage"
      v-model:page-size="pageSize"
      :total="filteredData.length"
      show-total
      show-size-changer
      show-jumper
      show-page-size
      style="margin-top: 20px"
    />
  </a-card>

  <a-modal
    v-model:visible="isModalVisible"
    title="添加"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
    >    
    <a-form :model="formData" :rules="rules" ref="formRef">
      <a-form-item label="IP地址" field="ip">
        <a-input v-model="formData.ip" />
      </a-form-item>
      <a-form-item label="子网掩码" field="mask">
        <a-input v-model="formData.mask" />
      </a-form-item>
      <a-form-item label="端口" field="port">
        <a-select v-model="formData.port" :style="{ width: '200px' }" :options="optionsGroup" />
      </a-form-item>
      <a-form-item label="绑定vlan" field="vlan">
        <a-input-number v-model="formData.vlan" />
      </a-form-item>
      <a-form-item label="描述" field="desc">
        <a-input v-model="formData.desc" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    onBeforeUnmount,
    computed,
    watch,
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidIPv4,isValidMask } from "@/utils/validate";

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      const formData = reactive({
        ip: '',
        mask: '',
        port: '',
        vlan: null,
        desc: ''
      });

      const currentPage = ref(1);
      const pageSize = ref(10);

      const paginatedData = computed(() => {
        const start = (currentPage.value - 1) * pageSize.value;
        return filteredData.value.slice(start, start + pageSize.value);
      });

      const columns = [
        {
          title: 'IP地址',
          dataIndex: 'ip',
          slotName: 'ip',
        },
        {
          title: '子网掩码',
          dataIndex: 'mask',
          slotName: 'mask',
        },
        {
          title: '端口',
          dataIndex: 'port',
          slotName: 'port',
        },
        {
          title: '绑定vlan',
          dataIndex: 'vlan',
          slotName: 'vlan',
        },
        {
          title: '描述',
          dataIndex: 'desc',
          slotName: 'desc',
        },
        {
          title: '操作',
          dataIndex: 'option',
          slotName: 'option',
        },
      ] as {
        title: string;
        dataIndex: string;
        slotName?: string;
      }[];
      const data = ref<any[]>([]);
      const filteredData = ref<any[]>([]);

      const isModalVisible = ref(false);
      const isComponentVisible = ref(true);
      const isRefreshing = ref(false);
      const searchQuery = ref('');

      const rules = {
        ip: [
          { required: true, message: 'IP地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        mask: [
          { required: true, message: '子网掩码不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!isValidMask(value)) {
                callback('子网掩码格式不正确，应为：*************');
              } else {
                callback();
              }
            },
          },
        ],
        port: [
          { required: true, message: '端口不能为空' },
        ],
        vlan: [
          { required: true, message: '绑定vlan不能为空' },
          {
            validator: (value: number, callback: (error?: string) => void) => {
              if (0>Number(value) || Number(value) > 4095) {
                callback('vlan的范围为0~4095');
              } else {
                callback();
              }
            },
          },
        ],
      };
      const formRef = ref();

      interface SelectOption {
        label: string;
        value: string;
      }
      const optionsGroup = ref<SelectOption[]>([]);  
      const getPorts = async () => {
        try {
          const response = await axios.post(
            '/lua/vm.lua',
            new URLSearchParams({ act: 'get_vm_port' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            
            const formattedData: SelectOption[] = [];    

            response.data.data.port.forEach((item: string) => {
              formattedData.push({ 
                label: String(item), 
                value: String(item),  
              });   
            });
              
            optionsGroup.value = formattedData;
            
          } else {
            Message.error(response.data.result || '获取端口失败');
          }
        } catch (error) {
          Message.error('获取端口错误');
        }
      };
      
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_port.lua',
            new URLSearchParams({ act: 'port_address' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          // console.log(response);
          if (response.data.code === 200) {
            data.value = response.data.data || [];
            filteredData.value = [...data.value];
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Fetch error:', error);
          Message.error('获取数据失败');
        }
      };
      
      const deleteRow = async (record: { ip: string}) => {
        try {
          if (!hasPermission('portAddressDelete')) {
            Message.error('您没有权限');
            return;
          }

          const { ip } = record;
          // 发送删除请求
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'port_address',
              act_type: 'del',
              ip,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          if (response.data.code === 200) {
            // 删除成功后重新获取数据
            await fetchData();
            Message.success(response.data.result);
          } else {
            Message.error(response.data.err);
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('删除失败');
        }
      };
      
      const editRow = async (record: { ip: string; mask: string; desc: string }) => {
        try {
          if (!hasPermission('portAddressDelete')) {
            Message.error('您没有权限');
            return;
          }

          const { ip, mask, desc } = record;
          const response = await axios.post(
            '/lua/set_cfg_port.lua',
            new URLSearchParams({
              act: 'port_address',
              act_type: 'edit',
              ip,
              mask,
              desc,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );

          console.log(response)
          if (response.data.code === 200) {
            await fetchData();
            Message.success(response.data.result);
          } else {
            Message.error(response.data.err);
          }
        } catch (error) {
          console.error('Error deleting data:', error);
          Message.error('编辑失败');
        }
      };

      // 显示模态框
      const showModal = () => {
        if (!hasPermission('portAddressAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 清空表单
        formData.ip = '';
        formData.mask = '';
        isModalVisible.value = true;
      };

      // 处理确认
      const handleBeforeOk = async (done) => {
        if (!hasPermission('portAddressAdd')) {
          Message.error('您没有权限');
          return;
        }
        // 只通过表单验证规则验证
        formRef.value.validate((errors) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 表单验证通过后，发送添加请求
          const { ip, mask, port, vlan, desc } = formData;

          axios
            .post(
              '/lua/set_cfg_port.lua',
              new URLSearchParams({
                act: 'port_address',
                act_type: 'add',
                ip,
                mask,
                port,
                vlan,
                desc
              }),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              }
            )
            .then((response) => {
              if (response.data.code === 200) {
                fetchData();
                Message.success('添加成功');
                // 成功后清空表单
                formData.ip = '';
                formData.mask = '';
                formData.port = '';
                formData.vlan = '';
                formData.desc = '';
                done(true); // 允许模态框关闭
                isModalVisible.value = false;
              } else {
                Message.error({
                  content: response.data.err,
                  duration: 5000,
                });
                done(false); // 阻止模态框关闭
              }
            })
            .catch((error) => {
              console.error('Error adding data:', error);
              Message.error('添加失败');
              done(false); // 阻止模态框关闭
            });
        });

        return false;
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      // 刷新数据
      const handleRefresh = () => {
        isRefreshing.value = true;
        fetchData().finally(() => {
          isRefreshing.value = false;
          Message.success('刷新成功');
        });
      };

      // 过滤数据
      const filterData = () => {
        const rawData = data.value;

        if (searchQuery.value.trim() === '') {
          filteredData.value = [...rawData];
          return;
        }

        filteredData.value = rawData.filter((item) =>
          item.ip.includes(searchQuery.value)
        );

        if (filteredData.value.length > 0) {
          Message.success('查询成功');
        } else {
          Message.error('未找到相关数据');
        }
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            getPorts();
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          getPorts();
          fetchData();
        }
      });

      return {
        formData,
        hasPermission,
        columns,
        data,
        filteredData,
        isModalVisible,
        rules,
        formRef,
        showModal,
        handleBeforeOk,
        handleCancel,
        isComponentVisible,
        handleRefresh,
        isRefreshing,
        searchQuery,
        filterData,
        deleteRow,
        editRow,
        paginatedData,
        currentPage,
        pageSize,
        optionsGroup,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }
  .form-label {
    display: inline-block;
    width: 90px; /* 调整这个值 */
    text-align: right;
    margin-right: 8px;
  }
</style>
