<!-- 接入端口组件 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.system-configuration', 'menu.access-port']" />

    <a-card title="接入端口">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-row :gutter="20" :style="{ marginBottom: '20px' }">
          <a-col :span="8">
            <a-card
              title="接入端口"
              :bordered="false"
              :style="{ width: '100%' }"
            >
              <a-alert type="info" banner closable>
                <template #title>
                  <span style="font-size: 15px"
                    >注意：配置后需要重新启动虚机才能生效
                  </span>
                </template>
              </a-alert>
              <br />
              <template #extra> </template>
              <div class="session">
                <div class="form-item">
                  <span>端口：</span>
                  <a-tooltip
                    content="若绑定多个端口，请以'-'隔开，最多可输入4个"
                    position="tr"
                    background-color="#3491FA"
                  >
                    <a-input
                      v-model="formData.port"
                      class="input-field"
                      :status="portError ? 'error' : undefined"
                    />
                  </a-tooltip>
                  <div v-if="portError" class="error-message">{{
                    portError
                  }}</div>
                </div>
                <div class="form-item">
                  <span>入口地址：</span>
                  <a-tooltip
                    content="格式为：***********"
                    position="tr"
                    background-color="#3491FA"
                  >
                    <a-input
                      v-model="formData.ip"
                      class="input-field"
                      :status="ipError ? 'error' : undefined"
                    />
                  </a-tooltip>
                  <div v-if="ipError" class="error-message">{{ ipError }}</div>
                </div>
                <div class="form-item">
                  <span>子网掩码：</span>
                  <a-tooltip
                    content="格式为：*************"
                    position="tr"
                    background-color="#3491FA"
                  >
                    <a-input
                      v-model="formData.mask"
                      class="input-field"
                      :status="maskError ? 'error' : undefined"
                    />
                  </a-tooltip>
                  <div v-if="maskError" class="error-message">{{
                    maskError
                  }}</div>
                </div>
                <div class="form-item">
                  <span>网关：</span>
                  <a-tooltip
                    content="格式为：***********"
                    position="tr"
                    background-color="#3491FA"
                  >
                    <a-input
                      v-model="formData.gateway"
                      class="input-field"
                      :status="gatewayError ? 'error' : undefined"
                    />
                  </a-tooltip>
                  <div v-if="gatewayError" class="error-message">{{
                    gatewayError
                  }}</div>
                </div>
                <div class="form-item">
                  <span>VLAN：</span>
                  <a-input v-model="formData.value" class="input-field" />
                </div>
                <br />

                <div class="button">
                  <a-button
                    id="'accessPortSubmit'"
                    :disabled="!hasPermission('accessPortSubmit')"
                    type="primary"
                    @click="handleNotification1"
                  >
                    <template #icon>
                      <icon-check />
                    </template>
                    <template #default>提交</template>
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';

  import {
    handleNotification4,
    handleNotification1,
    handleNotification2,
  } from '../../../utils/info';

  const { hasPermission } = usePermission();

  export default defineComponent({
    setup() {
      const formData = reactive({
        port: '',
        gateway: '',
        mask: '',
        value: '',
        ip: '',
      });
      const userStore = useUserStore();

      // 添加错误状态
      const ipError = ref('');
      const maskError = ref('');
      const gatewayError = ref('');
      const portError = ref('');

      // 验证IP地址格式
      const validateIpAddress = (ip: string): boolean => {
        const ipRegex =
          /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
      };

      // 验证子网掩码
      const validateSubnetMask = (mask: string): boolean => {
        // 检查是否符合IP地址格式
        if (!validateIpAddress(mask)) {
          return false;
        }

        // 检查是否是有效的子网掩码
        const parts = mask.split('.');
        const binary = parts
          .map((part) => parseInt(part, 10).toString(2).padStart(8, '0'))
          .join('');

        // 有效的子网掩码应该是连续的1后跟连续的0
        return /^1*0*$/.test(binary);
      };

      // 验证端口
      const validatePort = (port: string): boolean => {
        if (!port) return false;

        // 检查单个端口或多个端口（以'-'分隔）
        const ports = port.split('-');

        // 最多可以有4个端口
        if (ports.length > 4) return false;

        // 验证每个端口是否是有效的数字且在范围内
        return ports.every((p) => {
          const num = parseInt(p, 10);
          return !Number.isNaN(num) && num >= 0 && num <= 65535;
        });
      };

      onMounted(async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg.lua',
            new URLSearchParams({ act: 'input' }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            formData.port = response.data.data.port;
            formData.gateway = response.data.data.gateway;
            formData.mask = response.data.data.mask;
            formData.value = response.data.data.value;
            formData.ip = response.data.data.ip;
          } else {
            console.error('Failed to fetch data:', response.data.error);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      });

      // 校验所有字段
      const validateAllFields = (): boolean => {
        let isValid = true;

        // 验证IP地址
        if (!formData.ip) {
          ipError.value = '入口地址不能为空';
          isValid = false;
        } else if (!validateIpAddress(formData.ip)) {
          ipError.value = '入口地址格式不正确，应为：***********';
          isValid = false;
        } else {
          ipError.value = '';
        }

        // 验证子网掩码
        if (!formData.mask) {
          maskError.value = '子网掩码不能为空';
          isValid = false;
        } else if (!validateSubnetMask(formData.mask)) {
          maskError.value = '子网掩码格式不正确，请重新输入';
          isValid = false;
        } else {
          maskError.value = '';
        }

        // 验证网关
        if (!formData.gateway) {
          gatewayError.value = '网关不能为空';
          isValid = false;
        } else if (!validateIpAddress(formData.gateway)) {
          gatewayError.value = '网关格式不正确，请重新输入';
          isValid = false;
        } else {
          gatewayError.value = '';
        }

        // 验证端口
        if (!formData.port) {
          portError.value = '端口不能为空';
          isValid = false;
        } else if (!validatePort(formData.port)) {
          portError.value = '端口格式不正确，请重新输入';
          isValid = false;
        } else {
          portError.value = '';
        }

        return isValid;
      };

      // 将设置数据的逻辑移动到一个方法中
      const submitData = async () => {
        if (!hasPermission('accessPortSubmit')) {
          Message.error({
            content: '您没有权限',
            duration: 5000,
          });
          return;
        }

        // 在提交前验证所有字段
        if (!validateAllFields()) {
          Message.error({
            content: '请修正表单中的错误再提交',
            duration: 5000,
          });
          return;
        }

        try {
          if (!hasPermission('accessPort')) {
            Message.error('您没有权限');
            return;
          }

          // console.log('提交的数据:', {
          //   port: formData.port,
          //   ip: formData.ip,
          //   mask: formData.mask,
          //   value: formData.value,
          //   gateway: formData.gateway,
          // });

          const response = await axios.post(
            '/lua/set_cfg.lua',
            new URLSearchParams({
              act: 'input',
              port: formData.port,
              ip: formData.ip,
              mask: formData.mask,
              gateway: formData.gateway,
              vlan: formData.value,
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          );
          if (response.data.code === 200) {
            Message.success(response.data.data.result);
          } else {
            Message.error({
              content: response.data.err,
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('Error setting data:', error);
        }
      };

      return {
        handleNotification4,
        handleNotification1: submitData, // 更新按钮点击事件处理函数
        handleNotification2,
        formData,
        hasPermission,
        // 添加错误状态到返回值
        ipError,
        maskError,
        gatewayError,
        portError,
      };
    },
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }

  /* 使用 CSS Grid 来控制每行显示多少个 a-descriptions */
  .descriptions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示4个 a-descriptions */
    gap: 16px; /* 控制每个组件之间的间距 */
  }

  .general-card {
    width: 100%;
  }

  .hint {
    flex: 1; /* 占据剩余空间 */
    text-align: left;
    color: gray;
    color: red;
    visibility: hidden;
    font-size: 16px;
  }

  .form-item:hover .hint {
    visibility: visible;
  }
  .button {
    text-align: center;
  }

  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .arco-input-wrapper {
    margin: 10px 0 !important;
  }

  .error-message {
    color: red;
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
</style>
