<template>
  <div class="container">
    <Breadcrumb :items="['menu.Operator management', 'menu.project title']" />

    <a-card title="项目标题设置">
      <div
        :style="{
          boxSizing: 'border-box',
          width: '100%',
          padding: '20px',
          backgroundColor: 'var(--color-fill-2)',
        }"
      >
        <a-row :gutter="20" :style="{ marginBottom: '20px' }">
          <a-col :span="8">
            <a-card
              title="项目标题设置"
              :bordered="false"
              :style="{ width: '100%' }"
            >
              <template #extra> </template>
              <div class="title-form">
                <div class="form-item">
                  <span>当前网页标题：</span>
                  <a-input
                    v-model="formData.current_title"
                    class="input-field"
                    disabled
                  />
                </div>
                <div class="form-item">
                  <span>新网页标题：</span>
                  <a-input
                    v-model="formData.new_title"
                    class="input-field"
                    placeholder="请输入新的网页标题"
                  />
                </div>
                <br />

                <div class="button">
                  <a-button
                    id="projectTitleSubmit"
                    :disabled="!hasPermission('projectTitleSubmit')"
                    type="primary"
                    @click="submitTitle"
                  >
                    <template #icon>
                      <icon-check />
                    </template>
                    <template #default>提交</template>
                  </a-button>

                  <a-button
                    type="secondary"
                    style="margin-left: 10px"
                    @click="resetForm"
                  >
                    <template #icon>
                      <icon-refresh />
                    </template>
                    <template #default>重置</template>
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, onMounted } from 'vue';
  import axios from 'axios';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { fetchTitleFromApi, setAndSaveAppTitle } from '@/utils/title';

  const userStore = useUserStore();
  const { hasPermission } = usePermission();
  const formData = reactive({
    current_title: '',
    new_title: '',
  });

  // 重置表单
  const resetForm = () => {
    formData.new_title = '';
  };

  // 获取当前网页标题
  const getCurrentTitle = async () => {
    try {
      const title = await fetchTitleFromApi();
      if (title) {
        formData.current_title = title;
      } else {
        formData.current_title = document.title;
      }
    } catch (error) {
      console.error('获取网页标题失败:', error);
      formData.current_title = document.title;
      Message.error('获取网页标题失败');
    }
  };

  // 提交新标题
  const submitTitle = async () => {
    // 表单验证
    if (!hasPermission('projectTitleSubmit')) {
      Message.error('您没有权限');
      return;
    }
    if (!formData.new_title) {
      Message.error('请输入新的网页标题');
      return;
    }

    try {
      const response = await axios.post(
        '/lua/project_title.lua',
        new URLSearchParams({
          act: 'set',
          project_title: formData.new_title,
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data.code === 200) {
        Message.success('网页标题修改成功');
        // 更新当前标题
        formData.current_title = formData.new_title;

        // 使用工具函数设置并保存标题
        setAndSaveAppTitle(formData.new_title);

        // 重置表单
        resetForm();
      } else {
        Message.error((response as any)?.result || '网页标题修改失败');
      }
    } catch (error) {
      console.error('修改网页标题失败:', error);
      Message.error('网页标题修改请求失败');
    }
  };

  onMounted(() => {
    // 获取当前网页标题
    getCurrentTitle();
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .title-form {
    width: 100%;
  }

  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .form-item span {
    width: 120px;
    text-align: right;
    margin-right: 12px;
  }

  .input-field {
    flex: 1;
  }

  .button {
    margin-top: 20px;
    text-align: center;
  }

  .arco-alert-with-title {
    padding: 0px 5px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
</style>
