<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.radClient-status']" />
    <a-card title="接口信息">
        <!-- <a-space direction="vertical" size="large" fill>
          <a-descriptions title="" :column="{xs:1, md:3, lg:3}" bordered>
            <a-descriptions-item v-for="item of data" :label="item.label">
              <a-tag>{{ item.value }}</a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </a-space>          -->
        <a-space direction="vertical" size="large" fill>
          <a-descriptions v-for="item of data" title="" :column="{xs:1, md:3, lg:3}" bordered>
            <a-descriptions-item v-for="i of item" :label="i.label">
              {{ i.value }}
            </a-descriptions-item>
          </a-descriptions>
        </a-space>
    </a-card>
  </div>
</template>

<script lang="ts" setup>

  import { ref, onMounted } from 'vue';
  import axios from 'axios';

  const data = ref([]);

  onMounted(async () => {
    try {
      const response = await axios.post(
        '/lua/system_info.lua',
        new URLSearchParams({ act: 'auth_acct_status' }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data?.code === 200) {
        const formattedData: Record<string, any>[] = [];
        let currentPortData: Record<string, any> = [];
        
        response.data.data.forEach((item) => {          
          if(item.describe == ""){
            formattedData.push(currentPortData);
            currentPortData = [];
          }else{
            currentPortData.push({'label': item.describe, 'value': item.data});
          }
        });      
        
        data.value = formattedData;
        console.log(formattedData)
      } else {
        console.error('Error:', response.data?.err || 'No data');
      }
    } catch (error) {
      console.error('API error:', error);
    }
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .a-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
</style>
