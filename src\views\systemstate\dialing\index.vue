<!-- 拨号状态组件 -->
<template>
  <div class="container">
    <Breadcrumb :items="['menu.systemstate', 'menu.dialing']" />
    <a-card title="拨号状态">
      <div class="dialing">
        <span>状态：</span>
        <a-select
          v-model="selectedStatus"
          :style="{ width: '200px' }"
          style="margin-right: 20px"
          placeholder="显示全部"
        >
          <a-option value="">显示全部</a-option>
          <a-option value="snifferUp">snifferUp</a-option>
          <a-option value="Up">Up</a-option>
          <a-option value="discovery">discovery</a-option>
        </a-select>
        <a-button
          id="dialStatusFilter"
          :disabled="!hasPermission('dialStatusFilter')"
          type="primary"
          style="margin-right: 20px"
          @click="filterData"
        >
          <template #icon>
            <icon-search />
          </template>
          <template #default>筛选</template>
        </a-button>

        <a-button
          id="dialStatusExport"
          :disabled="!hasPermission('dialStatusExport')"
          type="secondary"
          @click="exportStatusAccount"
        >
          <template #icon>
            <icon-share-external />
          </template>
          <template #default>导出状态账号</template>
        </a-button>
        <div class="dialing2">
          <a-table
            :columns="columns"
            :data="filteredDialingData"
            column-resizable
            :pagination="false"
          />
        </div>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { Message } from '@arco-design/web-vue';

  const userStore = useUserStore();
  const { hasPermission } = usePermission();
  interface Dialingdata {
    key: string;
    dialUpUsername: string;
    status: string;
    localMac: string;
    serverMac: string;
    localAddress: string;
    sessionId: string;
    mtu: string;
    userCount: string;
    idleTime: string;
    receivedBytes: string;
    sentBytes: string;
    downloadSpeed: string;
    uploadSpeed: string;
  }
  const dialingdata = ref<Dialingdata[]>([]);

  const columns = [
    {
      title: '拨号用户名',
      dataIndex: 'dialUpUsername',
    },
    {
      title: '状态',
      dataIndex: 'status',
    },
    {
      title: '本机mac',
      dataIndex: 'localMac',
    },
    {
      title: '服务器mac',
      dataIndex: 'serverMac',
    },
    {
      title: '本机地址',
      dataIndex: 'localAddress',
    },
    {
      title: '回话ID',
      dataIndex: 'sessionId',
    },
    {
      title: 'MTU',
      dataIndex: 'mtu',
    },
    {
      title: '使用人数',
      dataIndex: 'userCount',
    },
    {
      title: '闲置时间(秒)',
      dataIndex: 'idleTime',
    },
    {
      title: '接收字节',
      dataIndex: 'receivedBytes',
    },
    {
      title: '发送字节',
      dataIndex: 'sentBytes',
    },
    {
      title: '下行速率(kbits/s)',
      dataIndex: 'downloadSpeed',
    },
    {
      title: '上行速率(kbits/s)',
      dataIndex: 'uploadSpeed',
    },
  ];

  const selectedStatus = ref<string>('');
  const filteredDialingData = ref<Dialingdata[]>([]);

  const fetchDialingData = async (statusType = '') => {
    try {
      const params = new URLSearchParams({ act: 'oeclient' });
      if (statusType) {
        params.append('statusType', statusType);
      }

      const response = await axios.post('/lua/system_info.lua', params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      console.log('Response data:', response.data);

      if (response.data.code === 200) {
        const formattedData = [];
        response.data.data.forEach((item) => {
          const currentDialingData: Dialingdata = {
            key: item.session_id,
            dialUpUsername: item.account,
            status: item.status,
            localMac: item.local_mac,
            serverMac: item.server_mac,
            localAddress: item.local_addr,
            sessionId: item.session_id,
            mtu: item.mtu,
            userCount: item.use_num,
            idleTime: item.idle_timeout,
            receivedBytes: item.receive_byte,
            sentBytes: item.send_byte,
            downloadSpeed: item.downrate,
            uploadSpeed: item.uprate,
          };
          formattedData.push(currentDialingData);
        });

        dialingdata.value = formattedData;
        filteredDialingData.value = formattedData;
      } else {
        console.error('Failed to fetch data:', response.data.err);
        Message.error({
          content: response.data.err,
          duration: 5000,
        });
      }
    } catch (error) {
      console.error('Error fetching dialing info:', error);
      Message.error('获取拨号状态信息失败');
    }
  };

  const filterData = () => {
    if (!hasPermission('dialStatusFilter')) {
      Message.error('您没有权限');
      return;
    }
    fetchDialingData(selectedStatus.value);
    console.log('Filtering data by status:', selectedStatus.value);
  };

  const exportStatusAccount = async () => {
    if (!hasPermission('dialStatusExport')) {
      Message.error('您没有权限');
      return;
    }
    try {
      const params = new URLSearchParams({ act: 'oeclient' });
      if (selectedStatus.value) {
        params.append('statusType', selectedStatus.value);
      }

      console.log('导出请求参数:', params.toString());
      console.log('导出请求URL: /lua/download.lua');

      // 方法一：使用axios（与原来实现一致）
      try {
        const response = await axios.post('/lua/download.lua', params, {
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        });

        console.log('导出响应状态:', response.status);
        console.log('导出响应类型:', response.headers['content-type']);

        // 检查响应是否成功
        if (response.status === 200) {
          // 创建一个临时链接并下载文件
          const blob = new Blob([response.data], {
            type: response.headers['content-type'] || 'text/csv;charset=utf-8',
          });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', 'oeclient_status.csv');
          document.body.appendChild(link);
          link.click();
          link.parentNode?.removeChild(link);
          window.URL.revokeObjectURL(url);
          Message.success('文件导出成功');
        } else {
          Message.error('导出失败：响应状态码异常');
        }
      } catch (axiosError) {
        console.error('Axios导出错误:', axiosError);

        // 如果axios方法失败，尝试使用XMLHttpRequest方法
        console.log('尝试使用备用方法导出...');

        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/lua/download.lua', true);
        xhr.setRequestHeader(
          'Content-Type',
          'application/x-www-form-urlencoded'
        );
        xhr.responseType = 'arraybuffer';

        xhr.onload = function () {
          console.log('XHR响应状态:', xhr.status);
          console.log('XHR响应头:', xhr.getAllResponseHeaders());

          if (xhr.status === 200) {
            try {
              const blob = new Blob([xhr.response], {
                type: 'text/csv;charset=utf-8',
              });
              const url = window.URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.href = url;
              link.setAttribute('download', 'oeclient_status.csv');
              document.body.appendChild(link);
              link.click();
              link.parentNode?.removeChild(link);
              window.URL.revokeObjectURL(url);
              Message.success('文件导出成功');
            } catch (error) {
              console.error('处理XHR响应数据时出错:', error);
              Message.error('导出失败：响应数据处理错误');
            }
          } else {
            Message.error(`导出失败：服务器返回状态码 ${xhr.status}`);
          }
        };

        xhr.onerror = function () {
          console.error('XHR请求失败');
          Message.error('导出失败：网络错误');
        };

        xhr.send(params.toString());
      }
    } catch (error) {
      console.error('导出状态账号总错误:', error);
      Message.error('导出失败');
    }
  };

  onMounted(async () => {
    await fetchDialingData();
  });
</script>

<style scoped>
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }
  .dialing,
  .dialing2 {
    text-align: left;
    padding: 5px;
    background-color: #fff;
    white-space: nowrap;
  }
  .dialing2 {
    margin-top: 20px;
  }
  span {
    font-size: 20px;
  }
</style>
