<template>
  <a-card
    class="general-card"
    :title="$t('workplace.recently.visited')"
    :header-style="{ paddingBottom: '0' }"
    :body-style="{ paddingTop: '26px' }"
  >
    <div style="margin-bottom: -1rem">
      <a-row :gutter="8">
        <a-col
          v-for="link in recentLinks"
          :key="link.text"
          :span="8"
          class="wrapper"
          @click="navigateTo(link.route)"
          style="cursor: pointer"
        >
          <div class="icon">
            <component :is="link.icon" />
          </div>
          <a-typography-paragraph class="text">
            {{ $t(link.text) }}
          </a-typography-paragraph>
        </a-col>
      </a-row>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const recentLinks = ref([]);
  function updateRecentLinks(route: string) {
    const links = JSON.parse(localStorage.getItem('recentLinks') || '[]');
    if (!links.includes(route)) {
      links.unshift(route);
      if (links.length > 3) {
        links.pop();
      }
      localStorage.setItem('recentLinks', JSON.stringify(links));
    }
  }

  function getLinkInfo(route: string) {
    const linkMap = {
      '/systemstate/port': {
        text: 'menu.port',
        icon: 'icon-file',
        route: '/systemstate/port',
      },
      '/systemcontrol/system-control': {
        text: 'menu.system-control',
        icon: 'icon-link',
        route: '/systemcontrol/system-control',
      },
      '/systemconfiguration/access-port': {
        text: 'menu.access-port',
        icon: 'icon-share-alt',
        route: '/systemconfiguration/access-port',
      },
      '/Virtual_Machine_Settings/network_port': {
        text: 'menu.basic_settings',
        icon: 'icon-share-alt',
        route: '/Virtual_Machine_Settings/network_port',
      },
      '/Host_Setting/Network_settings': {
        text: 'menu.Network settings',
        icon: 'icon-settings',
        route: '/Host_Setting/Network_settings',
      },
      '/Operator_management/Operator_Configuration': {
        text: 'menu.Operator Configuration',
        icon: 'icon-user',
        route: '/Operator_management/Operator_Configuration',
      },
    };
    return linkMap[route] || { text: 'unknown', icon: 'icon-unknown' };
  }
  function navigateTo(route: string) {
    router.push(route);
    updateRecentLinks(route);
  }
  onMounted(() => {
    const links = JSON.parse(localStorage.getItem('recentLinks') || '[]');
    recentLinks.value = links.map((route) => {
      return getLinkInfo(route);
    });
  });
</script>

<style lang="less" scoped>
  :deep(.arco-card-header-title) {
    line-height: inherit;
  }
</style>
