<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="18">
        <a-space>
          <!-- 新建按钮 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            {{ $t('add') }}
          </a-button>

          <a-button type="primary" @click="handleEdit">
            <template #icon>
              <icon-edit />
            </template>
            {{ $t('edit') }}
          </a-button>

          <a-button @click="handleEditDesc">
            <template #icon>
              <icon-edit />
            </template>
            {{ $t('edic_desc') }}
          </a-button>

          <a-button @click="handleDelete">
            <template #icon>
              <icon-delete />
            </template>
            {{ $t('delete') }}
          </a-button>
        </a-space>
      </a-col>
      <a-col :span="6" style="text-align: right">
        <a-input-search
          v-model="searchText"
          :placeholder="$t('search_start_address')"
          style="width: 200px"
          @search="handleSearch"
          @clear="handleClearSearch"
          allow-clear
        />
      </a-col>
    </a-row>

    <!-- 添加表格组件 -->
    <a-table
      v-model:selectedKeys="selectedRowKeys"
      :columns="columns"
      :data="dataWithIndex"
      :row-selection="rowSelection"
      row-key="index"
      :loading="isRefreshing"
      :pagination="{ showTotal: true }"
    />
  </a-card>

  <!-- 添加地址池模态框 -->
  <a-modal
    v-model:visible="isModalVisible"
    :width="700"
    :title="$t('add_address_pool')"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-form :model="formData" :rules="rules" ref="formRef">
      <a-form-item :label="$t('start_address')" field="start_ip">
        <a-input
          v-model="formData.start_ip"
          style="width: 200px"
          :placeholder="$t('please_enter_start_address')"
        />
      </a-form-item>
      <a-form-item :label="$t('end_address')" field="end_ip">
        <a-input
          v-model="formData.end_ip"
          style="width: 200px"
          :placeholder="$t('please_enter_end_address')"
        />
      </a-form-item>
      <a-form-item :label="$t('network_mask')" field="mask">
        <a-input
          v-model="formData.mask"
          style="width: 200px"
          :placeholder="$t('please_enter_network_mask')"
        />
      </a-form-item>
      <a-form-item :label="$t('gateway_address')" field="route">
        <a-input
          v-model="formData.route"
          style="width: 200px"
          :placeholder="$t('please_enter_gateway_address')"
        />
      </a-form-item>
      <a-form-item :label="$t('dns_server_1')" field="dns1">
        <a-input
          v-model="formData.dns1"
          style="width: 200px"
          :placeholder="$t('please_enter_dns_server_1')"
        />
      </a-form-item>
      <a-form-item :label="$t('dns_server_2')" field="dns2">
        <a-input
          v-model="formData.dns2"
          style="width: 200px"
          :placeholder="$t('please_enter_dns_server_2')"
        />
      </a-form-item>
      <a-form-item :label="$t('time_seconds')" field="times">
        <a-input
          v-model="formData.times"
          style="width: 200px"
          :placeholder="$t('please_enter_time')"
        />
      </a-form-item>
      <a-form-item :label="$t('port')" field="port">
        <a-input
          v-model="formData.port"
          style="width: 200px"
          :placeholder="$t('please_enter_port')"
        />
      </a-form-item>
      <a-form-item :label="$t('vlan_id')" field="vlan">
        <a-input
          v-model="formData.vlan"
          style="width: 200px"
          :placeholder="$t('please_enter_vlan_id')"
        />
      </a-form-item>
      <a-form-item :label="$t('description')" field="desc">
        <a-input
          v-model="formData.desc"
          style="width: 200px"
          :placeholder="$t('please_enter_description')"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 编辑模态框 -->
  <a-modal
    v-model:visible="isEditModalVisible"
    :width="600"
    :title="$t('edit_address_pool')"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleEditBeforeOk"
    @cancel="handleEditCancel"
  >
    <a-form :model="editFormData" :rules="editRules" ref="editFormRef">
      <a-form-item :label="$t('start_address')" field="start_ip">
        <a-input
          v-model="editFormData.start_ip"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item :label="$t('end_address')" field="end_ip">
        <a-input
          v-model="editFormData.end_ip"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item :label="$t('network_mask')" field="mask">
        <a-input
          v-model="editFormData.mask"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item :label="$t('gateway_address')" field="route">
        <a-input
          v-model="editFormData.route"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item :label="$t('dns_server_1')" field="dns1">
        <a-input
          v-model="editFormData.dns1"
          style="width: 200px"
          :placeholder="$t('please_enter_dns_server_1')"
        />
      </a-form-item>
      <a-form-item :label="$t('dns_server_2')" field="dns2">
        <a-input
          v-model="editFormData.dns2"
          style="width: 200px"
          :placeholder="$t('please_enter_dns_server_2')"
        />
      </a-form-item>
      <a-form-item :label="$t('time_seconds')" field="times">
        <a-input
          v-model="editFormData.times"
          style="width: 200px"
          :placeholder="$t('please_enter_time')"
        />
      </a-form-item>
      <a-form-item :label="$t('port')" field="port">
        <a-input
          v-model="editFormData.port"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item :label="$t('vlan_id')" field="vlan">
        <a-input
          v-model="editFormData.vlan"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 编辑描述模态框 -->
  <a-modal
    v-model:visible="isDescModalVisible"
    :width="500"
    :title="$t('edit_description')"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleDescBeforeOk"
    @cancel="handleDescCancel"
  >
    <a-form :model="descFormData" ref="descFormRef">
      <a-form-item :label="$t('start_address')" field="start_ip">
        <a-input
          v-model="descFormData.start_ip"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item :label="$t('description')" field="desc">
        <a-textarea
          v-model="descFormData.desc"
          style="width: 300px"
          :placeholder="$t('please_enter_description')"
          :rows="4"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    watch,
    computed,
  } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidVlanId } from '@/utils/validate';
  import { isValidIPv4, isValidMask } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // 表格列定义
      const { t } = useI18n();
      const columns: TableColumnData[] = [
        {
          title: t('start_address'),
          dataIndex: 'start_ip',
          align: 'center',
          width: 120,
        },
        {
          title: t('end_address'),
          dataIndex: 'end_ip',
          align: 'center',
          width: 120,
        },
        {
          title: t('network_mask'),
          dataIndex: 'mask',
          align: 'center',
          width: 120,
        },
        {
          title: t('gateway_address'),
          dataIndex: 'route',
          align: 'center',
          width: 120,
        },
        {
          title: t('dns_server_1'),
          dataIndex: 'dns1',
          align: 'center',
          width: 120,
        },
        {
          title: t('dns_server_2'),
          dataIndex: 'dns2',
          align: 'center',
          width: 120,
        },
        {
          title: t('time_seconds'),
          dataIndex: 'times',
          align: 'center',
          width: 100,
        },
        {
          title: t('port'),
          dataIndex: 'port',
          align: 'center',
          width: 80,
        },
        {
          title: t('vlan_id'),
          dataIndex: 'vlan',
          align: 'center',
          width: 100,
        },
        {
          title: t('description'),
          dataIndex: 'desc',
          align: 'center',
          width: 120,
        },
      ];

      interface DhcpPoolData {
        route: string;
        dns2: string;
        dns1: string;
        mask: string;
        desc: string;
        end_ip: string;
        times: string;
        start_ip: string;
        vlan: string;
        port: string;
      }

      const data = ref<DhcpPoolData[]>([]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);
      const isEditModalVisible = ref(false);
      const isDescModalVisible = ref(false);

      // 搜索相关
      const searchText = ref('');
      const filteredData = ref<DhcpPoolData[]>([]);
      const isSearching = ref(false);

      // 为数据添加索引
      const dataWithIndex = computed(() => {
        const sourceData = isSearching.value ? filteredData.value : data.value;
        return sourceData.map((item, index) => ({
          ...item,
          index: index.toString(),
        }));
      });

      // 表格选择
      const selectedRowKeys = ref<string[]>([]);

      // 监听选中状态变化
      watch(
        selectedRowKeys,
        (newVal) => {
          console.log('selectedRowKeys 变化:', newVal);
        },
        { deep: true }
      );

      const rowSelection = {
        type: 'checkbox' as const,
        showCheckedAll: true,
      };

      // 表单数据
      const formData = reactive({
        route: '',
        dns2: '',
        dns1: '',
        mask: '',
        desc: '',
        end_ip: '',
        times: '',
        start_ip: '',
        vlan: '',
        port: '',
      });

      // 编辑表单数据
      const editFormData = reactive({
        route: '',
        dns2: '',
        dns1: '',
        mask: '',
        desc: '',
        end_ip: '',
        times: '',
        start_ip: '',
        vlan: '',
        port: '',
      });

      // 描述编辑表单数据
      const descFormData = reactive({
        start_ip: '',
        desc: '',
        originalRecord: null as DhcpPoolData | null,
      });

      // 表单规则
      const rules = {
        start_ip: [
          { required: true, message: t('start_address_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('ip_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        end_ip: [
          { required: true, message: t('end_address_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('ip_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        route: [
          { required: true, message: t('gateway_address_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('ip_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        dns1: [
          { required: true, message: t('dns_server_1_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('dns_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        dns2: [
          { required: true, message: t('dns_server_2_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('dns_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        mask: [
          { required: true, message: t('network_mask_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidMask(value)) {
                callback(t('mask_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        port: [{ required: true, message: t('port_required') }],
        vlan: [{ required: true, message: t('vlan_id_required') }],
        times: [{ required: true, message: t('time_required') }],
      };

      // 编辑表单规则
      const editRules = {
        dns1: [
          { required: true, message: t('dns_server_1_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('dns_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        dns2: [
          { required: true, message: t('dns_server_2_required') },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback(t('dns_format_error'));
              } else {
                callback();
              }
            },
          },
        ],
        times: [{ required: true, message: t('time_required') }],
      };

      const formRef = ref();
      const editFormRef = ref();
      const descFormRef = ref();

      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'dhcp_pool' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            data.value = response.data.data || [];
            console.log('获取到的数据:', data.value);
            if (data.value.length > 0) {
              console.log('第一条数据的start_ip:', data.value[0].start_ip);
            }
          } else {
            Message.error({
              content: response.data.err || t('get_data_failed'),
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error(t('get_data_failed'));
        }
      };

      // 删除选中项
      const handleDelete = () => {
        if (selectedRowKeys.value.length === 0) {
          Message.warning(t('please_select_items_to_delete'));
          return;
        }

        // 实际应调用删除API
        // 这里仅做前端删除
        data.value = data.value.filter(
          (item) => !selectedRowKeys.value.includes(item.start_ip)
        );
        selectedRowKeys.value = [];
        Message.success(t('delete_success'));
      };

      // 显示添加模态框
      const showModal = () => {
        // 重置表单
        formData.route = '';
        formData.dns2 = '';
        formData.dns1 = '';
        formData.mask = '';
        formData.desc = '';
        formData.end_ip = '';
        formData.times = '';
        formData.start_ip = '';
        formData.vlan = '';
        formData.port = '';
        isModalVisible.value = true;
      };

      // 处理编辑按钮点击
      const handleEdit = () => {
        console.log('选中的记录数量:', selectedRowKeys.value.length);
        console.log('选中的记录:', selectedRowKeys.value);

        if (selectedRowKeys.value.length !== 1) {
          Message.warning(t('please_select_one_record_to_edit'));
          return;
        }

        const selectedIndex = parseInt(selectedRowKeys.value[0], 10);
        const selectedRecord = data.value[selectedIndex];
        if (selectedRecord) {
          showEditModal(selectedRecord);
        }
      };

      // 处理编辑描述按钮点击
      const handleEditDesc = () => {
        if (selectedRowKeys.value.length !== 1) {
          Message.warning(t('please_select_one_record_to_edit'));
          return;
        }

        const selectedIndex = parseInt(selectedRowKeys.value[0], 10);
        const selectedRecord = data.value[selectedIndex];
        if (selectedRecord) {
          showDescModal(selectedRecord);
        }
      };

      // 显示编辑模态框
      const showEditModal = (record: DhcpPoolData) => {
        // 复制数据到编辑表单
        Object.assign(editFormData, record);
        isEditModalVisible.value = true;
      };

      // 显示描述编辑模态框
      const showDescModal = (record: DhcpPoolData) => {
        descFormData.start_ip = record.start_ip;
        descFormData.desc = record.desc;
        descFormData.originalRecord = record;
        isDescModalVisible.value = true;
      };

      // 处理添加确认
      const handleBeforeOk = (done: any) => {
        formRef.value.validate().then((errors: any) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 添加到列表
          data.value.push({
            route: formData.route,
            dns2: formData.dns2,
            dns1: formData.dns1,
            mask: formData.mask,
            desc: formData.desc,
            end_ip: formData.end_ip,
            times: formData.times,
            start_ip: formData.start_ip,
            vlan: formData.vlan,
            port: formData.port,
          });

          Message.success(t('add_success'));
          done(true); // 允许模态框关闭
        });
      };

      // 处理编辑确认
      const handleEditBeforeOk = async (done: any) => {
        try {
          const errors = await editFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          // 这里应该调用编辑API
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'dhcp_switch',
              action: 'edit',
              start_ip: editFormData.start_ip,
              dns1: editFormData.dns1,
              dns2: editFormData.dns2,
              times: editFormData.times,
            }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            Message.success(t('edit_success'));
            fetchData(); // 重新获取数据
            done(true);
          } else {
            Message.error(response.data.err || t('edit_failed'));
            done(false);
          }
        } catch (error) {
          console.error('编辑失败:', error);
          Message.error(t('edit_request_failed'));
          done(false);
        }
      };

      // 处理描述编辑确认
      const handleDescBeforeOk = async (done: any) => {
        try {
          // 这里应该调用编辑描述API
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'dhcp_switch',
              action: 'edit_desc',
              start_ip: descFormData.start_ip,
              desc: descFormData.desc,
            }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            Message.success(t('description_edit_success'));
            fetchData(); // 重新获取数据
            done(true);
          } else {
            Message.error(response.data.err || t('description_edit_failed'));
            done(false);
          }
        } catch (error) {
          console.error('描述编辑失败:', error);
          Message.error(t('description_edit_request_failed'));
          done(false);
        }
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      const handleEditCancel = () => {
        isEditModalVisible.value = false;
      };

      const handleDescCancel = () => {
        isDescModalVisible.value = false;
      };

      // 搜索方法
      const handleSearch = (value: string) => {
        const searchValue = value || searchText.value;
        if (searchValue.trim()) {
          filteredData.value = data.value.filter((item) =>
            item.start_ip.toLowerCase().includes(searchValue.toLowerCase())
          );
          isSearching.value = true;
        } else {
          filteredData.value = [];
          isSearching.value = false;
        }
      };

      // 清除搜索
      const handleClearSearch = () => {
        searchText.value = '';
        filteredData.value = [];
        isSearching.value = false;
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        dataWithIndex,
        isRefreshing,
        formData,
        editFormData,
        descFormData,
        rules,
        editRules,
        formRef,
        editFormRef,
        descFormRef,
        isModalVisible,
        isEditModalVisible,
        isDescModalVisible,
        showModal,
        handleEdit,
        handleEditDesc,
        showEditModal,
        showDescModal,
        handleBeforeOk,
        handleEditBeforeOk,
        handleDescBeforeOk,
        handleCancel,
        handleEditCancel,
        handleDescCancel,
        handleDelete,
        rowSelection,
        selectedRowKeys,
        // 搜索相关
        searchText,
        filteredData,
        isSearching,
        handleSearch,
        handleClearSearch,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
