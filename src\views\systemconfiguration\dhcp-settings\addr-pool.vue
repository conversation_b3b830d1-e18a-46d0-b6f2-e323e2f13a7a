<template>
  <a-card class="general-card">
    <a-divider style="margin-top: 0" />

    <a-row style="margin-bottom: 16px">
      <a-col :span="18">
        <a-space>
          <!-- 新建按钮 -->
          <a-button type="primary" @click="showModal">
            <template #icon>
              <icon-plus />
            </template>
            添加
          </a-button>

          <a-button type="primary" @click="handleEdit">
            <template #icon>
              <icon-edit />
            </template>
            编辑
          </a-button>

          <a-button @click="handleEditDesc">
            <template #icon>
              <icon-edit />
            </template>
            编辑描述
          </a-button>

          <a-button @click="handleDelete">
            <template #icon>
              <icon-delete />
            </template>
            删除
          </a-button>
        </a-space>
      </a-col>
      <a-col :span="6" style="text-align: right">
        <a-input-search
          v-model="searchText"
          placeholder="搜索开始地址"
          style="width: 200px"
          @search="handleSearch"
          @clear="handleClearSearch"
          allow-clear
        />
      </a-col>
    </a-row>

    <!-- 添加表格组件 -->
    <a-table
      v-model:selectedKeys="selectedRowKeys"
      :columns="columns"
      :data="dataWithIndex"
      :row-selection="rowSelection"
      row-key="index"
      :loading="isRefreshing"
      :pagination="{ showTotal: true }"
    />
  </a-card>

  <!-- 添加地址池模态框 -->
  <a-modal
    v-model:visible="isModalVisible"
    :width="700"
    title="添加地址池"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-form :model="formData" :rules="rules" ref="formRef">
      <a-form-item label="开始地址" field="start_ip">
        <a-input
          v-model="formData.start_ip"
          style="width: 200px"
          placeholder="请输入开始地址"
        />
      </a-form-item>
      <a-form-item label="结束地址" field="end_ip">
        <a-input
          v-model="formData.end_ip"
          style="width: 200px"
          placeholder="请输入结束地址"
        />
      </a-form-item>
      <a-form-item label="网络掩码" field="mask">
        <a-input
          v-model="formData.mask"
          style="width: 200px"
          placeholder="请输入网络掩码"
        />
      </a-form-item>
      <a-form-item label="网关地址" field="route">
        <a-input
          v-model="formData.route"
          style="width: 200px"
          placeholder="请输入网关地址"
        />
      </a-form-item>
      <a-form-item label="DNS服务器1" field="dns1">
        <a-input
          v-model="formData.dns1"
          style="width: 200px"
          placeholder="请输入DNS服务器1"
        />
      </a-form-item>
      <a-form-item label="DNS服务器2" field="dns2">
        <a-input
          v-model="formData.dns2"
          style="width: 200px"
          placeholder="请输入DNS服务器2"
        />
      </a-form-item>
      <a-form-item label="时间(秒)" field="times">
        <a-input
          v-model="formData.times"
          style="width: 200px"
          placeholder="请输入时间"
        />
      </a-form-item>
      <a-form-item label="端口" field="port">
        <a-input
          v-model="formData.port"
          style="width: 200px"
          placeholder="请输入端口"
        />
      </a-form-item>
      <a-form-item label="VLAN ID" field="vlan">
        <a-input
          v-model="formData.vlan"
          style="width: 200px"
          placeholder="请输入VLAN ID"
        />
      </a-form-item>
      <a-form-item label="描述" field="desc">
        <a-input
          v-model="formData.desc"
          style="width: 200px"
          placeholder="请输入描述"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 编辑模态框 -->
  <a-modal
    v-model:visible="isEditModalVisible"
    :width="600"
    title="编辑地址池"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleEditBeforeOk"
    @cancel="handleEditCancel"
  >
    <a-form :model="editFormData" :rules="editRules" ref="editFormRef">
      <a-form-item label="开始地址" field="start_ip">
        <a-input
          v-model="editFormData.start_ip"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item label="结束地址" field="end_ip">
        <a-input
          v-model="editFormData.end_ip"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item label="网络掩码" field="mask">
        <a-input
          v-model="editFormData.mask"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item label="网关地址" field="route">
        <a-input
          v-model="editFormData.route"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item label="DNS服务器1" field="dns1">
        <a-input
          v-model="editFormData.dns1"
          style="width: 200px"
          placeholder="请输入DNS服务器1"
        />
      </a-form-item>
      <a-form-item label="DNS服务器2" field="dns2">
        <a-input
          v-model="editFormData.dns2"
          style="width: 200px"
          placeholder="请输入DNS服务器2"
        />
      </a-form-item>
      <a-form-item label="时间(秒)" field="times">
        <a-input
          v-model="editFormData.times"
          style="width: 200px"
          placeholder="请输入时间"
        />
      </a-form-item>
      <a-form-item label="端口" field="port">
        <a-input
          v-model="editFormData.port"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item label="VLAN ID" field="vlan">
        <a-input
          v-model="editFormData.vlan"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 编辑描述模态框 -->
  <a-modal
    v-model:visible="isDescModalVisible"
    :width="500"
    title="编辑描述"
    draggable
    :mask-closable="false"
    :unmount-on-close="false"
    @before-ok="handleDescBeforeOk"
    @cancel="handleDescCancel"
  >
    <a-form :model="descFormData" ref="descFormRef">
      <a-form-item label="开始地址" field="start_ip">
        <a-input
          v-model="descFormData.start_ip"
          style="width: 200px"
          readonly
          disabled
        />
      </a-form-item>
      <a-form-item label="描述" field="desc">
        <a-textarea
          v-model="descFormData.desc"
          style="width: 300px"
          placeholder="请输入描述"
          :rows="4"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    ref,
    onMounted,
    watch,
    computed,
  } from 'vue';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import axios from 'axios';
  import { useUserStore } from '@/store';
  import usePermission from '@/hooks/permission';
  import { isValidVlanId } from '@/utils/validate';
  import { isValidIPv4, isValidMask } from '@/utils/validate';

  export default defineComponent({
    props: {
      active: Boolean,
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();

      // 表格列定义
      const columns: TableColumnData[] = [
        {
          title: '开始地址',
          dataIndex: 'start_ip',
          align: 'center',
          width: 120,
        },
        {
          title: '结束地址',
          dataIndex: 'end_ip',
          align: 'center',
          width: 120,
        },
        {
          title: '网络掩码',
          dataIndex: 'mask',
          align: 'center',
          width: 120,
        },
        {
          title: '网关地址',
          dataIndex: 'route',
          align: 'center',
          width: 120,
        },
        {
          title: 'dns服务器1',
          dataIndex: 'dns1',
          align: 'center',
          width: 120,
        },
        {
          title: 'dns服务器2',
          dataIndex: 'dns2',
          align: 'center',
          width: 120,
        },
        {
          title: '时间(秒)',
          dataIndex: 'times',
          align: 'center',
          width: 100,
        },
        {
          title: '端口',
          dataIndex: 'port',
          align: 'center',
          width: 80,
        },
        {
          title: 'vlanID',
          dataIndex: 'vlan',
          align: 'center',
          width: 100,
        },
        {
          title: '描述',
          dataIndex: 'desc',
          align: 'center',
          width: 120,
        },
      ];

      interface DhcpPoolData {
        route: string;
        dns2: string;
        dns1: string;
        mask: string;
        desc: string;
        end_ip: string;
        times: string;
        start_ip: string;
        vlan: string;
        port: string;
      }

      const data = ref<DhcpPoolData[]>([]);
      const isRefreshing = ref(false);
      const isModalVisible = ref(false);
      const isEditModalVisible = ref(false);
      const isDescModalVisible = ref(false);

      // 搜索相关
      const searchText = ref('');
      const filteredData = ref<DhcpPoolData[]>([]);
      const isSearching = ref(false);

      // 为数据添加索引
      const dataWithIndex = computed(() => {
        const sourceData = isSearching.value ? filteredData.value : data.value;
        return sourceData.map((item, index) => ({
          ...item,
          index: index.toString(),
        }));
      });

      // 表格选择
      const selectedRowKeys = ref<string[]>([]);

      // 监听选中状态变化
      watch(
        selectedRowKeys,
        (newVal) => {
          console.log('selectedRowKeys 变化:', newVal);
        },
        { deep: true }
      );

      const rowSelection = {
        type: 'checkbox' as const,
        showCheckedAll: true,
      };

      // 表单数据
      const formData = reactive({
        route: '',
        dns2: '',
        dns1: '',
        mask: '',
        desc: '',
        end_ip: '',
        times: '',
        start_ip: '',
        vlan: '',
        port: '',
      });

      // 编辑表单数据
      const editFormData = reactive({
        route: '',
        dns2: '',
        dns1: '',
        mask: '',
        desc: '',
        end_ip: '',
        times: '',
        start_ip: '',
        vlan: '',
        port: '',
      });

      // 描述编辑表单数据
      const descFormData = reactive({
        start_ip: '',
        desc: '',
        originalRecord: null as DhcpPoolData | null,
      });

      // 表单规则
      const rules = {
        start_ip: [
          { required: true, message: '开始地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        end_ip: [
          { required: true, message: '结束地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        route: [
          { required: true, message: '网关地址不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('IP地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        dns1: [
          { required: true, message: 'DNS服务器1不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('DNS地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        dns2: [
          { required: true, message: 'DNS服务器2不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('DNS地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        mask: [
          { required: true, message: '网络掩码不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidMask(value)) {
                callback('子网掩码格式不正确，应为：*************');
              } else {
                callback();
              }
            },
          },
        ],
        port: [{ required: true, message: '端口不能为空' }],
        vlan: [{ required: true, message: 'VLAN ID不能为空' }],
        times: [{ required: true, message: '时间不能为空' }],
      };

      // 编辑表单规则
      const editRules = {
        dns1: [
          { required: true, message: 'DNS服务器1不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('DNS地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        dns2: [
          { required: true, message: 'DNS服务器2不能为空' },
          {
            validator: (value: string, callback: (error?: string) => void) => {
              if (!value || !isValidIPv4(value)) {
                callback('DNS地址格式不正确，应为：*******');
              } else {
                callback();
              }
            },
          },
        ],
        times: [{ required: true, message: '时间不能为空' }],
      };

      const formRef = ref();
      const editFormRef = ref();
      const descFormRef = ref();

      // 获取数据
      const fetchData = async () => {
        try {
          const response = await axios.post(
            '/lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({ act: 'dhcp_pool' }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            data.value = response.data.data || [];
            console.log('获取到的数据:', data.value);
            if (data.value.length > 0) {
              console.log('第一条数据的start_ip:', data.value[0].start_ip);
            }
          } else {
            Message.error({
              content: response.data.err || '获取数据失败',
              duration: 5000,
            });
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Message.error('获取数据失败');
        }
      };

      // 删除选中项
      const handleDelete = () => {
        if (selectedRowKeys.value.length === 0) {
          Message.warning('请选择要删除的项');
          return;
        }

        // 实际应调用删除API
        // 这里仅做前端删除
        data.value = data.value.filter(
          (item) => !selectedRowKeys.value.includes(item.start_ip)
        );
        selectedRowKeys.value = [];
        Message.success('删除成功');
      };

      // 显示添加模态框
      const showModal = () => {
        // 重置表单
        formData.route = '';
        formData.dns2 = '';
        formData.dns1 = '';
        formData.mask = '';
        formData.desc = '';
        formData.end_ip = '';
        formData.times = '';
        formData.start_ip = '';
        formData.vlan = '';
        formData.port = '';
        isModalVisible.value = true;
      };

      // 处理编辑按钮点击
      const handleEdit = () => {
        console.log('选中的记录数量:', selectedRowKeys.value.length);
        console.log('选中的记录:', selectedRowKeys.value);

        if (selectedRowKeys.value.length !== 1) {
          Message.warning('请选择一条记录进行编辑');
          return;
        }

        const selectedIndex = parseInt(selectedRowKeys.value[0], 10);
        const selectedRecord = data.value[selectedIndex];
        if (selectedRecord) {
          showEditModal(selectedRecord);
        }
      };

      // 处理编辑描述按钮点击
      const handleEditDesc = () => {
        if (selectedRowKeys.value.length !== 1) {
          Message.warning('请选择一条记录进行编辑');
          return;
        }

        const selectedIndex = parseInt(selectedRowKeys.value[0], 10);
        const selectedRecord = data.value[selectedIndex];
        if (selectedRecord) {
          showDescModal(selectedRecord);
        }
      };

      // 显示编辑模态框
      const showEditModal = (record: DhcpPoolData) => {
        // 复制数据到编辑表单
        Object.assign(editFormData, record);
        isEditModalVisible.value = true;
      };

      // 显示描述编辑模态框
      const showDescModal = (record: DhcpPoolData) => {
        descFormData.start_ip = record.start_ip;
        descFormData.desc = record.desc;
        descFormData.originalRecord = record;
        isDescModalVisible.value = true;
      };

      // 处理添加确认
      const handleBeforeOk = (done: any) => {
        formRef.value.validate().then((errors: any) => {
          if (errors) {
            // 表单验证失败
            done(false); // 阻止模态框关闭
            return;
          }

          // 添加到列表
          data.value.push({
            route: formData.route,
            dns2: formData.dns2,
            dns1: formData.dns1,
            mask: formData.mask,
            desc: formData.desc,
            end_ip: formData.end_ip,
            times: formData.times,
            start_ip: formData.start_ip,
            vlan: formData.vlan,
            port: formData.port,
          });

          Message.success('添加成功');
          done(true); // 允许模态框关闭
        });
      };

      // 处理编辑确认
      const handleEditBeforeOk = async (done: any) => {
        try {
          const errors = await editFormRef.value.validate();
          if (errors) {
            done(false);
            return;
          }

          // 这里应该调用编辑API
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'dhcp_switch',
              action: 'edit',
              start_ip: editFormData.start_ip,
              dns1: editFormData.dns1,
              dns2: editFormData.dns2,
              times: editFormData.times,
            }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            Message.success('编辑成功');
            fetchData(); // 重新获取数据
            done(true);
          } else {
            Message.error(response.data.err || '编辑失败');
            done(false);
          }
        } catch (error) {
          console.error('编辑失败:', error);
          Message.error('编辑请求失败');
          done(false);
        }
      };

      // 处理描述编辑确认
      const handleDescBeforeOk = async (done: any) => {
        try {
          // 这里应该调用编辑描述API
          const response = await axios.post(
            'lua/get_cfg_dhcp_server.lua',
            new URLSearchParams({
              act: 'dhcp_switch',
              action: 'edit_desc',
              start_ip: descFormData.start_ip,
              desc: descFormData.desc,
            }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
          );

          if (response.data.code === 200) {
            Message.success('描述编辑成功');
            fetchData(); // 重新获取数据
            done(true);
          } else {
            Message.error(response.data.err || '描述编辑失败');
            done(false);
          }
        } catch (error) {
          console.error('描述编辑失败:', error);
          Message.error('描述编辑请求失败');
          done(false);
        }
      };

      // 处理取消
      const handleCancel = () => {
        isModalVisible.value = false;
      };

      const handleEditCancel = () => {
        isEditModalVisible.value = false;
      };

      const handleDescCancel = () => {
        isDescModalVisible.value = false;
      };

      // 搜索方法
      const handleSearch = (value: string) => {
        const searchValue = value || searchText.value;
        if (searchValue.trim()) {
          filteredData.value = data.value.filter((item) =>
            item.start_ip.toLowerCase().includes(searchValue.toLowerCase())
          );
          isSearching.value = true;
        } else {
          filteredData.value = [];
          isSearching.value = false;
        }
      };

      // 清除搜索
      const handleClearSearch = () => {
        searchText.value = '';
        filteredData.value = [];
        isSearching.value = false;
      };

      watch(
        () => props.active,
        (newVal) => {
          if (newVal) {
            fetchData();
          }
        }
      );

      onMounted(() => {
        if (props.active) {
          fetchData();
        }
      });

      return {
        columns,
        data,
        dataWithIndex,
        isRefreshing,
        formData,
        editFormData,
        descFormData,
        rules,
        editRules,
        formRef,
        editFormRef,
        descFormRef,
        isModalVisible,
        isEditModalVisible,
        isDescModalVisible,
        showModal,
        handleEdit,
        handleEditDesc,
        showEditModal,
        showDescModal,
        handleBeforeOk,
        handleEditBeforeOk,
        handleDescBeforeOk,
        handleCancel,
        handleEditCancel,
        handleDescCancel,
        handleDelete,
        rowSelection,
        selectedRowKeys,
        // 搜索相关
        searchText,
        filteredData,
        isSearching,
        handleSearch,
        handleClearSearch,
        hasPermission,
      };
    },
  });
</script>

<style scoped>
  .general-card {
    width: 100%;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
  }

  .action-icon:hover {
    background-color: var(--color-fill-2);
  }
</style>
